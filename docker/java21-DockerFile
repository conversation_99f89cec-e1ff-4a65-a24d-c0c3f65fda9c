FROM harbor.dlab.cn/public/openjdk:21-alphine
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' >/etc/timezone
MAINTAINER DestinyShen "<EMAIL>"

ADD ./target/server.jar /app/server.jar
ADD https://cdn.daddylab.com/Public/lib/apache-skywalking-java-agent-8.10.0.tgz /app/

# RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apk/repositories
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
RUN tar -xvzf /app/apache-skywalking-java-agent-8.10.0.tgz -C /app
#暴露端口
EXPOSE 8080

#最终运行docker的命令
WORKDIR /app
ENTRYPOINT ["/sbin/tini", "--"]
CMD ["java","-jar","server.jar"]
