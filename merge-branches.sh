#!/bin/bash

# 检查输入参数
if [ "$#" -ne 2 ]; then
  echo "Usage: $0 <target-branch> <commit-message>"
  exit 1
fi

# 获取输入参数
TARGET_BRANCH=$1
COMMIT_MESSAGE=$2

# 确保工作目录是一个Git仓库
if [ ! -d ".git" ]; then
  echo "Error: This is not a Git repository."
  exit 1
fi

# 获取当前分支
SOURCE_BRANCH=$(git branch --show-current)
if [ -z "$SOURCE_BRANCH" ]; then
  echo "Error: Failed to get the current branch."
  exit 1
fi

# 暂存未暂存的更改
git add -A
git commit -m "$COMMIT_MESSAGE"
if [ $? -ne 0 ]; then
  echo "No changes to commit."
fi

# 拉取源分支的最新代码
git pull origin $SOURCE_BRANCH --rebase
if [ $? -ne 0 ]; then
  echo "Error: Failed to pull latest code for source branch $SOURCE_BRANCH."
  exit 1
fi

# 推送源分支的更改
git push origin $SOURCE_BRANCH
if [ $? -ne 0 ]; then
  echo "Error: Failed to push changes to source branch $SOURCE_BRANCH."
  exit 1
fi

# 切换到目标分支
git checkout $TARGET_BRANCH
if [ $? -ne 0 ]; then
  echo "Error: Failed to checkout target branch $TARGET_BRANCH."
  exit 1
fi

# 拉取目标分支的最新代码
git pull origin $TARGET_BRANCH
if [ $? -ne 0 ]; then
  echo "Error: Failed to pull latest code for target branch $TARGET_BRANCH."
  exit 1
fi

# 合并源分支到目标分支
git merge $SOURCE_BRANCH -m "$COMMIT_MESSAGE"
if [ $? -ne 0 ]; then
  echo "Error: Merge conflict detected. Resolve conflicts and commit the changes."
  exit 1
fi

# 推送合并后的代码到远程仓库
git push origin $TARGET_BRANCH
if [ $? -ne 0 ]; then
  echo "Error: Failed to push merged code to remote repository."
  exit 1
fi

# 切换回源分支
git checkout $SOURCE_BRANCH
if [ $? -ne 0 ]; then
  echo "Error: Failed to checkout back to source branch $SOURCE_BRANCH."
  exit 1
fi

echo "Successfully merged $SOURCE_BRANCH into $TARGET_BRANCH and pushed to remote repository."
