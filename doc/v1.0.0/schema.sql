CREATE TABLE `red_book`
(
    `id`                 bigint(20)    NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `created_at`         bigint(20)    NOT NULL DEFAULT '0' COMMENT '创建时间',
    `created_uid`        bigint(20)    NOT NULL DEFAULT '0' COMMENT '创建人',
    `updated_at`         bigint(20)    NOT NULL DEFAULT '0' COMMENT '更新时间',
    `updated_uid`        bigint(20)    NOT NULL DEFAULT '0' COMMENT '更新人',
    `is_del`             bigint(20)    NOT NULL DEFAULT '0' COMMENT '是否已删除',
    `deleted_at`         bigint(20)    NOT NULL DEFAULT '0' COMMENT '删除时间',
    `account_id`         varchar(64)   NOT NULL DEFAULT '' COMMENT '账号',
    `account_encrypt_id` varchar(64)   NOT NULL DEFAULT '' COMMENT '账号加密ID',
    `note_id`            varchar(64)   NOT NULL DEFAULT '' COMMENT '小红书笔记ID',
    `account_name`       varchar(64)   NOT NULL DEFAULT '' COMMENT '账号名称',
    `title`              varchar(1000) NOT NULL DEFAULT '' COMMENT '标题',
    `content`            text COMMENT '笔记内容',
    `type`               tinyint(4)    NOT NULL DEFAULT '0' COMMENT '笔记类型 1-图文 2-视频',
    `cate_type`          tinyint(4)    NOT NULL DEFAULT '1' COMMENT '笔记种类 1-账号全部笔记 2-关键词热点数据',
    `url`                varchar(2048) NOT NULL DEFAULT '' COMMENT '详情链接',
    `tags`               varchar(2048) NOT NULL DEFAULT '' COMMENT '标签',
    `images`             text COMMENT '图片json,ps:["1.png", "2.jpg"]',
    `videos`             text COMMENT '视频链接json,ps:["1.mp4", "2.mp4"]',
    `is_parser`          tinyint(4)    NOT NULL DEFAULT '0' COMMENT '是否解析',
    `source_data`        text COMMENT '原内容',
    `upvote_num`         varchar(32)   NOT NULL DEFAULT '' COMMENT '点赞数量',
    `collect_num`        varchar(32)   NOT NULL DEFAULT '' COMMENT '收藏数量',
    `discuss_num`        varchar(32)   NOT NULL DEFAULT '' COMMENT '评论数量',
    `last_update_time`   bigint(20)    NOT NULL DEFAULT '0' COMMENT '最后更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_url` (`url`(128))
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='小红书笔记'


CREATE TABLE `redbook_hotspot`
(
    `id`           bigint(20)   NOT NULL AUTO_INCREMENT COMMENT 'id',
    `rank`         tinyint(4)   NOT NULL DEFAULT '0' COMMENT '榜单类型，1行业热度榜 2热词飙升榜 3热词总量榜',
    `gap`          tinyint(4)   NOT NULL DEFAULT '0' COMMENT '榜单周期,0日榜 1周榜 2月榜',
    `start_time`   bigint(20)   NOT NULL DEFAULT '0' COMMENT '榜单起始时间戳',
    `end_time`     bigint(20)   NOT NULL DEFAULT '0' COMMENT '榜单结束时间戳',
    `cate`         varchar(100) NOT NULL DEFAULT '' COMMENT '分类',
    `hotword`      varchar(100) NOT NULL DEFAULT '' COMMENT '热词',
    `hot_val`      bigint(20)   NOT NULL DEFAULT '0' COMMENT '热度指数',
    `note_val`     bigint(20)   NOT NULL DEFAULT '0' COMMENT '关联笔记数',
    `interact_val` bigint(20)   NOT NULL DEFAULT '0' COMMENT '关联笔记互动量',
    `note_new_val` bigint(20)   NOT NULL DEFAULT '0' COMMENT '新增笔记数',
    `tag_rate`     varchar(500) NOT NULL DEFAULT '' COMMENT '关联分类',
    `hot_attr`     tinyint(4)   NOT NULL DEFAULT '0' COMMENT '热点属性,0无 1搜索飙升',
    `created_at`   bigint(20)   NOT NULL DEFAULT '0' COMMENT '创建日期',
    `created_uid`  bigint(20)   NOT NULL DEFAULT '0' COMMENT '创建人',
    `updated_at`   bigint(20)   NOT NULL DEFAULT '0' COMMENT '更新日期',
    `updated_uid`  bigint(20)   NOT NULL DEFAULT '0' COMMENT '更新人',
    `is_del`       bigint(20)   NOT NULL DEFAULT '0' COMMENT '是否已删除',
    `deleted_at`   bigint(20)   NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `redbook_hotspot_rank_IDX` (`rank`) USING BTREE,
    KEY `redbook_hotspot_gap_IDX` (`gap`, `start_time`, `end_time`) USING BTREE,
    KEY `redbook_hotspot_cate_IDX` (`cate`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='小红书热点';


create table prompt_template
(
    id          bigint auto_increment comment '主键ID'
        primary key,
    created_at  bigint default 0 not null comment '创建时间',
    created_uid bigint default 0 not null comment '创建人',
    updated_at  bigint default 0 not null comment '更新时间',
    updated_uid bigint default 0 not null comment '更新人',
    is_del      bigint default 0 not null comment '是否已删除',
    deleted_at  bigint default 0 not null comment '删除时间',
    title       varchar(32)      not null default '' comment '提示词标题',
    type        smallint         not null default 0 comment '模板类型 1-优化提示词 2-构建虚拟人物 3-构建场景 4-构建标题 5-构建内容 6-提炼记忆体',
    content     text comment '模版内容(markdown)',
) comment '提示词模版' charset = utf8mb4;


create table prompt
(
    id                 bigint auto_increment comment '主键ID'
        primary key,
    created_at         bigint default 0 not null comment '创建时间',
    created_uid        bigint default 0 not null comment '创建人',
    updated_at         bigint default 0 not null comment '更新时间',
    updated_uid        bigint default 0 not null comment '更新人',
    is_del             bigint default 0 not null comment '是否已删除',
    deleted_at         bigint default 0 not null comment '删除时间',
    prompt_template_id bigint           not null default 0 comment '模版ID',
    title              varchar(32)      not null default '' comment '提示词标题',
    type               smallint         not null default 0 comment '提示词类型 1-优化提示词 2-构建虚拟人物 3-构建场景 4-构建标题 5-构建内容 6-提炼记忆体 7-笔记标签',
    active             tinyint          not null default 0 comment '是否使用',
    content            text comment '提示词内容(markdown)',
    remark             text comment '备注'
) comment '提示词' charset = utf8mb4;


create table virtual_person
(
    id             bigint auto_increment comment '主键ID'
        primary key,
    created_at     bigint                default 0 not null comment '创建时间',
    created_uid    bigint                default 0 not null comment '创建人',
    updated_at     bigint                default 0 not null comment '更新时间',
    updated_uid    bigint                default 0 not null comment '更新人',
    is_del         bigint                default 0 not null comment '是否已删除',
    deleted_at     bigint                default 0 not null comment '删除时间',
    prompt_id      bigint                default 0 not null comment '提示词ID',
    prompt_content text comment '提示词内容',
    name           varchar(64)  not null default '' comment '虚拟人物名称',
    avatar         varchar(256) not null default '' comment '头像',
    sex            tinyint      not null default 0 comment '性别 0-未知 1-男 2-女',
    age            int          not null default 0 comment '年龄',
    province       varchar(32)  not null default '' comment '省',
    city           varchar(32)  not null default '' comment '市',
    birthday       varchar(32)  not null default '' comment '生日',
    tags           text comment '人物标签',
    status         tinyint      not null default 0 comment '场景状态 0-待生成 1-生成中 2-生成完成',
    model          varchar(128) not null default '' comment '使用的模型',
    appearance     varchar(512) not null default '' comment '外貌特征',
    character      varchar(512) not null default '' comment '性格特征',
    background     varchar(512) not null default '' comment '背景故事',
    experience     varchar(512) not null default '' comment '成就和经历',
    relation       varchar(512) not null default '' comment '人际关系',
    values         varchar(512) not null default '' comment '价值观',
    hobby          varchar(512) not null default '' comment '兴趣爱好',
    future         varchar(512) not null default '' comment '未来展望',
    content        text comment '生成人物描述'
) comment '虚拟人物' charset = utf8mb4;

create table scene
(
    id                bigint auto_increment comment '主键ID'
        primary key,
    created_at        bigint default 0 not null comment '创建时间',
    created_uid       bigint default 0 not null comment '创建人',
    updated_at        bigint default 0 not null comment '更新时间',
    updated_uid       bigint default 0 not null comment '更新人',
    is_del            bigint default 0 not null comment '是否已删除',
    deleted_at        bigint default 0 not null comment '删除时间',
    virtual_person_id bigint default 0 not null comment '虚拟人物ID',
    prompt_id         bigint default 0 not null comment '提示词ID',
    prompt_content    text comment '提示词内容',
    title             varchar(1024)    not null default '' comment '场景标题',
    tags              text comment '场景标签',
    status            tinyint          not null default 0 comment '场景状态 0-待生成 1-生成中 2-生成完成',
    model             varchar(128)     not null default '' comment '使用的模型',
    content           text comment '生成场景内容',
    key idx_virtual_person_id (virtual_person_id)
) comment '场景' charset = utf8mb4;



create table note
(
    id                     bigint auto_increment comment '主键ID'
        primary key,
    created_at             bigint default 0 not null comment '创建时间',
    created_uid            bigint default 0 not null comment '创建人',
    updated_at             bigint default 0 not null comment '更新时间',
    updated_uid            bigint default 0 not null comment '更新人',
    is_del                 bigint default 0 not null comment '是否已删除',
    deleted_at             bigint default 0 not null comment '删除时间',
    virtual_person_id      bigint default 0 not null comment '虚拟人物ID',
    scene_id               bigint default 0 not null comment '场景ID',
    title_prompt_id        bigint default 0 not null comment '标题使用的提示词',
    content_prompt_id      bigint default 0 not null comment '内容使用的提示词',
    tag_prompt_id          bigint default 0 not null comment '标签使用的提示词',
    title_prompt_content   text comment '标题使用的提示词内容',
    content_prompt_content text comment '标题使用的提示词内容',
    tag_prompt_content     text comment '标签使用的提示词内容',
    title                  varchar(512)     not null default '' comment '生成的标题',
    content                text comment '生成的内容',
    tag                    varchar(512)     not null default '' comment '生成的标签',
    status                 tinyint          not null default 0 comment '笔记状态 0-待生成 1-生成中 2-生成完成',
    model                  varchar(128)     not null default '' comment '使用的模型',
    final_content          text comment '最终的要发表的笔记',
    index idx_virtual_person_id (virtual_person_id),
    index idx_scene_id (scene_id)
) comment '笔记' charset = utf8mb4;


create table character_characteristics_info
(
    id              bigint auto_increment comment '主键ID'
        primary key,
    created_at      bigint      default 0  not null comment '创建时间',
    created_uid     bigint      default 0  not null comment '创建人',
    updated_at      bigint      default 0  not null comment '更新时间',
    updated_uid     bigint      default 0  not null comment '更新人',
    is_del          bigint      default 0  not null comment '是否已删除',
    deleted_at      bigint      default 0  not null comment '删除时间',
    field           varchar(50) default '' not null comment '领域',
    characteristics text                   null comment '特征'

) comment '人物领域特征库' charset = utf8mb4;


create table red_book_note_ranking
(
    id                bigint auto_increment comment '主键ID'
        primary key,
    created_at        bigint  default 0 not null comment '创建时间',
    created_uid       bigint  default 0 not null comment '创建人',
    updated_at        bigint  default 0 not null comment '更新时间',
    updated_uid       bigint  default 0 not null comment '更新人',
    is_del            bigint  default 0 not null comment '是否已删除',
    deleted_at        bigint  default 0 not null comment '删除时间',
    ranking_type      tinyint           not null comment '排行类型。0：暴增笔记排行榜。1：商业笔记排行，2：低粉爆款排行',
    note_field        tinyint default 0 not null comment '笔记分类。0:家居家装',
    statics_time_type tinyint           not null comment '统计时间类型。0日榜。1周榜。2月榜。3近24小时。4近3天。5近7天。',
    statics_time_val  varchar(50)       not null comment '统计时间',
    date_type         tinyint default 1 not null comment '1：单日榜单。2：7日榜单。只对排行类型，00类型有效',
    source_data       text              null comment '笔记内容',
    source_type       tinyint default 0 not null comment '数据来源。0:新红数据'

) comment '小红书笔记排行' charset = utf8mb4;


create table person_grow_diary
(
    id            bigint auto_increment comment '主键ID'
        primary key,
    created_at    bigint default 0 not null comment '创建时间',
    created_uid   bigint default 0 not null comment '创建人',
    updated_at    bigint default 0 not null comment '更新时间',
    updated_uid   bigint default 0 not null comment '更新人',
    is_del        bigint default 0 not null comment '是否已删除',
    deleted_at    bigint default 0 not null comment '删除时间',
    person_uuid   varchar(50)      not null comment '人物ID，唯一键',
    diary_content text             null comment '日记内容'

) comment '人物成长日记' charset = utf8mb4;


create table llm_system_user
(
    id          bigint auto_increment comment '主键ID'
        primary key,
    created_at  bigint default 0 not null comment '创建时间',
    created_uid bigint default 0 not null comment '创建人',
    updated_at  bigint default 0 not null comment '更新时间',
    updated_uid bigint default 0 not null comment '更新人',
    is_del      bigint default 0 not null comment '是否已删除',
    deleted_at  bigint default 0 not null comment '删除时间',
    biz_key     varchar(50)      not null comment '业务编码',
    system_msg  text             null comment '系统用户定义'

) comment '大模型系统用户定义' charset = utf8mb4;


create table personalized_promotion
(
    id            bigint auto_increment comment '主键ID'
        primary key,
    created_at    bigint default 0 not null comment '创建时间',
    created_uid   bigint default 0 not null comment '创建人',
    updated_at    bigint default 0 not null comment '更新时间',
    updated_uid   bigint default 0 not null comment '更新人',
    is_del        bigint default 0 not null comment '是否已删除',
    deleted_at    bigint default 0 not null comment '删除时间',
    person_uuid   varchar(50)      not null comment '人物ID，唯一键',
    diary_content text             null comment '输出内容'

) comment '人物学习和个性化提升策略' charset = utf8mb4;


-- auto-generated definition
create table person_detail_snapshot
(
    id                 bigint unsigned auto_increment comment '主键ID'
        primary key,
    person_id          bigint                    not null comment '用户id',
    person_uuid        varchar(36)               not null comment '用户uuid',
    content            text                      null comment '人物档案',
    update_reason      text                      null comment '人物更新理由',
    attribute_analysis text                      null comment '人物属性分析',
    created_at         bigint unsigned default 0 null comment '创建时间戳 (秒)',
    created_uid        bigint          default 0 not null comment '创建人',
    updated_at         bigint unsigned default 0 null comment '更新时间戳 (秒)',
    updated_uid        bigint          default 0 not null comment '更新人',
    deleted_at         bigint unsigned default 0 null comment '逻辑删除时间戳 (秒), 0表示未删除',
    is_del             bigint(20)                NOT NULL DEFAULT '0' COMMENT '是否已删除'
)
    comment '虚拟人详情快照';


CREATE TABLE `knowledge_base_material`
(
    `id`                                bigint(20)          NOT NULL AUTO_INCREMENT COMMENT 'id',
    `material_uuid`                     varchar(36)         NOT NULL COMMENT '素材全局唯一UUID',
    `material_name`                     varchar(100)        NOT NULL DEFAULT '' COMMENT '素材名称(运营用，空为文件名)',
    `material_type`                     varchar(20)         NOT NULL DEFAULT '' COMMENT '素材类型,200字内逗号相隔,比如:文章,白皮书',
    `description`                       text COMMENT '描述（运营用）',
    `original_file_url`                 varchar(512)        NOT NULL COMMENT '原始文件存储URL',
    `original_content`                  longtext            NOT NULL COMMENT '原始文件富文本',
    `parsed_content`                    longtext COMMENT '解析后的完整文本内容',
    `content_chunks_json`               longtext COMMENT '结构化切片数据 (格式示例见下方)',
    `parsing_status`                    tinyint(4)          NOT NULL DEFAULT '0' COMMENT '解析状态 0-待处理 1-成功 2-失败',
    `content_category_tags_json`        text COMMENT '内容分类标签 (JSON数组: ["技术文档","营销策略"])',
    `target_application_scenarios_json` text COMMENT '适用场景 (JSON数组: ["推广","知识分享"])',
    `keywords_json`                     text COMMENT '关键词集合 (JSON数组: ["SEO优化","用户体验"])',
    `core_information_points_json`      text COMMENT '核心信息要点 (JSON数组: ["5G技术优势","成本降低20%"])',
    `reference_urls_json`               text COMMENT '参考文献/数据来源链接 (JSON数组)',
    `usage_guidelines`                  text COMMENT '使用说明 (如适用于技术类内容创作)',
    `usage_restrictions`                text COMMENT '使用限制（如不能用于冬天）',
    `contain_errors`                    text COMMENT '可能的错误',
    `related_questions_json`            text COMMENT '需要提问的问题(JSON数组)',
    `is_enabled`                        tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '启用状态 1-启用 0-归档',
    `chat_id`                           bigint(20)          NOT NULL DEFAULT '0' COMMENT '生成的会话ID',
    `created_at`                        bigint(20)          NOT NULL DEFAULT '0' COMMENT '创建时间',
    `created_uid`                       bigint(20)          NOT NULL DEFAULT '0' COMMENT '创建人',
    `updated_at`                        bigint(20)          NOT NULL DEFAULT '0' COMMENT '更新时间',
    `updated_uid`                       bigint(20)          NOT NULL DEFAULT '0' COMMENT '更新人',
    `is_del`                            bigint(20)          NOT NULL DEFAULT '0' COMMENT '是否已删除',
    `deleted_at`                        bigint(20)          NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_material_uuid` (`material_uuid`),
    KEY `idx_material_version_scenario` (`chat_id`, `is_enabled`)
) COMMENT ='知识库素材表';

CREATE TABLE `activity_material`
(
    `id`                                bigint(20)          NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `material_uuid`                     varchar(36)         NOT NULL COMMENT '素材全局唯一UUID',
    `material_name`                     varchar(100)        NOT NULL DEFAULT '' COMMENT '素材名称',
    `material_type`                     varchar(200)        NOT NULL DEFAULT '' COMMENT '素材类型,200字内逗号相隔,比如:电商促销,线下活动',
    `support_policy`                    text COMMENT '活动扶持政策（含补贴规则/激励措施）',
    `start_time`                        bigint(20)          NOT NULL DEFAULT 0 COMMENT '活动开始时间（时间戳）',
    `end_time`                          bigint(20)          NOT NULL DEFAULT 0 COMMENT '活动结束时间（时间戳）',
    `original_file_url`                 varchar(512)        NOT NULL COMMENT '原始文件存储URL',
    `original_content`                  longtext            NOT NULL COMMENT '原始文件富文本',
    `parsed_content`                    longtext COMMENT '解析后的完整文本内容',
    `content_chunks_json`               longtext COMMENT '结构化切片数据 (格式示例见下方)',
    `parsing_status`                    tinyint(4)          NOT NULL DEFAULT 0 COMMENT '解析状态 0-待处理 1-成功 2-失败',
    `target_application_scenarios_json` text COMMENT '适用场景 ["直播带货","门店运营"]',
    `keywords_json`                     text COMMENT '关键词 ["满减优惠","限时折扣"]',
    `usage_guidelines`                  text COMMENT '使用说明（如仅限新用户）',
    `usage_restrictions`                text COMMENT '使用限制（如不可与其他活动叠加）',
    `related_questions_json`            text COMMENT '需要提问的问题(JSON数组["如何领取优惠券？"])',
    `is_enabled`                        tinyint(3) unsigned NOT NULL DEFAULT 1 COMMENT '启用状态 1-启用 0-归档',
    `created_at`                        bigint(20)          NOT NULL DEFAULT 0 COMMENT '创建时间',
    `created_uid`                       bigint(20)          NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_at`                        bigint(20)          NOT NULL DEFAULT 0 COMMENT '更新时间',
    `updated_uid`                       bigint(20)          NOT NULL DEFAULT 0 COMMENT '更新人',
    `is_del`                            tinyint(3)          NOT NULL DEFAULT 0 COMMENT '是否已删除',
    `deleted_at`                        bigint(20)          NOT NULL DEFAULT 0 COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_material_uuid` (`material_uuid`),
    KEY `idx_time_range` (`start_time`, `end_time`),
    KEY `idx_status` (`is_enabled`)
) COMMENT ='活动信息素材表';

CREATE TABLE `bailian_file`
(
    `id`               bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `created_at`       bigint(20)   NOT NULL DEFAULT '0' COMMENT '创建时间',
    `created_uid`      bigint(20)   NOT NULL DEFAULT '0' COMMENT '创建人',
    `updated_at`       bigint(20)   NOT NULL DEFAULT '0' COMMENT '更新时间',
    `updated_uid`      bigint(20)   NOT NULL DEFAULT '0' COMMENT '更新人',
    `is_del`           bigint(20)   NOT NULL DEFAULT '0' COMMENT '是否已删除',
    `deleted_at`       bigint(20)   NOT NULL DEFAULT '0' COMMENT '删除时间',
    `type`             smallint(6)  NOT NULL DEFAULT '0' COMMENT '模块类型 1-稿定模版 2-图片素材 3-知识库素材 4-活动信息素材',
    `type_id`          bigint(20)   NOT NULL DEFAULT '0' COMMENT '类型ID',
    `file_type`        tinyint(4)   NOT NULL DEFAULT '0' COMMENT '1-基础信息 2-文件信息',
    `url`              varchar(512) NOT NULL DEFAULT '原文件地址',
    `file_id`          varchar(64)  NOT NULL DEFAULT '' COMMENT '关联百炼文件ID',
    `filename`         varchar(128) NOT NULL DEFAULT '' COMMENT '文件名',
    `remote_status`    tinyint(4)   NOT NULL DEFAULT '0' COMMENT '百炼数据文件状态  1-待上传 2-已上传 3-已删除',
    `last_search_time` bigint(20)   NOT NULL DEFAULT '0' COMMENT '最近查询文件的时间',
    `delete_flag`      tinyint(4)   NOT NULL DEFAULT '0' COMMENT '删除标记（标记此文件可以被彻底删除）',
    `ref_count`        int(11)      NOT NULL DEFAULT '-1' COMMENT '引用计数，用于记录当前文档是否正在被索引，为0时，可以删除',
    PRIMARY KEY (`id`),
    KEY `idx_type_type_id` (`type`, `type_id`),
    KEY `idx_delete_flag` (`delete_flag`),
    KEY `idx_ref_count_remote_status` (`ref_count`, `remote_status`)
) ENGINE = InnoDB
  CHARSET = utf8mb4 COMMENT ='百炼文件处理';



CREATE TABLE `bailian_index_document`
(
    `id`            bigint(20)       NOT NULL AUTO_INCREMENT COMMENT 'id',
    `created_at`    bigint(20)       NOT NULL DEFAULT '0' COMMENT '创建日期',
    `created_uid`   bigint(20)       NOT NULL DEFAULT '0' COMMENT '创建人uid',
    `updated_at`    bigint(20)       NOT NULL DEFAULT '0' COMMENT '更新日期',
    `updated_uid`   bigint(20)       NOT NULL DEFAULT '0' COMMENT '更新人uid',
    `deleted_at`    bigint(20)       NOT NULL DEFAULT '0' COMMENT '删除日期',
    `is_del`        bigint(20)       NOT NULL DEFAULT '0' COMMENT '是否删除',
    `index_id`      varchar(32)      NOT NULL DEFAULT '' COMMENT '索引ID',
    `source_id`     varchar(64)      NOT NULL DEFAULT '' COMMENT '非结构化数据指向类目ID，结构化数据代表数据表ID',
    `file_id`       varchar(64)      NOT NULL DEFAULT '' COMMENT '知识库文档ID',
    `name`          varchar(256)     NOT NULL DEFAULT '' COMMENT '文档名称',
    `size`          int(10) unsigned NOT NULL DEFAULT '0' COMMENT '文档大小，单位字节 Byte。',
    `document_type` varchar(32)      NOT NULL DEFAULT '' COMMENT '文档格式类型。可能值为： pdf、docx、doc、txt、md、pptx、ppt、png、jpg、jpeg、bmp、gif、EXCEL。',
    `status`        varchar(32)      NOT NULL DEFAULT '' COMMENT '文档导入状态。可能值为： INSERT_ERROR：文档导入失败。 RUNNING：文档导入中。 DELETED：文档已删除。 FINISH：文档导入成功。',
    `code`          varchar(32)      NOT NULL DEFAULT '' COMMENT '文档导入错误状态码。',
    `message`       varchar(1024)    NOT NULL DEFAULT '' COMMENT '文档导入错误信息。',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_file_id` (`file_id`, `index_id`, `is_del`),
    KEY `idx_status` (`status`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='百炼文件索引下的文档列表';


CREATE TABLE `image_material`
(
    `id`                bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `material_uuid`     varchar(36)  NOT NULL COMMENT '素材全局唯一UUID',
    `material_name`     varchar(100) NOT NULL DEFAULT '' COMMENT '素材名称',
    `original_file_url` varchar(512) NOT NULL COMMENT '原始文件存储URL',
    `metadata`          json comment '图片元信息',
    `remote_file_id`    varchar(64)  NOT NULL DEFAULT '' COMMENT '关联百炼文件ID',
    `index_status`      tinyint(4)   NOT NULL DEFAULT 0 COMMENT '解析状态 0-待处理 1-已上传 2-已索引 3-失败',
    `created_at`        bigint(20)   NOT NULL DEFAULT 0 COMMENT '创建时间',
    `created_uid`       bigint(20)   NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_at`        bigint(20)   NOT NULL DEFAULT 0 COMMENT '更新时间',
    `updated_uid`       bigint(20)   NOT NULL DEFAULT 0 COMMENT '更新人',
    `is_del`            tinyint(3)   NOT NULL DEFAULT 0 COMMENT '是否已删除',
    `deleted_at`        bigint(20)   NOT NULL DEFAULT 0 COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_material_uuid` (`material_uuid`),
    KEY `idx_index_status` (`index_status`)
) COMMENT ='图片素材表';