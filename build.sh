#!/bin/bash
source  /tmp/evn
sed -i "s@ENV@"${ENV}"@" Makefile

echo 'monitorPort=9902' >>/tmp/evn

#替换解压命令
sed -i 's/tar -xvzf/tar --no-same-owner  --no-same-permissions -xvzf/' Dockerfile


serverName=ms-ai-agent
##开发
#if [[ "${ENV}" == "dev" ]];then
#  backendService=*************:11800
#  sed -i "/CMD.*/d" Dockerfile
#  echo 'CMD ["java","-javaagent:/app/skywalking-agent/skywalking-agent.jar", "-Dskywalking.agent.service_name='$serverName'-dev", "-Dskywalking.collector.backend_service='$backendService'","-jar","server.jar"]' >>Dockerfile
#fi
##测试
#if [[ "${ENV}" == "test" ]];then
#  backendService=*************:11800
#  sed -i "/ADD/aADD ark-sailor-admin\/src\/main\/resources\/jacocoagent.jar \/tmp\/jacocoagent.jar" Dockerfile
#  sed -i "/CMD.*/d" Dockerfile
#  echo 'CMD ["java","-javaagent:/tmp/jacocoagent.jar=output=tcpserver,port=8086,address=*","-javaagent:/app/skywalking-agent/skywalking-agent.jar","-Dskywalking.agent.service_name='$serverName'-test", "-Dskywalking.collector.backend_service='$backendService'","-jar","server.jar"]' >>Dockerfile
#fi
if [[ "${ENV}" == "test" ]];then
  backendService=*************:11800
  sed -i "/CMD.*/d" Dockerfile
  echo 'CMD ["java","-Xms3072m","-Xmx3072m","-javaagent:/app/skywalking-agent/skywalking-agent.jar", "-Dskywalking.agent.service_name='$serverName'-test", "-Dskywalking.collector.backend_service='$backendService'","-jar","server.jar"]' >>Dockerfile
fi
##灰度
if [[ "${ENV}" == "gray" ]];then
  backendService=************:118001
  sed -i "/CMD.*/d" Dockerfile
  echo 'CMD ["java","-Xms3072m","-Xmx3072m","-javaagent:/app/skywalking-agent/skywalking-agent.jar", "-Dskywalking.agent.service_name='$serverName'-gray", "-Dskywalking.collector.backend_service='$backendService'","-jar","server.jar"]' >>Dockerfile
fi
#生产
if [[ "${ENV}" == "prod" ]];then
  backendService=************:11800
  sed -i "/CMD.*/d" Dockerfile
  echo 'CMD ["java","-Xms3072m","-Xmx3072m","-javaagent:/app/skywalking-agent/skywalking-agent.jar", "-Dskywalking.agent.service_name='$serverName'-prod", "-Dskywalking.collector.backend_service='$backendService'","-jar","server.jar"]' >>Dockerfile
fi
