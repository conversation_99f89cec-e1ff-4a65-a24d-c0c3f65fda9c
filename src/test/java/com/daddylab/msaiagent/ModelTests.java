package com.daddylab.msaiagent;

import cn.hutool.core.util.URLUtil;
import com.alibaba.cloud.ai.dashscope.api.DashScopeImageApi;
import com.alibaba.cloud.ai.dashscope.chat.DashScopeChatOptions;
import com.alibaba.cloud.ai.dashscope.embedding.DashScopeEmbeddingModel;
import com.alibaba.cloud.ai.dashscope.image.DashScopeImageModel;
import com.alibaba.cloud.ai.dashscope.image.DashScopeImageOptions;
import com.daddylab.msaiagent.common.utils.JsonUtil;
import com.daddylab.msaiagent.domain.enums.AIModelEnum;
import com.daddylab.msaiagent.manage.AiClientManage;
import com.daddylab.msaiagent.prompt.ScenePrompts;
import com.daddylab.msaiagent.tool.RedBookTool;
import com.daddylab.msaiagent.tool.TimeTool;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.embedding.Embedding;
import org.springframework.ai.embedding.EmbeddingOptionsBuilder;
import org.springframework.ai.embedding.EmbeddingRequest;
import org.springframework.ai.embedding.EmbeddingResponse;
import org.springframework.ai.image.ImageMessage;
import org.springframework.ai.image.ImagePrompt;
import org.springframework.ai.model.Media;
import org.springframework.ai.model.tool.ToolCallingChatOptions;
import org.springframework.ai.tool.ToolCallbacks;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.MimeTypeUtils;

import java.net.MalformedURLException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@SpringBootTest
public class ModelTests {
    @Autowired RedBookTool redBookTool;

    @Autowired private DashScopeEmbeddingModel dashScopeEmbeddingModel;
    @Autowired private DashScopeImageModel dashScopeImageModel;

    @Test
    public void embedding() {
        EmbeddingResponse embeddingResponse =
                dashScopeEmbeddingModel.call(
                        new EmbeddingRequest(
                                List.of("种草"),
                                EmbeddingOptionsBuilder.builder()
                                        .withModel("text-embedding-v3")
                                        .build()));
        Embedding result = embeddingResponse.getResult();
        Assertions.assertNotNull(result);
        System.out.println(Arrays.toString(result.getOutput()));
    }

    @Test
    public void dashscopeChatTest() throws InterruptedException {
        ChatClient chatClient = AiClientManage.buildDefaultClient(AIModelEnum.AZURE_OPENAI);
        String content =
                chatClient
                        .prompt()
                        .options(
                                ToolCallingChatOptions.builder()
                                        .model("gpt-4.1")
                                        .toolCallbacks(ToolCallbacks.from(TimeTool.class))
                                        .toolCallbacks(ToolCallbacks.from(redBookTool))
                                        .build())
                        .user("今天是几月几号？查询今天的小红书热点，写一篇种草笔记")
                        .call()
                        .content();
        System.out.println(content);
    }

    @Test
    public void azureOpenaiChatTest() {
        ChatClient chatClient = AiClientManage.buildDefaultClient(AIModelEnum.AZURE_OPENAI);
        ChatClient.CallResponseSpec call =
                chatClient
                        .prompt()
                        .options(ChatOptions.builder().model("gpt-4.1").build())
                        .user("hello, are you ok?")
                        .call();
        System.out.println(call.content());
        System.out.println(JsonUtil.toJSONString(call.chatResponse()));
    }

    @Test
    public void dashScopeImageRecogonizeModelTest() throws MalformedURLException {
        ChatClient chatClient = AiClientManage.buildDefaultClient(AIModelEnum.DASHSCOPE);
        List<String> imgs =
                List.of(
                        "https://cdn.daddylab.com/Upload/supplier/item/image/1926830757083189248.jpg",
                        "https://cdn.daddylab.com/Upload/supplier/item/image/1926830757821386752.jpg",
                        "https://cdn.daddylab.com/Upload/supplier/item/image/1926830756462432256.jpg",
                        "https://cdn.daddylab.com/Upload/supplier/item/image/1926830758106599424.jpg",
                        "https://cdn.daddylab.com/Upload/supplier/item/image/1926830756957360128.jpg",
                        "https://cdn.daddylab.com/Upload/supplier/item/image/1926830757150298112.jpg",
                        "https://cdn.daddylab.com/Upload/supplier/item/image/1926830759398445056.png",
                        "https://cdn.daddylab.com/Upload/supplier/item/image/1926830761243938816.png",
                        "https://cdn.daddylab.com/Upload/supplier/item/image/1926830763991207936.png",
                        "https://cdn.daddylab.com/Upload/supplier/item/image/1926830762690973696.jpg");
        ChatOptions chatOptions =
                DashScopeChatOptions.builder()
                        .withModel("qwen-vl-max")
                        .withMultiModel(true)
                        .build();
        List<Media> mediaList =
                imgs.stream()
                        .map(url -> new Media(MimeTypeUtils.IMAGE_JPEG, URLUtil.url(url)))
                        .collect(Collectors.toList());
        Prompt prompt =
                new Prompt(
                        new UserMessage(ScenePrompts.ITEM_USE_SCENE, mediaList),
                        chatOptions);
        String content =
                chatClient.prompt(prompt).advisors(new SimpleLoggerAdvisor()).call().content();
        System.out.println(content);
    }

    @Test
    public void dashScopeGenerateImageTest() {
        List<ImageMessage> imageMessages =
                List.of(
                        new ImageMessage(
"""
### 1. **Overall Scene Description:**

The image is an advertisement for a pair of eyeglasses. The main subject is a pair of black-framed glasses with clear lenses, prominently displayed at the top of the image. Below the glasses, there is text in Chinese that translates to "Matches multiple face shapes, designed for Asians." Further down, there are illustrations of four different female face shapes (long face, round face, oval face, and diamond face) with red outlines indicating how the glasses would fit each shape. The overall mood of the image is informative and commercial, aiming to showcase the versatility and design of the glasses.

### 2. **Objects and Entities:**

- **Eyeglasses:**
  - **Color:** The frame is primarily black with a subtle pinkish tint on the inner side of the lenses.
  - **Size:** The glasses appear to be of a standard adult size, with wide temples and large lenses.
  - **Shape:** The frame has a rounded rectangular shape with slightly curved edges.
  - **Texture and Material:** The frame appears to be made of a smooth, glossy plastic material.
  - **Branding/Logos:** No visible branding or logos are present on the glasses.

- **Illustrations of Face Shapes:**
  - **Description:** Four different female face shapes are illustrated with detailed facial features and hair.
  - **Attributes:** Each face is depicted with long, dark hair and similar facial features (eyes, nose, mouth), differing only in the overall shape of the face.
  - **Red Outlines:** Red lines are used to highlight the contours of each face shape, showing how the glasses would complement each type.

### 3. **Setting and Environment:**

- **Location and Environment:** The image is a digital illustration and does not depict a real-world location. It is designed for advertising purposes.
- **Background and Foreground:**
  - **Background:** The background is plain white, which helps to emphasize the glasses and the face illustrations.
  - **Foreground:** The glasses are placed at the top, with the text and face illustrations below.
- **Lighting Conditions:** The lighting is bright and even, typical of product photography, ensuring all details are clearly visible.
- **Time of Day and Weather Conditions:** Not applicable as this is a digitally created image.

### 4. **Text and Symbols:**

- **Text:**
  - **Main Text:** "匹配多种脸型 为亚洲人设计" (Matches multiple face shapes, designed for Asians).
  - **Subtext:** "适配脸型:" (Suitable face shapes:).
  - **Face Shape Labels:** "长脸" (Long face), "圆脸" (Round face), "鹅蛋脸" (Oval face), "菱形脸" (Diamond face).

- **Symbols:** No specific symbols are present, but the red outlines around the face shapes serve as a visual guide.

### 5. **Composition and Perspective:**

- **Camera Angle and Perspective:** The glasses are shown from a front-facing perspective, providing a clear view of the frame and lenses. The face illustrations are also presented head-on.
- **Arrangement of Elements:** The image is neatly organized with the glasses at the top, followed by the text, and then the face illustrations. This creates a logical flow of information.
- **Focal Point:** The glasses are the primary focal point, with the text and illustrations supporting the main message.

### 6. **Actions and Interactions:**

- **Actions:** There are no actions being performed as the image consists of static objects and illustrations.
- **Interactions:** The red outlines on the face illustrations suggest how the glasses would interact with different face shapes, emphasizing their versatility.

### 7. **Contextual Understanding and Inferences:**

- **Purpose:** The image is clearly an advertisement aimed at promoting the eyeglasses, highlighting their suitability for various Asian face shapes.
- **Inferences:** The use of illustrations rather than real people suggests a focus on clarity and general applicability. The emphasis on Asian face shapes indicates a target market specific to Asian consumers.
- **Cultural Elements:** The text and the focus on Asian face shapes reflect a cultural specificity, catering to the preferences and needs of an Asian audience.

### 8. **Details and Subtleties:**

- **Pinkish Tint:** The subtle pinkish tint on the inner side of the lenses is a small but noticeable detail that adds a unique touch to the design.
- **Hair and Earrings:** The illustrations include detailed hair and earrings, adding realism and appeal to the face shapes.

### 9. **Potential Ambiguities:**

- **Real vs. Illustrated Faces:** The face shapes are illustrated rather than photographed, which might lead to some ambiguity in how the glasses would actually look on real individuals.
- **Specificity of "Asian" Design:** The term "designed for Asians" is broad and could encompass a wide range of specific ethnicities and preferences.

### 10. **Key Information Summary:**

- **Eyeglasses with a black frame and clear lenses.**
- **Designed to match multiple face shapes (long, round, oval, diamond).**
- **Targeted specifically for Asian consumers.**
- **Illustrated face shapes with red outlines to demonstrate fit.**
- **Bright, even lighting for clear visibility.**
- **Organized composition with a clear focal point on the glasses.**
- **Advertisement purpose with a cultural focus on Asian aesthetics.**
- **Category:** Product shot (specifically, eyewear advertisement).

This thorough analysis covers all aspects of the image, providing a comprehensive understanding of its content and context.
"""));
        DashScopeImageOptions dashScopeImageOptions = new DashScopeImageOptions();
        dashScopeImageOptions.setModel("wanx2.1-t2i-plus");
        ImagePrompt imagePrompt = new ImagePrompt(imageMessages, dashScopeImageOptions);
        String taskId = dashScopeImageModel.submitImageGenTask(imagePrompt);
        System.out.println(taskId);
    }

    @Test
    public void dashScopeGenerateImageGetResultTest() {
        String taskId = "4f38cb75-b57a-49df-bd15-50c69563ef49";
        while (true) {
            DashScopeImageApi.DashScopeImageAsyncReponse imageGenTask =
                    dashScopeImageModel.getImageGenTask(taskId);
            System.out.println(JsonUtil.toJSONString(imageGenTask));
            if (!imageGenTask.output().results().isEmpty()) {
                break;
            }
        }
    }
}
