package com.daddylab.msaiagent;

import com.alibaba.cloud.ai.dashscope.embedding.DashScopeEmbeddingModel;

import com.daddylab.msaiagent.db.aiAgent.dao.RedBookDao;
import com.daddylab.msaiagent.db.aiAgent.entity.RedBook;
import io.qdrant.client.QdrantClient;
import io.qdrant.client.QdrantGrpcClient;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.ai.document.Document;
import org.springframework.ai.embedding.*;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.filter.FilterExpressionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;
import java.util.concurrent.ExecutionException;

@SpringBootTest
@Slf4j
public class VectorStoreTests {

    @Autowired private VectorStore vectorStore;
    @Autowired private RedBookDao redBookDao;


    @Test
    public void save() {
        for (RedBook redBook : redBookDao.lambdaQuery().list()) {
            ArrayList<Document> documents = new ArrayList<>();
            documents.add(
                    Document.builder()
                            .id(UUID.randomUUID().toString())
                            .text(redBook.getContent())
                            .metadata("table", "redbook")
                            .metadata("id", redBook.getId().toString())
                            .metadata("accountId", redBook.getAccountId())
                            .metadata(
                                    "accountName",
                                    Optional.ofNullable(redBook.getAccountName()).orElse(""))
                            .metadata("type", Optional.ofNullable(redBook.getType()).orElse(0))
                            .metadata("url", Optional.ofNullable(redBook.getUrl()).orElse(""))
                            .metadata(
                                    "tags",
                                    Optional.ofNullable(redBook.getTags())
                                            .map(list -> String.join(",", list))
                                            .orElse(""))
                            .build());
            vectorStore.add(documents);
            log.info("[小红书笔记][向量索引] id={}", redBook.getId());
        }
    }

    @Test
    public void search() {
        List<Document> documents =
                vectorStore.similaritySearch(
                        SearchRequest.builder()
                                .query("甲醛")
                                .similarityThreshold(0.7)
                                .topK(10)
                                .filterExpression(
                                        new FilterExpressionBuilder()
                                                .eq("accountId", "**********")
                                                .build())
                                .build());
        Assertions.assertNotNull(documents);
        System.out.println(documents);
    }
}
