package com.daddylab.msaiagent;

import com.daddylab.msaiagent.db.aiAgent.entity.SceneFragment;
import com.fasterxml.jackson.databind.JsonNode;
import com.github.victools.jsonschema.generator.*;
import org.junit.jupiter.api.Test;
import org.springframework.ai.util.json.schema.JsonSchemaGenerator;

public class JsonSchemaGenerateTests {
    @Test
    public void generateTest() {
        String schema = JsonSchemaGenerator.generateForType(SceneFragment.class);
        System.out.println(schema);
    }
}
