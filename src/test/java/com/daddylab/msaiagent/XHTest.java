package com.daddylab.msaiagent;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.msaiagent.agent.VirtualPersonGrowthAgent;
import com.daddylab.msaiagent.common.utils.JsonUtil;
import com.daddylab.msaiagent.db.aiAgent.dao.PersonDetailSnapshotDao;
import com.daddylab.msaiagent.db.aiAgent.dao.PersonaAttributeDefinitionDao;
import com.daddylab.msaiagent.db.aiAgent.dao.PersonaDetailDao;
import com.daddylab.msaiagent.db.aiAgent.dao.RedbookHotspotDao;
import com.daddylab.msaiagent.db.aiAgent.entity.PersonDetailSnapshot;
import com.daddylab.msaiagent.db.aiAgent.entity.PersonaAttributeDefinition;
import com.daddylab.msaiagent.db.aiAgent.entity.PersonaDetail;
import com.daddylab.msaiagent.db.aiAgent.entity.RedbookHotspot;
import com.daddylab.msaiagent.domain.enums.GrowType;
import com.daddylab.msaiagent.service.impl.RedBookThirdPartyDataService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> up
 * @date 2025年05月12日 5:28 PM
 */
@SpringBootTest
public class XHTest {

  @Autowired RedBookThirdPartyDataService thirdPartyDataService;

  @Autowired VirtualPersonGrowthAgent virtualPersonGrowthAgent;

  @Autowired PersonaAttributeDefinitionDao personaAttributeDefinitionDao;
  @Autowired PersonaDetailDao personaDetailDao;
  @Autowired RedbookHotspotDao redbookHotspotDao;
  @Autowired
  PersonDetailSnapshotDao personDetailSnapshotDao;

  public static void main(String[] args) {}

  @Test
  public void test() throws Exception {
//    virtualPersonGrowthAgent.updatePerson(1L);

    //    final String collect =
    //        redbookHotspotDao
    //            .lambdaQuery()
    //            .ge(RedbookHotspot::getStartTime, 1746028800)
    //            .le(RedbookHotspot::getEndTime, 1748707200)
    //            .eq(RedbookHotspot::getRank, 4)
    //            .orderByDesc(RedbookHotspot::getHotVal)
    //            .last("limit 500")
    //            .list()
    //            .stream()
    //            .map(val -> StrUtil.format("hotspot：{}，index：{}", val.getHotword(),
    // val.getHotVal()))
    //            .distinct()
    //            .collect(Collectors.joining("\n"));
    //    System.out.println(collect);

        final PersonaDetail personaDetail = personaDetailDao.getById(1L);
        final Map<String, Object> personFileMap = JsonUtil.parseMap(personaDetail.getContent());
        final Set<String> attributeSet = personFileMap.keySet();
        final List<VirtualPersonGrowthAgent.AttributeDto> attributeDtoList =
            personaAttributeDefinitionDao
                .lambdaQuery()
                .in(PersonaAttributeDefinition::getAttributeKey, attributeSet)
                .list()
                .stream()
                .filter(val -> !val.getGrowType().equals(GrowType.STATIC_TYPE))
                .map(
                    val -> {
                      VirtualPersonGrowthAgent.AttributeDto dto =
                          new VirtualPersonGrowthAgent.AttributeDto();
                      dto.setAttributeKey(val.getAttributeKey());
                      dto.setAttributeDesc(val.getAttributeName());
                      dto.setGrowType(val.getGrowType().getDesc());
                      dto.setGrowCondition(val.getGrowCondition());
                      dto.setGrowValue(val.getGrowFrequency().getDesc());
                      return dto;
                    })
                .toList();
        String personAttributeJson = JsonUtil.toJSONString(attributeDtoList);
//        System.out.println(personAttributeJson);


    final PersonDetailSnapshot snapshot = personDetailSnapshotDao.getById(8L);
    final String s = virtualPersonGrowthAgent.attributeGrowUp(personaDetail.getContent(),
            personAttributeJson, snapshot.getAttributeAnalysis());
    System.out.println(s);

    //    final String collect =
    //        personaAttributeDefinitionDao.lambdaQuery().list().stream()
    //            .map(
    //                val ->
    //                    StrUtil.format(
    //                        "key:{},name:{},desc:{}",
    //                        val.getAttributeKey(),
    //                        val.getAttributeName(),
    //                        val.getDescription()))
    //            .collect(Collectors.joining("\n"));
    //    System.out.println(collect);

    //        virtualPersonGrowthAgent.writeDairy0("0QPX-371010");
    //        thirdPartyDataService.fetchSurgeNoteRank();
    //        thirdPartyDataService.fetchBusinessNoteRank();
    //        thirdPartyDataService.fetchLowFanNoteRank();

    // 获取笔记热度上升排行榜
    //        ResponseEntity<String> surgeResponse = thirdPartyDataService.getSurgeNoteRank(
    //                "家居家装",
    //                "",
    //                "1",
    //                "2025-05-11 00:00:00+08",
    //                1,
    //                "interactive_count_incr",
    //                1,
    //                20);
    //        System.out.println("热度上升排行榜响应: " + surgeResponse.getBody());
    //
    //        // 获取商业笔记排行榜
    //        ResponseEntity<String> businessResponse = thirdPartyDataService.getBusinessNoteRank(
    //                "家居家装",
    //                "",
    //                "3",
    //                3,
    //                "2025-04-01",
    //                "liked_count",
    //                1,
    //                20);
    //        System.out.println("商业笔记排行榜响应: " + businessResponse.getBody());
    //
    //        // 获取低粉作者爆发榜
    //        ResponseEntity<String> lowFanResponse = thirdPartyDataService.getLowFanSurgeRank(
    //                "家居家装",
    //                "3",
    //                "3",
    //                "interactive_count",
    //                1,
    //                20);
    //        System.out.println("低粉作者爆发榜响应: " + lowFanResponse.getBody());
  }
}
