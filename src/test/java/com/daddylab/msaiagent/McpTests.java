package com.daddylab.msaiagent;

import com.daddylab.msaiagent.common.utils.JsonUtil;
import org.junit.jupiter.api.Test;
import org.springframework.ai.mcp.client.McpClient;
import org.springframework.ai.mcp.client.McpSyncClient;
import org.springframework.ai.mcp.client.transport.ServerParameters;
import org.springframework.ai.mcp.client.transport.StdioClientTransport;
import org.springframework.ai.mcp.spec.ClientMcpTransport;
import org.springframework.ai.mcp.spec.McpSchema;
import org.springframework.ai.mcp.spec.McpTransport;

import java.util.HashMap;

public class McpTests {
    @Test
    public void sequentialThinkingClient() {
        ServerParameters params = ServerParameters.builder("/Users/<USER>/.nvm/versions/node/v22.15.1/bin/npx")
                .args("-y", "@modelcontextprotocol/server-sequential-thinking")
                .build();
        StdioClientTransport transport = new StdioClientTransport(params);
        McpSyncClient mcpClient = McpClient.sync(transport).build();
        McpSchema.ListToolsResult listToolsResult = mcpClient.listTools();
        System.out.println("tools:" + JsonUtil.toJSONString(listToolsResult));
        HashMap<String, Object> toolArgs = new HashMap<>();
        //"thought", "nextThoughtNeeded", "thoughtNumber", "totalThoughts"
        toolArgs.put("thought", "I want to make a sandwich.");
        toolArgs.put("nextThoughtNeeded", true);
        toolArgs.put("thoughtNumber", 1);
        toolArgs.put("totalThoughts", 3);
        McpSchema.CallToolResult sequentialthinkingResult = mcpClient.callTool(new McpSchema.CallToolRequest("sequentialthinking", toolArgs));
        System.out.println(JsonUtil.toJSONString(sequentialthinkingResult));
    }
}
