package com.daddylab.msaiagent;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.volcengine.service.visual.IVisualService;
import com.volcengine.service.visual.impl.VisualServiceImpl;

public class VolcengineTests {

    public static void main(String[] args) {
        IVisualService visualService = VisualServiceImpl.getInstance();
        // call below method if you dont set ak and sk in ～/.vcloud/config
        visualService.setAccessKey("AKLTMTA4ZGZjNGM4ZGI3NGQ1ODg2M2FmNThiOTU0OTMwOWQ");
        visualService.setSecretKey("WW1JMVl6QmtOamd4TVdVMU5EZ3laR0ZrTnpOalpEVmhaamhrT1dVeFlUSQ==");

        var prompt =
"""
## Task: generate the image based on the provided JSON structure

## Image Description JSON

```json
{
  "overall_scene_description": {
    "summary": "The image depicts a promotional advertisement for custom-made safety bumpers designed to protect children.",
    "main_subjects": "Rolled-up beige-colored foam cushions and stacked foam cushions",
    "mood": "Informative and educational"
  },
  "objects_and_entities": [
    {
      "type": "Foam Cushion",
      "description": "A rolled-up beige-colored foam cushion",
      "attributes": {
        "color": "Beige",
        "size": "Approximately 10 inches in diameter when rolled",
        "shape": "Circular when rolled",
        "texture": "Smooth",
        "material": "Foam"
      }
    },
    {
      "type": "Foam Cushion",
      "description": "Three stacked beige-colored foam cushions",
      "attributes": {
        "color": "Beige",
        "size": "Approximately 6 inches in height when stacked",
        "shape": "Rectangular",
        "texture": "Smooth",
        "material": "Foam"
      }
    },
    {
      "type": "Plant",
      "description": "A small green plant in the background",
      "attributes": {
        "color": "Green",
        "size": "Small, approximately 4 inches tall",
        "shape": "Leafy",
        "texture": "Textured leaves",
        "material": "Plant"
      }
    }
  ],
  "setting_and_environment": {
    "location": "Indoor setting, likely a studio or home environment",
    "background_elements": "Plain white wall with a wooden surface",
    "foreground_elements": "Rolled-up and stacked foam cushions on a wooden surface",
    "lighting_conditions": "Bright artificial lighting",
    "time_and_weather": "Not applicable (indoor setting)"
  },
  "text_and_symbols": {
    "text": [
      "我们为什么要定制防撞条?",
      "很多有宝宝家庭会使用防撞条，来给宝宝提供防护",
      "我们花了7个月的时间，在尝试更换发泡剂、生产工艺调整，不断打样、调试、检测后",
      "终于定制出了这款无甲酰胺风险的防撞条",
      "*由于发泡剂的更换，该款防撞条相对较硬"
    ],
    "symbols_and_icons": []
  },
  "composition_and_perspective": {
    "camera_angle": "Eye-level",
    "element_arrangement": "Rolled-up cushion on the left, stacked cushions on the right, text above",
    "focal_point": "Rolled-up foam cushion",
    "composition_techniques": "Rule of thirds, leading lines from the cushions"
  },
  "actions_and_interactions": {
    "actions": "No actions being performed",
    "interactions": "No interactions between elements",
    "motion_or_stillness": "Stillness"
  },
  "contextual_understanding_and_inferences": {
    "purpose_or_context": "Promotional advertisement for custom-made safety bumpers",
    "inferences": "The product is designed to be safe for children by avoiding harmful chemicals",
    "cultural_or_historical_elements": "None specific",
    "outside_frame_or_next": "Potential use of the cushions in a child's room or play area"
  },
  "details_and_subtleties": {
    "notable_details": "The text provides detailed information about the product's development and benefits",
    "anomalies_or_unusual_elements": "None"
  },
  "potential_ambiguities": "The exact brand or model of the foam cushions is not specified",
  "key_information_summary": [
    "Custom-made safety bumpers for child protection",
    "Developed over 7 months to avoid harmful chemicals",
    "Safe for long-term contact with children",
    "Harder texture due to changes in foaming agent"
  ],
  "image_category": "Product Advertisement"
}
```
""";
        JSONObject req =
                JSON.parseObject(
                        """
                        {
                            "req_key": "high_aes_general_v21_L",
                            "llm_seed": -1,
                            "seed": -1,
                            "scale": 5,
                            "ddim_steps": 50,
                            "width": 512,
                            "height": 512,
                            "use_pre_llm": false,
                            "use_sr": true,
                            "return_url": true
                        }
                        """);
        req.put("prompt", prompt);

        try {
            Object response = visualService.cvProcess(req);
            System.out.println(JSON.toJSONString(response));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
