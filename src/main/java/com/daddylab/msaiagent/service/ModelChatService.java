package com.daddylab.msaiagent.service;

import com.daddylab.msaiagent.db.aiAgent.entity.ModelChat;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.openai.OpenAiChatModel;

import java.util.List;
import java.util.function.Consumer;

public interface ModelChatService {

    ModelChat createChat(String modelName, String chatType);

    ModelChat getChat(String modelName, String chatType);

    ModelChat getChat(Long id);

    void startChatAsync(ModelChat chat, String promptStr, Consumer<String> callback);

    List<Message> getChatMessages(ModelChat chat);

    OpenAiChatModel getChatModel(String modelName);
}
