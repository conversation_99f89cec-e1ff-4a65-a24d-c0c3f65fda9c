package com.daddylab.msaiagent.service;

import com.daddylab.msaiagent.common.feign.domain.query.YingDaoStartQuery;
import com.daddylab.msaiagent.common.feign.domain.vo.YingDaoTaskQueryVo;

import java.util.List;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @ClassName YingDaoService.java
 * @Description 描述类的作用
 * @Date 2023-11-28 17:36
 */
public interface YingDaoService {

    /**
     * 执行任务
     *
     * @param scheduleId String
     * @return void
     * @date 2023/11/28 17:37
     * <AUTHOR>
     */
    String runTask(String scheduleId);

    String runTask(String scheduleId, List<YingDaoStartQuery.ScheduleRelaParams> scheduleRelaParams);

    void registerCallBack(String taskUuId, long intervalSecond, Consumer<YingDaoTaskQueryVo> handler);
}
