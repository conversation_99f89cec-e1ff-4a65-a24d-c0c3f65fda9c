package com.daddylab.msaiagent.service.material;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.daddylab.msaiagent.domain.form.ProductAiGeneratedForm;
import com.daddylab.msaiagent.domain.form.ProductUpdateForm;
import com.daddylab.msaiagent.domain.vo.AiJobVO;
import com.daddylab.msaiagent.domain.vo.ProductInfoVO;


public interface ProductService {

    AiJobVO generateByAi(ProductAiGeneratedForm form);

    Long updateInfo(ProductUpdateForm form);

    ProductInfoVO getProductDetail(Long productId);

    IPage<ProductInfoVO> getPersonList(int pageNum, int pageSize);

    void productArchived(Long productId);

    void parseDetailByChatRecord(Long productId);
}
