package com.daddylab.msaiagent.service.material.impl;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.msaiagent.common.utils.JsonUtil;
import com.daddylab.msaiagent.convert.ProductConvert;
import com.daddylab.msaiagent.db.aiAgent.dao.ProductMaterialDao;
import com.daddylab.msaiagent.db.aiAgent.entity.AigcJob;
import com.daddylab.msaiagent.db.aiAgent.entity.ProductMaterial;
import com.daddylab.msaiagent.domain.constants.ChatTypeConst;
import com.daddylab.msaiagent.domain.dto.ProductDetailDto;
import com.daddylab.msaiagent.domain.form.ProductAiGeneratedForm;
import com.daddylab.msaiagent.domain.form.ProductUpdateForm;
import com.daddylab.msaiagent.domain.vo.AiJobVO;
import com.daddylab.msaiagent.domain.vo.ProductInfoVO;
import com.daddylab.msaiagent.prompt.ProductGeneratePrompt;
import com.daddylab.msaiagent.service.AigcJobService;
import com.daddylab.msaiagent.service.material.ProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ProductServiceImpl implements ProductService {

    @Autowired
    private ProductMaterialDao productMaterialDao;
    @Autowired
    AigcJobService aigcJobService;

    @Override
    public AiJobVO generateByAi(ProductAiGeneratedForm form) {
        ProductMaterial material = new ProductMaterial();
        material.setAssetUuid(RandomUtil.randomStringUpper(4)+"-"+RandomUtil.randomNumbers(6));
        material.setProductName(form.getProductName());
        material.setDescription(form.getProductDetail());
        material.setIsEnabled(1);
        productMaterialDao.save(material);
        AigcJob job = aigcJobService.createJob(material.getId(),ChatTypeConst.AI_GENERATOR_PRODUCT,0L,form);
        String template = getTemplate(form);
        log.info("开始请求ai进行商品分析:{}", material.getAssetUuid());
        aigcJobService.startJobAsync(job, template, content -> saveProduct(job.getId(),content, material));
        return AiJobVO.fromJob(job);
    }

    private static String getTemplate(ProductAiGeneratedForm form) {
        String template = ProductGeneratePrompt.GENERATE;
        template = template.replace("[product_name_input]", form.getProductName())
                .replace("[product_detail]", form.getProductDetail())
                .replace("[core_selling_points_manual_input]", form.getCoreSellingPoints())
                .replace("[target_keywords]", form.getTargetKeywords())
                .replace("[additional_notes_for_ai]", form.getAdditionalNotesForAi())
                .replace("[instructions_for_use_brief]", form.getInstructionsForUseBrief());
        return template;
    }

    @Override
    public Long updateInfo(ProductUpdateForm form) {

        ProductMaterial material = getProductInfo(form.getId());
        if(null == material){
            return form.getId();
        }
        ProductConvert.INSTANCE.updateMaterialFromDto(form.getProductDetail(), material);
        productMaterialDao.updateById(material);
        return  form.getId();
    }

    private ProductMaterial getProductInfo(Long productId) {
        return productMaterialDao.getById(productId);
    }

    @Override
    public ProductInfoVO getProductDetail(Long productId) {
        ProductMaterial material = productMaterialDao.getById(productId);
        if (null == material) {
            return null;
        }
        ProductInfoVO productInfoVO = new ProductInfoVO();
        ProductDetailDto productDetail = ProductConvert.INSTANCE.materialToDto(material);
        productInfoVO.setProductDetail(productDetail);
        productInfoVO.setProductUk(material.getAssetUuid());
        productInfoVO.setId(material.getId());
        return productInfoVO;
    }

    @Override
    public IPage<ProductInfoVO> getPersonList(int pageNum, int pageSize) {
        LambdaQueryChainWrapper<ProductMaterial> query = productMaterialDao.lambdaQuery();
        Page<ProductMaterial> records = productMaterialDao.page(new Page<>(pageNum, pageSize), query);
        return records.convert(material->{
            ProductInfoVO productInfoVO = new ProductInfoVO();
            ProductDetailDto productDetail = ProductConvert.INSTANCE.materialToDto(material);
            productInfoVO.setProductDetail(productDetail);
            productInfoVO.setProductUk(material.getAssetUuid());
            productInfoVO.setId(material.getId());
            return productInfoVO;
        });
    }

    @Override
    public void productArchived(Long productId) {
        ProductMaterial productInfo = getProductInfo(productId);
        if(null == productInfo){
            return;
        }
        productInfo.setIsEnabled(0);
        productMaterialDao.updateById(productInfo);
    }

    private void saveProduct(Long jobId,String content, ProductMaterial material) {

        try {
            log.info("ai 返回商品参数的信息，开始整合:{}", material.getAssetUuid());
            ProductDetailDto dto = JsonUtil.parseObject(content, ProductDetailDto.class);
            if (null == dto) {
                log.error("ai返回的不是约定格式的json数据");
                aigcJobService.jobError(jobId, "ai返回的不是约定格式的json数据");
                return;
            }
            // 使用MapStruct自动映射非空字段
            ProductConvert.INSTANCE.updateMaterialFromDto(dto, material);
            // 保存更新后的material到数据库
            material.setJobId(jobId);
            productMaterialDao.updateById(material);
            log.info("ai 返回商品参数的信息，整合完毕:{}", material.getAssetUuid());
        } catch (Exception e) {
            log.error("解析ai返回的数据异常{},{}",material.getAssetUuid(), e.getMessage());
            aigcJobService.jobError(jobId, "解析ai返回的数据异常");
        }
    }

    @Override
    public void parseDetailByChatRecord(Long productId) {

    }

    /*private void updateByAiContent(List<Message> messages, ProductMaterial product, int detailChatIndex) {
        if (detailChatIndex < 0 || detailChatIndex >= messages.size()) {
            log.error("没有找到解析商品的聊天记录");
            return;
        }
        Message message = messages.get(detailChatIndex);
        if (message.getMessageType() != MessageType.ASSISTANT) {
            log.error("解析商品的聊天记录不是助手的消息");
            return;
        }
        String content = message.getText();
        saveProduct(content, product);
    }*/
}
