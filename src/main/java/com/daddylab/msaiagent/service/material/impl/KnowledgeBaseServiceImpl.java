package com.daddylab.msaiagent.service.material.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.PageResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.daddylab.msaiagent.common.bailian.BaiLianService;
import com.daddylab.msaiagent.common.bailian.domain.enums.ModuleEnum;
import com.daddylab.msaiagent.common.oss.OssGateway;
import com.daddylab.msaiagent.common.utils.PageUtils;
import com.daddylab.msaiagent.common.utils.PdfUtil;
import com.daddylab.msaiagent.convert.KnowledgeBaseConvert;
import com.daddylab.msaiagent.db.aiAgent.dao.KnowledgeBaseMaterialDao;
import com.daddylab.msaiagent.db.aiAgent.entity.KnowledgeBaseMaterial;
import com.daddylab.msaiagent.db.aiAgent.enums.BaiLianFileTypeEnum;
import com.daddylab.msaiagent.db.aiAgent.mapper.KnowledgeBaseMaterialMapper;
import com.daddylab.msaiagent.domain.form.KnowledgeBaseMaterialAIForm;
import com.daddylab.msaiagent.domain.form.KnowledgeBaseMaterialAddForm;
import com.daddylab.msaiagent.domain.form.KnowledgeBaseMaterialEditForm;
import com.daddylab.msaiagent.domain.form.KnowledgeBaseMaterialFixForm;
import com.daddylab.msaiagent.domain.query.KnowledgeBaseMaterialQuery;
import com.daddylab.msaiagent.domain.vo.KnowledgeBaseMaterialPageVO;
import com.daddylab.msaiagent.domain.vo.KnowledgeBaseMaterialVO;
import com.daddylab.msaiagent.service.bailian.BaiLianFileService;
import com.daddylab.msaiagent.service.material.KnowledgeBaseService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.K;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class KnowledgeBaseServiceImpl extends ServiceImpl<KnowledgeBaseMaterialMapper, KnowledgeBaseMaterial> implements KnowledgeBaseService {
    @Autowired
    private KnowledgeBaseMaterialDao knowledgeBaseMaterialDao;
    @Autowired
    private BaiLianFileService baiLianFileService;
    @Autowired
    private OssGateway ossGateway;

    @Override
    public PageResponse<KnowledgeBaseMaterialPageVO> pageQuery(KnowledgeBaseMaterialQuery query) {
        LambdaQueryWrapper<KnowledgeBaseMaterial> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(query.getMaterialName()), KnowledgeBaseMaterial::getMaterialName, query.getMaterialName())
                .like(StrUtil.isNotBlank(query.getMaterialType()), KnowledgeBaseMaterial::getMaterialType, query.getMaterialType())
                .eq(Objects.nonNull(query.getIsEnabled()), KnowledgeBaseMaterial::getIsEnabled, query.getIsEnabled())
                .ge(Objects.nonNull(query.getCreatedAtStart()) && query.getCreatedAtStart() != 0, KnowledgeBaseMaterial::getCreatedAt, query.getCreatedAtStart())
                .le(Objects.nonNull(query.getCreatedAtEnd()) && query.getCreatedAtEnd() != 0, KnowledgeBaseMaterial::getCreatedAt, query.getCreatedAtEnd())
                .orderByDesc(KnowledgeBaseMaterial::getCreatedAt);
        PageInfo<KnowledgeBaseMaterial> pageInfo = PageHelper.startPage(query.getPageIndex(), query.getPageSize())
                .doSelectPageInfo(() -> baseMapper.selectList(queryWrapper));
        List<KnowledgeBaseMaterialPageVO> voList = knowledgeBaseMaterialDao.list(queryWrapper).stream()
                .map(KnowledgeBaseConvert.INSTANCE::toPageVO)
                .collect(Collectors.toList());
        return PageResponse.of(voList, (int) pageInfo.getTotal(), query.getPageSize(), query.getPageIndex());
    }

    @Override
    public String add(KnowledgeBaseMaterialAddForm form) {
        List<String> originalFileUrl = List.of();
        if(Objects.nonNull(form.getOriginalFileUrl()) && !form.getOriginalFileUrl().isEmpty()) {
            originalFileUrl = form.getOriginalFileUrl();
        }
        if(Objects.nonNull(form.getOriginalContent()) && !form.getOriginalContent().isBlank()){
            //如果是富文本，需要创建PDF文档，上传到OSS
            byte[] pdfBytes = PdfUtil.generatePdfFromHtml(form.getOriginalContent());
            String ossPath = String.format("/tech/pdf/%s/%s.pdf",
                    DatePattern.PURE_DATE_FORMAT.format(new Date()),
                    DateUtil.format(new DateTime(), "HHmmssSSS"));
            InputStream inputStream =  new ByteArrayInputStream(pdfBytes);
            String ossUrl = ossGateway.put(ossPath, inputStream);
            originalFileUrl.add(ossUrl);
        }
        //上传到百炼
        baiLianFileService.batchUploadFiles(BaiLianFileTypeEnum.KNOWLEDGE_BASE_MATERIAL, originalFileUrl);
        KnowledgeBaseMaterial material = new KnowledgeBaseMaterial();
        String uuid = RandomUtil.randomStringUpper(4)+"-"+RandomUtil.randomNumbers(6);
        material.setMaterialUuid(uuid);
        material.setMaterialName(form.getMaterialName());
        material.setDescription(form.getDescription());
        material.setOriginalFileUrl(originalFileUrl.stream().collect(Collectors.joining(",")));
        material.setOriginalContent(form.getOriginalContent());
        saveOrUpdate(material);
        return uuid;
    }


    @Override
    public Boolean edit(KnowledgeBaseMaterialEditForm form) {
        KnowledgeBaseMaterial material = getInfo(form.getMaterialUuid());
        Assert.notNull(material, "知识库素材不存在");
        //fixField加个黑名单，id/uuid/name不能修改，根据fixField动态修改FixContent
        Set<String> blacklist = Set.of("id", "materialUuid", "materialName","createdAt","createdUid", "updatedAt", "updatedUid","isEnabled","isDel","DeletedAt");
        if (blacklist.contains(form.getFixField())) {
            throw new IllegalArgumentException("该字段不允许修改");
        }
        // 通过反射动态设置字段
        try {
            Field field = KnowledgeBaseMaterial.class.getDeclaredField(form.getFixField());
            field.setAccessible(true);
            Class<?> type = field.getType();
            Object value = form.getFixContent();
            if (type != String.class && value instanceof String) {
                if (type == Integer.class || type == int.class) {
                    value = Integer.valueOf((String) value);
                } else if (type == Long.class || type == long.class) {
                    value = Long.valueOf((String) value);
                }
            }
            field.set(material, value);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        saveOrUpdate(material);
        return true;
    }

    @Override
    public Boolean fix(KnowledgeBaseMaterialFixForm form) {

        return true;
    }

    @Override
    public KnowledgeBaseMaterialVO detail(String uuid) {
        KnowledgeBaseMaterial material = getInfo(uuid);
        Assert.notNull(material, "知识库素材不存在");
        return KnowledgeBaseConvert.INSTANCE.toVO(material);
    }


    @Override
    public Boolean updateByAi(KnowledgeBaseMaterialAIForm form) {
        KnowledgeBaseMaterial knowledgeBaseMaterial = knowledgeBaseMaterialDao.getByUuid(form.getMaterialUuid());
        Assert.notNull(knowledgeBaseMaterial);
        return null;
    }

    @Override
    public KnowledgeBaseMaterial getInfo(String uuid) {
        return knowledgeBaseMaterialDao.getByUuid(uuid);
    }

    @Override
    public Boolean enable(String uuid, Integer isEnabled) {
        KnowledgeBaseMaterial knowledgeBaseMaterial = getInfo(uuid);
        if(knowledgeBaseMaterial == null) {
            log.warn("知识库素材没找到,uuid={}", uuid);
            return false;
        }
        knowledgeBaseMaterial.setIsEnabled(isEnabled);
        return knowledgeBaseMaterialDao.updateById(knowledgeBaseMaterial);
    }

}
