package com.daddylab.msaiagent.service.material.impl;

import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.URLUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.msaiagent.common.bailian.BaiLianService;
import com.daddylab.msaiagent.common.utils.IdUtils;
import com.daddylab.msaiagent.common.utils.JsonUtil;
import com.daddylab.msaiagent.common.utils.PageUtils;
import com.daddylab.msaiagent.db.aiAgent.dao.ImageMaterialDao;
import com.daddylab.msaiagent.db.aiAgent.entity.BailianFile;
import com.daddylab.msaiagent.db.aiAgent.entity.ImageMaterial;
import com.daddylab.msaiagent.db.aiAgent.enums.BaiLianFileTypeEnum;
import com.daddylab.msaiagent.db.base.Entity;
import com.daddylab.msaiagent.domain.query.ImageMaterialQuery;
import com.daddylab.msaiagent.domain.request.ImageMaterialBatchDeleteRequest;
import com.daddylab.msaiagent.domain.request.ImageMaterialUploadRequest;
import com.daddylab.msaiagent.service.OssService;
import com.daddylab.msaiagent.service.bailian.BaiLianFileService;
import com.daddylab.msaiagent.service.material.ImageMaterialService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class ImageMaterialServiceImpl implements ImageMaterialService {

    @Autowired private ImageMaterialDao imageMaterialDao;
    @Autowired private BaiLianFileService baiLianFileService;
    @Autowired private BaiLianService baiLianService;
    @Autowired private OssService ossService;

    @Override
    public PageUtils.PageResponse<ImageMaterial> list(ImageMaterialQuery queryParams) {
        // 构建查询条件
        Page<ImageMaterial> page = queryParams.page();
        imageMaterialDao
                .lambdaQuery()
                .eq(ImageMaterial::getMaterialUuid, queryParams.getMaterialUuid())
                .like(ImageMaterial::getMaterialName, queryParams.getMaterialName())
                .ge(ImageMaterial::getCreatedAt, queryParams.getCreateTimeStart())
                .le(ImageMaterial::getCreatedAt, queryParams.getCreateTimeEnd())
                .page(page);
        return PageUtils.PageResponse.of(page.getTotal(), page.getRecords(), (int) page.getSize());
    }

    @Override
    public List<ImageMaterial> upload(ImageMaterialUploadRequest request) {
        List<ImageMaterial> imageMaterials = new ArrayList<>();
        for (String ossUrl : request.getOssUrlList()) {
            ossUrl = URLUtil.decode(ossUrl);
            ImageMaterial imageMaterial = new ImageMaterial();
            imageMaterial.setMaterialUuid(IdUtils.generate());
            imageMaterial.setMaterialName(FileNameUtil.getPrefix(ossUrl));
            imageMaterial.setOriginalFileUrl(ossUrl);
            imageMaterial.setMetadata(JsonUtil.toJSONString(ossService.imageInfo(ossUrl)));
            imageMaterial.setRemoteFileId("");
            imageMaterial.setIndexStatus(0);
            imageMaterials.add(imageMaterial);
        }
        imageMaterialDao.saveBatch(imageMaterials);
        for (ImageMaterial imageMaterial : imageMaterials) {
            Thread.startVirtualThread(() -> {
                BailianFile bailianFile =
                        baiLianFileService.uploadFile(
                                BaiLianFileTypeEnum.IMAGE_MATERIAL,
                                imageMaterial.getId(),
                                imageMaterial.getOriginalFileUrl(),
                                FileNameUtil.getName(imageMaterial.getOriginalFileUrl()),
                                null);
                baiLianService.syncFileIdToIndex(bailianFile.getFileId(), bailianFile.getIndexId())
                imageMaterial.setRemoteFileId(bailianFile.getFileId());
                imageMaterialDao.updateById(imageMaterial);
            });
        }
        return imageMaterials;
    }

    @Override
    public Boolean batchDelete(ImageMaterialBatchDeleteRequest request) {
        List<ImageMaterial> imageMaterials = imageMaterialDao.listByIds(request.getIds());
        if (imageMaterials.isEmpty()) {
            return true;
        }
        imageMaterialDao.removeByIds(imageMaterials.stream().map(Entity::getId).toList());
        List<String> baiLianFileIds =
                imageMaterials.stream().map(ImageMaterial::getRemoteFileId).toList();
        List<BailianFile> bailianFiles = baiLianFileService.listByFileIds(baiLianFileIds);
        if (bailianFiles.isEmpty()) {
            return true;
        }
        for (BailianFile bailianFile : bailianFiles) {
            baiLianFileService.deleteAll(bailianFile);
        }
        return true;
    }

    @Override
    public ImageMaterial getById(Long id) {
        return imageMaterialDao.getById(id);
    }

    @Override
    public ImageMaterial getByUuid(String uuid) {
        return imageMaterialDao.lambdaQuery()
                .eq(ImageMaterial::getMaterialUuid, uuid)
                .one();
    }

    @Override
    public Boolean updateById(ImageMaterial imageMaterial) {
        return imageMaterialDao.updateById(imageMaterial);
    }

    @Override
    public Boolean removeById(Long id) {
        ImageMaterial imageMaterial = imageMaterialDao.getById(id);
        if (imageMaterial == null) {
            return true;
        }

        // 删除数据库记录
        boolean removed = imageMaterialDao.removeById(id);

        // 删除关联的百炼文件
        if (removed && imageMaterial.getRemoteFileId() != null && !imageMaterial.getRemoteFileId().isEmpty()) {
            List<BailianFile> bailianFiles = baiLianFileService.listByFileIds(List.of(imageMaterial.getRemoteFileId()));
            for (BailianFile bailianFile : bailianFiles) {
                baiLianFileService.deleteAll(bailianFile);
            }
        }

        return removed;
    }
}
