package com.daddylab.msaiagent.service.material;

import com.alibaba.cola.dto.PageResponse;
import com.daddylab.msaiagent.db.aiAgent.entity.KnowledgeBaseMaterial;
import com.daddylab.msaiagent.domain.form.KnowledgeBaseMaterialAIForm;
import com.daddylab.msaiagent.domain.form.KnowledgeBaseMaterialAddForm;
import com.daddylab.msaiagent.domain.form.KnowledgeBaseMaterialEditForm;
import com.daddylab.msaiagent.domain.form.KnowledgeBaseMaterialFixForm;
import com.daddylab.msaiagent.domain.query.KnowledgeBaseMaterialQuery;
import com.daddylab.msaiagent.domain.vo.KnowledgeBaseMaterialPageVO;
import com.daddylab.msaiagent.domain.vo.KnowledgeBaseMaterialVO;

public interface KnowledgeBaseService {

    /**
     * 分页查询
     */
    PageResponse<KnowledgeBaseMaterialPageVO> pageQuery(KnowledgeBaseMaterialQuery query);

    /**
     * 素材创建
     */
    String add(KnowledgeBaseMaterialAddForm form);

    /**
     * 素材编辑
     */
    Boolean edit(KnowledgeBaseMaterialEditForm form);

    /**
     * 素材修正
     */
    Boolean fix(KnowledgeBaseMaterialFixForm form);

    /**
     * 素材详情
     */
    KnowledgeBaseMaterialVO detail(String uuid);

    /**
     * AI完善知识库
     */
    Boolean updateByAi(KnowledgeBaseMaterialAIForm form);

    /**
     * 获取知识库信息
     */
    KnowledgeBaseMaterial getInfo(String uuid);

    /**
     * 启用或禁用知识库素材
     */
    Boolean enable(String uuid, Integer isEnabled);
}
