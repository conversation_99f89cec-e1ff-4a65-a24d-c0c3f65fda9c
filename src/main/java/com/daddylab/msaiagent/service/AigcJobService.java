package com.daddylab.msaiagent.service;

import com.daddylab.msaiagent.db.aiAgent.entity.AigcJob;

import java.util.List;
import java.util.function.Consumer;

public interface AigcJobService {

    AigcJob createJob(Long bizId, String jobType,Long chatId, Object jobData);

    AigcJob getFirstJob(Long bizId, String jobType);

    AigcJob getJob(Long jobId);

    void startJobAsync(AigcJob job, String promptStr, Consumer<String> callback);

    void jobError(Long jobId, String error);

    void jobUpdateAiInfo(Long jobId, List<String> clarificationQuestions,List<String> updateNotes,Object otherData);
}
