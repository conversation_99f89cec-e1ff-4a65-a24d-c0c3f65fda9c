package com.daddylab.msaiagent.service.scene.impl;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.msaiagent.common.utils.JsonUtil;
import com.daddylab.msaiagent.convert.SceneConvert;
import com.daddylab.msaiagent.db.aiAgent.dao.SceneFragmentDao;
import com.daddylab.msaiagent.db.aiAgent.entity.AigcJob;
import com.daddylab.msaiagent.db.aiAgent.entity.PersonaCore;
import com.daddylab.msaiagent.db.aiAgent.entity.SceneFragment;
import com.daddylab.msaiagent.domain.constants.ChatTypeConst;
import com.daddylab.msaiagent.domain.dto.SceneDetailDto;
import com.daddylab.msaiagent.domain.dto.SceneGeneratorAiRespDto;
import com.daddylab.msaiagent.domain.enums.PersonSubjectTypeEnum;
import com.daddylab.msaiagent.domain.form.SceneFragmentGeneratorForm;
import com.daddylab.msaiagent.domain.form.SceneUpdateByAiForm;
import com.daddylab.msaiagent.domain.form.SceneUpdateForm;
import com.daddylab.msaiagent.domain.vo.AiJobVO;
import com.daddylab.msaiagent.domain.vo.PersonInfoVO;
import com.daddylab.msaiagent.domain.vo.ProductInfoVO;
import com.daddylab.msaiagent.domain.vo.SceneInfoVO;
import com.daddylab.msaiagent.prompt.SceneGeneratePrompt;
import com.daddylab.msaiagent.service.AigcJobService;
import com.daddylab.msaiagent.service.material.ProductService;
import com.daddylab.msaiagent.service.person.PersonService;
import com.daddylab.msaiagent.service.scene.SceneFragmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;

@Service
@Slf4j
public class SceneFragmentServiceImpl implements SceneFragmentService {

    @Autowired
    SceneFragmentDao sceneFragmentDao;
    @Autowired
    PersonService personService;
    @Autowired
    ProductService productService;
    @Autowired
    private AigcJobService aigcJobService;

    @Override
    public AiJobVO generateByAi(SceneFragmentGeneratorForm form) {

        SceneFragment material = new SceneFragment();
        material.setFragmentUuid(RandomUtil.randomStringUpper(4)+"-"+RandomUtil.randomNumbers(6));
        sceneFragmentDao.save(material);
        AigcJob job = aigcJobService.createJob(material.getId(), ChatTypeConst.AI_GENERATOR_PRODUCT_SCENE, 0L, form);
        String subjectTypeName = "COMMON(通用)";
        String subjectTypeCode = "0";
        String personaName = "无";
        String personaSummary = "{}";
        String assets = getAssets(form);
        if(null != form.getUid() && form.getUid() > 0) {
            PersonInfoVO person = personService.getPersonInfo(form.getUid());
            if(null != person){
                PersonSubjectTypeEnum subject = PersonSubjectTypeEnum.fromCode(person.getCore().getSubjectType());
                if (null != subject) {
                    subjectTypeName = subject.name();
                    subjectTypeCode = subject.getCode() + "";
                }
                if(subject == PersonSubjectTypeEnum.INDIVIDUAL){
                    personaName = Optional.ofNullable(person.getPersonaAttributes().get("profile.full_name_virtual")).map(Object::toString).orElse("无");
                }else {
                    personaName = Optional.ofNullable(person.getPersonaAttributes().get("identity.organization_name")).map(Object::toString).orElse("无");
                }
                personaSummary = JsonUtil.toJSONString(person.getPersonaAttributes());
            }
        }
        String template = SceneGeneratePrompt.GENERATE;
        template = template.replace("[target_platform_hint]", "小红书")
                .replace("[subject_type_name]", "subject_type_name:" + subjectTypeName)
                .replace("[subject_type_code]", "subject_type_code:" + subjectTypeCode)
                .replace("[init_question_answer]", getInitQuestionAnswer(form.getInitQuestionAndAnswer()))
                .replace("[selected_persona_name]", personaName)
                .replace("[selected_persona_core_profile_summary_json]", personaSummary)
                .replace("[assets]", assets)
                .replace("[task_description]", (form.getTaskDescription() == null || form.getTaskDescription().isEmpty()) ? "无" : form.getTaskDescription())
                .replace("[free_input_scene_requirements]", (form.getFreeText() == null || form.getFreeText().isEmpty()) ? "无" : form.getFreeText());
        aigcJobService.startJobAsync(job,template,content-> saveScene(job.getId(), content, material));
        return AiJobVO.fromJob(job);
    }

    @Override
    public AiJobVO updateByAi(SceneUpdateByAiForm form) {
        SceneFragment material = sceneFragmentDao.getById(form.getId());
        if(null == material){
            return null;
        }
        AigcJob job = aigcJobService.createJob(material.getId(), ChatTypeConst.AI_UPDATE_SCENE, 0L, form);
        String subjectTypeName = "COMMON(通用)";
        String subjectTypeCode = "0";
        AigcJob firstJob = aigcJobService.getFirstJob(material.getId(), ChatTypeConst.AI_GENERATOR_SCENE);
        if(null != firstJob){
            SceneFragmentGeneratorForm generatorForm = JsonUtil.parseObject(firstJob.getJobData(), SceneFragmentGeneratorForm.class);
            if(null != generatorForm){
                if(null != generatorForm.getUid() && generatorForm.getUid() > 0) {
                    PersonaCore person = personService.getPerson(generatorForm.getUid());
                    if(null != person){
                        PersonSubjectTypeEnum subject = PersonSubjectTypeEnum.fromCode(person.getSubjectType());
                        if (null != subject) {
                            subjectTypeName = subject.name();
                            subjectTypeCode = subject.getCode() + "";
                        }
                    }
                }
            }
        }
        String template = SceneGeneratePrompt.GENERATE;
        template = template.replace("[target_platform_hint]", "小红书")
                .replace("[subject_type_name]", "subject_type_name:" + subjectTypeName)
                .replace("[subject_type_code]", "subject_type_code:" + subjectTypeCode)
                .replace("[init_question_answer]", getInitQuestionAnswer(form.getUserAnswer()))
                .replace("[user_additional_free_input]", form.getUserAdditionInfo())
                .replace("[current_scene_description_text]", JsonUtil.toJSONString(SceneConvert.INSTANCE.fragmentToDto(material)));
        aigcJobService.startJobAsync(job,template,content-> saveScene(job.getId(), content, material));
        return AiJobVO.fromJob(job);
    }

    @Override
    public Long updateInfo(SceneUpdateForm form) {
        SceneFragment material = sceneFragmentDao.getById(form.getId());
        if (null == material) {
            log.error("场景片段不存在: {}", form.getId());
            return form.getId();
        }
        SceneConvert.INSTANCE.updateFragmentFromDto(form.getDetail(), material);
        sceneFragmentDao.updateById(material);
        return form.getId();
    }

    @Override
    public SceneInfoVO getSceneDetail(Long id) {
        SceneFragment sceneFragment = sceneFragmentDao.getById(id);
        if (null == sceneFragment) {
            log.error("场景片段不存在: {}", id);
            return null;
        }

        SceneInfoVO sceneInfoVO = new SceneInfoVO();
        SceneDetailDto sceneDetail = SceneConvert.INSTANCE.fragmentToDto(sceneFragment);
        sceneInfoVO.setDetail(sceneDetail);
        sceneInfoVO.setId(sceneFragment.getId());
        if(null != sceneFragment.getJobId()){
            AigcJob job = aigcJobService.getJob(sceneFragment.getJobId());
            if(null != job){
                sceneInfoVO.setAiQuestions(JsonUtil.parseObjectList(job.getAiQuestions(),String.class));
                sceneInfoVO.setUpdateNotes(JsonUtil.parseObjectList(job.getUpdateNotes(),String.class));
            }
        }
        return sceneInfoVO;
    }

    @Override
    public IPage<SceneInfoVO> getSceneList(int pageNum, int pageSize) {
        LambdaQueryChainWrapper<SceneFragment> query = sceneFragmentDao.lambdaQuery();
        Page<SceneFragment> records = sceneFragmentDao.page(new Page<>(pageNum, pageSize), query);
        return records.convert(material->{
            SceneInfoVO sceneInfoVO = new SceneInfoVO();
            SceneDetailDto sceneDetail = SceneConvert.INSTANCE.fragmentToDto(material);
            sceneInfoVO.setDetail(sceneDetail);
            sceneInfoVO.setId(material.getId());
            return sceneInfoVO;
        });
    }

    @Override
    public void sceneArchived(Long id) {
        SceneFragment sceneFragment = sceneFragmentDao.getById(id);
        if (null == sceneFragment) {
            log.error("场景片段不存在，无法归档: {}", id);
            return;
        }
        sceneFragment.setIsEnabled(0);
        sceneFragmentDao.updateById(sceneFragment);
    }

    @Override
    public void parseDetailByChatRecord(Long id) {

    }

    private String getInitQuestionAnswer(Map<String, String> initQuestionAndAnswer) {
        if (null == initQuestionAndAnswer || initQuestionAndAnswer.isEmpty()) {
            return "无";
        }
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : initQuestionAndAnswer.entrySet()) {
            sb.append("*    **").append(entry.getKey()).append(":**").append("\n").append("```text\n").append(entry.getValue()).append("\n```").append("\n");
        }
        return sb.toString();
    }

    private String getAssets(SceneFragmentGeneratorForm form) {
        StringBuilder assets = new StringBuilder();
        int assetsCount = 0;
        if(null != form.getProducts() && !form.getProducts().isEmpty()) {
            for (int i = 0; i < form.getProducts().size(); i++) {
                ProductInfoVO product = productService.getProductDetail(form.getProducts().get(i));
                if(null == product){
                    continue;
                }
                assetsCount += 1;
                assets.append("素材").append(assetsCount).append(":\n");
                assets.append("`asset_type`: \"PRODUCT\"\n")
                        .append("`asset_name`: \"").append(product.getProductDetail().getProductName()).append("\"\n")
                        .append("`asset_core_data_summary_json`: \"").append(JsonUtil.toJSONString(product.getProductDetail())).append("\"\n");
            }
        }
        //fixme 如果有其他类型的素材（如知识、事件等），可以在这里添加类似的逻辑
        return assetsCount > 0 ? assets.toString() : "无";
    }

    private void saveScene(Long jobId, String content, SceneFragment material) {
        try {
            log.info("ai 返回场景参数的信息，开始整合:{}", material.getFragmentUuid());
            SceneGeneratorAiRespDto dto = JsonUtil.parseObject(content, SceneGeneratorAiRespDto.class);
            if (null == dto) {
                log.error("ai返回的不是约定格式的json数据");
                aigcJobService.jobError(jobId, "ai返回的不是约定格式的json数据");
                return;
            }
            SceneDetailDto sceneFragment = dto.getGeneratedSceneFragmentDraft();
            if(null == sceneFragment){
                sceneFragment = dto.getUpdatedSceneDescription();
            }
            if(null != sceneFragment){
                // 使用MapStruct自动映射非空字段
                SceneConvert.INSTANCE.updateFragmentFromDto(sceneFragment, material);
            }
            // 保存更新后的material到数据库
            material.setJobId(jobId);
            sceneFragmentDao.updateById(material);
            log.info("ai 返回场景参数的信息，整合完毕:{}", material.getId());
            sceneFragmentDao.updateById(material);
            aigcJobService.jobUpdateAiInfo(jobId, dto.getClarificationQuestions(), dto.getSuggestionsForNextIteration(), null);
        } catch (Exception e) {
            log.error("解析ai返回的数据异常{},{}",material.getId(), e.getMessage());
            aigcJobService.jobError(jobId, "解析ai返回的数据异常");
        }
    }
}
