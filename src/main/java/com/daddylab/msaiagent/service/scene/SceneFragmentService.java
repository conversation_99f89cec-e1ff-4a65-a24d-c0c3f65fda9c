package com.daddylab.msaiagent.service.scene;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.daddylab.msaiagent.domain.form.SceneFragmentGeneratorForm;
import com.daddylab.msaiagent.domain.form.SceneUpdateByAiForm;
import com.daddylab.msaiagent.domain.form.SceneUpdateForm;
import com.daddylab.msaiagent.domain.vo.AiJobVO;
import com.daddylab.msaiagent.domain.vo.SceneInfoVO;

public interface SceneFragmentService {

    AiJobVO generateByAi(SceneFragmentGeneratorForm form);

    AiJobVO updateByAi(SceneUpdateByAiForm form);

    Long updateInfo(SceneUpdateForm form);

    SceneInfoVO getSceneDetail(Long id);

    IPage<SceneInfoVO> getSceneList(int pageNum, int pageSize);

    void sceneArchived(Long id);

    void parseDetailByChatRecord(Long id);
}
