package com.daddylab.msaiagent.service.bailian;


import com.daddylab.msaiagent.db.aiAgent.enums.BaiLianIndexDocumentStatusEnum;
import com.daddylab.msaiagent.domain.request.BaiLianRemoveRemoteIndexDocsRequest;
import com.daddylab.msaiagent.domain.request.BaiLianSyncIndexDocumentRequest;

/**
 * <AUTHOR>
 */
public interface BaiLianIndexDocService {
  void runSyncIndexDocuments();

  void runSyncIndexDocuments(BaiLianSyncIndexDocumentRequest request);

  BaiLianIndexDocumentStatusEnum indexDocStatus(String indexId, String fileId);

  void syncSubmitIndexJobStatus();

  void processDocumentError(int num);

  void removeRemoteIndexDocs(BaiLianRemoveRemoteIndexDocsRequest request);
}
