package com.daddylab.msaiagent.service.bailian.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.aliyun.bailian20231229.models.GetIndexJobStatusResponseBody.GetIndexJobStatusResponseBodyData;
import com.aliyun.bailian20231229.models.GetIndexJobStatusResponseBody.GetIndexJobStatusResponseBodyDataDocuments;
import com.daddylab.msaiagent.common.bailian.AliBaiLianConfig;
import com.daddylab.msaiagent.common.bailian.BaiLianService;
import com.daddylab.msaiagent.common.bailian.domain.vo.BaiLianIndexDocVO;
import com.daddylab.msaiagent.db.aiAgent.dao.BailianFileDao;
import com.daddylab.msaiagent.db.aiAgent.dao.BailianIndexDocumentDao;
import com.daddylab.msaiagent.db.aiAgent.entity.BailianFile;
import com.daddylab.msaiagent.db.aiAgent.entity.BailianIndexDocument;
import com.daddylab.msaiagent.db.aiAgent.enums.BaiLianFileTypeEnum;
import com.daddylab.msaiagent.db.aiAgent.enums.BaiLianIndexDocumentStatusEnum;
import com.daddylab.msaiagent.db.base.Entity;
import com.daddylab.msaiagent.domain.request.BaiLianRemoveRemoteIndexDocsRequest;
import com.daddylab.msaiagent.domain.request.BaiLianSyncIndexDocumentRequest;
import com.daddylab.msaiagent.service.bailian.BaiLianFileService;
import com.daddylab.msaiagent.service.bailian.BaiLianIndexDocService;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/3/28
 */
@Service
@Slf4j
public class BaiLianIndexDocServiceImpl implements BaiLianIndexDocService {

  @Autowired private BaiLianService baiLianService;
  @Autowired private BaiLianFileService baiLianFileService;
  @Autowired private BailianIndexDocumentDao bailianIndexDocumentDao;
  @Autowired private AliBaiLianConfig aliBaiLianConfig;
  @Autowired private BailianFileDao bailianFileDao;
  @Autowired private RedissonClient redissonClient;

  @Override
  public void runSyncIndexDocuments() {
    final List<AliBaiLianConfig.BaiLianIndexConfig> indexConfigs = aliBaiLianConfig.getIndexConfigs();
    if (CollUtil.isNotEmpty(indexConfigs)) {
      for (AliBaiLianConfig.BaiLianIndexConfig indexConfig : indexConfigs) {
        if (indexConfig.isSyncIndexDocDisabled()) {
          continue;
        }
        try {
          final BaiLianSyncIndexDocumentRequest request = new BaiLianSyncIndexDocumentRequest();
          request.setIndexId(indexConfig.getIndexId());
          runSyncIndexDocuments(request);
        } catch (Exception e) {
          log.error("[百炼-索引文档]同步异常，indexId={}", indexConfig.getIndexId(), e);
        }
      }
    }
  }

  @Override
  public void runSyncIndexDocuments(BaiLianSyncIndexDocumentRequest request) {
    final long currentTime = DateUtil.currentSeconds();
    String indexId = request.getIndexId();
    log.info("[百炼-索引文档]开始同步，indexId={}", indexId);
    int pageSize = 100;
    baiLianService.foreachIndexDocList(
        indexId,
        documentPage -> {
          log.info(
              "[百炼-索引文档]同步进行中，indexId={}，pageIndex={}，pageSize={}，total={}",
              indexId,
              documentPage.getCurrent(),
              documentPage.getSize(),
              documentPage.getTotal());
          syncIndexDoc(documentPage.getRecords());
        },
        pageSize);
    processDeleted(indexId, currentTime);
    log.info("[百炼-索引文档]同步完成，indexId={}", indexId);
  }

  private void processDeleted(String indexId, long currentTime) {
    bailianIndexDocumentDao
        .lambdaUpdate()
        .eq(BailianIndexDocument::getIndexId, indexId)
        .lt(Entity::getUpdatedAt, currentTime)
        .remove();
  }

  private void syncIndexDoc(List<BaiLianIndexDocVO> baiLianIndexDocVOS) {
    // 获取文件ID列表
    List<String> fileIds =
        baiLianIndexDocVOS.stream().map(BaiLianIndexDocVO::getFileId).collect(Collectors.toList());

    // 一次性查询所有已存在的文档
    List<BailianIndexDocument> existingDocs =
        bailianIndexDocumentDao.lambdaQuery().in(BailianIndexDocument::getFileId, fileIds).list();

    // 构建文件ID到现有文档的映射
    Map<String, List<BailianIndexDocument>> fileIdToDocumentsMap =
        existingDocs.stream().collect(Collectors.groupingBy(BailianIndexDocument::getFileId));

    // 区分需要更新和新增的记录
    List<BailianIndexDocument> toUpdate = new ArrayList<>();
    List<BailianIndexDocument> toInsert = new ArrayList<>();

    for (BaiLianIndexDocVO docVO : baiLianIndexDocVOS) {
      List<BailianIndexDocument> theExistingDocs =
          fileIdToDocumentsMap.getOrDefault(docVO.getFileId(), Collections.emptyList());
      final List<BailianIndexDocument> thisIndexExitingDocs =
          theExistingDocs.stream()
              .filter(doc -> doc.getIndexId().equals(docVO.getIndexId()))
              .collect(Collectors.toList());
      if (!thisIndexExitingDocs.isEmpty()) {
        thisIndexExitingDocs.forEach(entity -> copyDocProperties(docVO, entity));
        toUpdate.addAll(thisIndexExitingDocs);
      } else {
        BailianIndexDocument document = convertToDocumentEntity(docVO);
        toInsert.add(document);
      }
    }

    // 批量更新和插入
    if (!toUpdate.isEmpty()) {
      bailianIndexDocumentDao.updateBatchById(toUpdate);
      log.info("[百炼-索引文档]更新{}", toUpdate.size());
    }
    if (!toInsert.isEmpty()) {
      bailianIndexDocumentDao.saveBatch(toInsert);
      log.info("[百炼-索引文档]新增{}", toInsert.size());
    }

    // 更新文件引用计数
    updateFileRefCounts(fileIdToDocumentsMap);

    // 清理业务上的重复文件
    cleanDuplicateFiles(fileIds);
  }

  private void updateFileRefCounts(Map<String, List<BailianIndexDocument>> existingDocsMap) {
    // 按照 refCount 分组收集需要更新的数据
    Map<Long, List<String>> refCountMap =
        existingDocsMap.entrySet().stream()
            .collect(
                Collectors.groupingBy(
                    entry ->
                        entry.getValue().stream()
                            .filter(
                                doc ->
                                    Objects.equals(doc.getStatus(), "RUNNING")
                                        || Objects.equals(doc.getStatus(), "INSERT_ERROR"))
                            .count(),
                    Collectors.mapping(Map.Entry::getKey, Collectors.toList())));

    // 批量更新每组 refCount 相同的文件
    refCountMap.forEach(
        (refCount, fileIdsOfRefCount) -> {
          bailianFileDao.updateRefCount(fileIdsOfRefCount, refCount);
        });
  }

  /**
   * 清理重复文件
   *
   * @param fileIds 文件ID
   */
  private void cleanDuplicateFiles(List<String> fileIds) {
    if (CollUtil.isEmpty(fileIds)) {
      return;
    }
    // 获取所有相关文件
    final List<BailianFile> bailianFiles =
        bailianFileDao.lambdaQuery().in(BailianFile::getFileId, fileIds).list();

    // 按文件类型分组
    Map<BaiLianFileTypeEnum, List<BailianFile>> typeGroupedFiles =
        bailianFiles.stream().collect(Collectors.groupingBy(BailianFile::getType));

    // 批量处理每种类型的文件
    for (Map.Entry<BaiLianFileTypeEnum, List<BailianFile>> entry : typeGroupedFiles.entrySet()) {
      BaiLianFileTypeEnum type = entry.getKey();
      List<BailianFile> filesOfType = entry.getValue();

      // 获取该类型下所有的业务ID
      List<Long> typeIds =
          filesOfType.stream().map(BailianFile::getTypeId).distinct().collect(Collectors.toList());

      // 批量查询该类型下所有业务ID关联的文件
      List<BailianFile> allRelatedFiles = bailianFileDao.listByTypeIds(type, typeIds);

      // 按业务ID分组处理
      Map<Long, List<BailianFile>> typeIdGroupedFiles =
          allRelatedFiles.stream().collect(Collectors.groupingBy(BailianFile::getTypeId));

      List<BailianFile> filesToDelete = new ArrayList<>();

      // 处理每个业务ID下的重复文件
      for (List<BailianFile> relatedFiles : typeIdGroupedFiles.values()) {
        if (relatedFiles.size() > 1) {
          // 获取最新的文件
          BailianFile latestFile =
              relatedFiles.stream().max(Comparator.comparing(Entity::getId)).get();

          // 标记删除其他文件
          relatedFiles.stream()
              .filter(
                  file ->
                      !Objects.equals(file.getFileId(), latestFile.getFileId())
                          && file.getDeleteFlag() == 0)
              .forEach(
                  file -> {
                    // 收集需要删除的文件
                    BailianFile bailianFileUO = new BailianFile();
                    bailianFileUO.setId(file.getId());
                    bailianFileUO.setDeleteFlag(1);
                    bailianFileUO.setUpdatedAt(DateUtil.currentSeconds());
                    filesToDelete.add(bailianFileUO);
                  });
        }
      }

      // 批量更新删除标记
      if (!filesToDelete.isEmpty()) {
        bailianFileDao.updateBatchById(filesToDelete);
      }
    }
  }

  private BailianIndexDocument convertToDocumentEntity(BaiLianIndexDocVO doc) {
    BailianIndexDocument document = new BailianIndexDocument();
    copyDocProperties(doc, document);
    return document;
  }

  private static void copyDocProperties(BaiLianIndexDocVO docVO, BailianIndexDocument docEntity) {
    docEntity.setFileId(docVO.getFileId());
    docEntity.setIndexId(docVO.getIndexId());
    docEntity.setSourceId(docVO.getSourceId());
    docEntity.setName(docVO.getName());
    docEntity.setSize(docVO.getSize());
    docEntity.setDocumentType(docVO.getDocumentType());
    docEntity.setStatus(docVO.getStatus());
    docEntity.setCode(docVO.getCode());
    docEntity.setMessage(docVO.getMessage());
  }

  @Override
  public BaiLianIndexDocumentStatusEnum indexDocStatus(String indexId, String fileId) {
    BailianIndexDocument bailianIndexDocument =
        bailianIndexDocumentDao
            .lambdaQuery()
            .eq(BailianIndexDocument::getIndexId, indexId)
            .eq(BailianIndexDocument::getFileId, fileId)
            .one();
    if (bailianIndexDocument == null) {
      return null;
    }
    return BaiLianIndexDocumentStatusEnum.valueOf(bailianIndexDocument.getStatus());
  }

  @Override
  public void syncSubmitIndexJobStatus() {
//    final List<BailianSubmitIndexJob> list =
//        bailianSubmitIndexJobDao
//            .lambdaQuery()
//            .in(BailianSubmitIndexJob::getJobStatus, "RUNNING", "PENDING")
//            .list();
//    if (CollUtil.isEmpty(list)) {
//      return;
//    }
//    for (BailianSubmitIndexJob bailianSubmitIndexJob : list) {
//      baiLianService.getIndexJobStatus(
//          bailianSubmitIndexJob.getIndexId(),
//          bailianSubmitIndexJob.getJobId(),
//          1000,
//          bailianSubmitIndexJob1 -> updateJobStatus(bailianSubmitIndexJob, bailianSubmitIndexJob1));
//    }
  }

  @Override
  public void processDocumentError(int num) {
    final List<BailianIndexDocument> bailianIndexDocuments =
        bailianIndexDocumentDao
            .lambdaQuery()
            .eq(
                BailianIndexDocument::getStatus,
                BaiLianIndexDocumentStatusEnum.INSERT_ERROR.getValue())
            .last("limit " + num)
            .list();
    if (CollUtil.isEmpty(bailianIndexDocuments)) {
      return;
    }
    for (BailianIndexDocument bailianIndexDocument : bailianIndexDocuments) {
      final RAtomicLong counter =
          redissonClient.getAtomicLong("bailian:retry:" + bailianIndexDocument.getName());
      counter.expire(Duration.ofDays(1));
      final long retryNum = counter.incrementAndGet();
      final boolean abandonRetry = retryNum > 3;
      log.info(
          abandonRetry
              ? "[百炼-索引文档]超过最大重试次数，indexId={}, fileId={}，name={}，重试次数={}"
              : "[百炼-索引文档]重试导入失败的文档，indexId={}, fileId={}，name={}，重试次数={}",
          bailianIndexDocument.getIndexId(),
          bailianIndexDocument.getFileId(),
          bailianIndexDocument.getName(),
          retryNum,
          abandonRetry);
      if (abandonRetry) {
        bailianIndexDocument.setStatus(
            BaiLianIndexDocumentStatusEnum.INSERT_ERROR_FINAL.getValue());
        bailianIndexDocumentDao.updateById(bailianIndexDocument);
      } else {
        baiLianFileService.reUpload(bailianIndexDocument.getFileId());
        bailianIndexDocument.setStatus(
            BaiLianIndexDocumentStatusEnum.INSERT_ERROR_RETRY.getValue());
        bailianIndexDocumentDao.updateById(bailianIndexDocument);
      }
    }
  }

  @Override
  public void removeRemoteIndexDocs(BaiLianRemoveRemoteIndexDocsRequest request) {
    final List<String> fileIds = request.getFileIds();
    if (CollUtil.isNotEmpty(fileIds)) {
      baiLianService.deleteIndexDoc(request.getIndexId(), fileIds);
    } else {
      final List<BailianIndexDocument> bailianIndexDocuments =
          bailianIndexDocumentDao.listByCategoryId(request.getIndexId(), request.getCategoryId());
      if (CollUtil.isNotEmpty(bailianIndexDocuments)) {
        CollUtil.split(bailianIndexDocuments, 100)
            .forEach(
                sub -> {
                  baiLianService.deleteIndexDoc(
                      request.getIndexId(),
                      sub.stream()
                          .map(BailianIndexDocument::getFileId)
                          .collect(Collectors.toList()));
                });
      }
    }
  }

//  private void updateJobStatus(
//      BailianSubmitIndexJob bailianSubmitIndexJob,
//      GetIndexJobStatusResponseBodyData getIndexJobStatusResponseBodyData) {
//    log.info(
//        "[百炼-索引文档]更新索引任务状态，indexId={}，jobId={}, status={}, documents size={}",
//        bailianSubmitIndexJob.getIndexId(),
//        bailianSubmitIndexJob.getJobId(),
//        getIndexJobStatusResponseBodyData.getStatus(),
//        getIndexJobStatusResponseBodyData.getDocuments().size());
//    bailianSubmitIndexJob.setJobStatus(getIndexJobStatusResponseBodyData.getStatus());
//    final List<GetIndexJobStatusResponseBodyDataDocuments> documents =
//        Optional.ofNullable(getIndexJobStatusResponseBodyData.getDocuments())
//            .orElseGet(Collections::emptyList);
//    final Map<String, List<GetIndexJobStatusResponseBodyDataDocuments>> statusGroupedDocuments =
//        documents.stream()
//            .collect(Collectors.groupingBy(GetIndexJobStatusResponseBodyDataDocuments::getStatus));
//    int numInsertError =
//        statusGroupedDocuments.getOrDefault("INSERT_ERROR", Collections.emptyList()).size();
//    int numRunning = statusGroupedDocuments.getOrDefault("RUNNING", Collections.emptyList()).size();
//    int numDeleted = statusGroupedDocuments.getOrDefault("DELETED", Collections.emptyList()).size();
//    int numFinish = statusGroupedDocuments.getOrDefault("FINISH", Collections.emptyList()).size();
//    bailianSubmitIndexJob.setNumInsertError(numInsertError);
//    bailianSubmitIndexJob.setNumRunning(numRunning);
//    bailianSubmitIndexJob.setNumDeleted(numDeleted);
//    bailianSubmitIndexJob.setNumFinish(numFinish);
//    bailianSubmitIndexJobDao.updateById(bailianSubmitIndexJob);
//    for (GetIndexJobStatusResponseBodyDataDocuments document : documents) {
//      final List<BaiLianIndexDocVO> baiLianIndexDocVOS =
//          convertToBaiLianIndexDocVOS(bailianSubmitIndexJob, document);
//      syncIndexDoc(baiLianIndexDocVOS);
//    }
//  }

//  private static @NotNull List<BaiLianIndexDocVO> convertToBaiLianIndexDocVOS(
//      BailianSubmitIndexJob bailianSubmitIndexJob,
//      GetIndexJobStatusResponseBodyDataDocuments document) {
//    final List<BaiLianIndexDocVO> baiLianIndexDocVOS = new ArrayList<>();
//    final BaiLianIndexDocVO baiLianIndexDocVO = new BaiLianIndexDocVO();
//    baiLianIndexDocVO.setIndexId(bailianSubmitIndexJob.getIndexId());
//    baiLianIndexDocVO.setFileId(document.getDocId());
//    baiLianIndexDocVO.setName(document.getDocName());
//    baiLianIndexDocVO.setStatus(document.getStatus());
//    baiLianIndexDocVO.setCode(document.getCode());
//    baiLianIndexDocVO.setMessage(document.getMessage());
//
//    baiLianIndexDocVOS.add(baiLianIndexDocVO);
//    return baiLianIndexDocVOS;
//  }
}
