package com.daddylab.msaiagent.service.bailian;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.msaiagent.common.bailian.domain.vo.RetrieveVO;
import com.daddylab.msaiagent.db.aiAgent.entity.BailianFile;
import com.daddylab.msaiagent.db.aiAgent.enums.BaiLianFileSubTypeEnum;
import com.daddylab.msaiagent.db.aiAgent.enums.BaiLianFileTypeEnum;
import com.daddylab.msaiagent.domain.dto.BaiLianFileDealDTO;
import com.daddylab.msaiagent.domain.form.BailianRetrieveForm;
import com.daddylab.msaiagent.domain.request.RemoveRemoteFilesRequest;

import java.util.Collections;
import java.util.List;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @className BailianFileService
 * @date 2025/3/4 11:56
 * @description: TODO
 */
public interface BaiLianFileService {

    BailianFile getById(Long id);

    List<BailianFile> getByIds(List<Long> ids);

    List<BailianFile> listByFileIds(List<String> fileIds);

    Boolean exists(BaiLianFileTypeEnum baiLianFileTypeEnum, Long typeId);

    List<BailianFile> getBaiLianFiles(BaiLianFileTypeEnum baiLianFileTypeEnum, Long typeId);

    List<BailianFile> getBaiLianFilTypeList(
            BaiLianFileTypeEnum baiLianFileTypeEnum, Long typeId, BaiLianFileSubTypeEnum fileType);

    void handler(BaiLianFileDealDTO baiLianFileDealDTO);

    void deleteAll(BailianFile bailianFile);

    void deleteRemoteIndexDocs(BailianFile bailianFile);

    void deleteRemoteFile(BailianFile bailianFile);

    List<BailianFile> batchUploadFiles(BaiLianFileTypeEnum type, List<String> urls);

    BailianFile uploadFile(
            BaiLianFileTypeEnum type,
            Long typeId,
            String fileUrl,
            String filename,
            List<String> tags);

    void syncCategoryFileToIndex();

    void runRemoveFile(int num);

    void removeRemoteFiles(RemoveRemoteFilesRequest request);

    void handleStatusWithReUpload(
            Long bizId, BaiLianFileTypeEnum baiLianFileTypeEnum, Supplier<String> uploadHandler);

    default List<String> filterTags(List<String> tags) {
        if (CollUtil.isEmpty(tags)) {
            return Collections.emptyList();
        }
        return tags.stream()
                .map(
                        t -> {
                            String tag = t;
                            tag = StrUtil.replace(tag, StrUtil.SLASH, "_");
                            tag = StrUtil.replace(tag, StrUtil.SPACE, "_");
                            tag = StrUtil.replace(tag, StrUtil.DOT, "_");
                            tag = StrUtil.replace(tag, "'", "_");
                            tag = StrUtil.replace(tag, "\"", "_");
                            tag = StrUtil.replace(tag, ",", "_");
                            tag = StrUtil.replace(tag, "、", "_");
                            tag = StrUtil.replace(tag, "，", "_");
                            tag = StrUtil.replace(tag, ";", "_");
                            tag = StrUtil.replace(tag, "；", "_");
                            tag = StrUtil.replace(tag, "（", "_");
                            tag = StrUtil.replace(tag, "）", "_");
                            return tag;
                        })
                .filter(StrUtil::isNotBlank)
                .filter(t -> t.length() <= 32)
                .distinct()
                .limit(10)
                .collect(Collectors.toList());
    }

    void markDelete(List<BailianFile> bailianFiles);

    void update(BailianFile bailianFile);

    /**
     * 重新上传文件
     *
     * @param fileId 文件ID
     */
    void reUpload(String fileId);

    /**
     * 文件检索
     *
     * @param bailianRetrieveForm com.daddylab.msaiagent.domain.form.BailianRetrieveForm
     * @return java.util.List<com.daddylab.msaiagent.common.bailian.domain.vo.RetrieveVO>
     * <AUTHOR>
     * @date 2025/5/27 10:21
     */
    List<RetrieveVO> retrieve(BailianRetrieveForm bailianRetrieveForm);
}
