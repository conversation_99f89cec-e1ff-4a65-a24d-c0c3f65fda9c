package com.daddylab.msaiagent.service.bailian.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.bailian20231229.models.ListFileResponseBody.ListFileResponseBodyDataFileList;
import com.daddylab.msaiagent.common.bailian.AliBaiLianConfig;
import com.daddylab.msaiagent.common.bailian.BaiLianService;
import com.daddylab.msaiagent.common.bailian.domain.dto.RetrieveQuery;
import com.daddylab.msaiagent.common.bailian.domain.enums.BaiLianFileEventTypeEnum;
import com.daddylab.msaiagent.common.bailian.domain.vo.*;
import com.daddylab.msaiagent.common.base.exception.AiAgentErrorCodeEnum;
import com.daddylab.msaiagent.common.base.exception.AiAgentException;
import com.daddylab.msaiagent.common.lock.DistributedLock;
import com.daddylab.msaiagent.common.log.LogVar;
import com.daddylab.msaiagent.common.rocketmq.RocketMqProducer;
import com.daddylab.msaiagent.common.utils.JsonUtil;
import com.daddylab.msaiagent.db.aiAgent.dao.BailianFileDao;
import com.daddylab.msaiagent.db.aiAgent.dao.BailianIndexDocumentDao;
import com.daddylab.msaiagent.db.aiAgent.entity.BailianFile;
import com.daddylab.msaiagent.db.aiAgent.entity.BailianIndexDocument;
import com.daddylab.msaiagent.db.aiAgent.enums.BaiLianFileRemoteStatusEnum;
import com.daddylab.msaiagent.db.aiAgent.enums.BaiLianFileSubTypeEnum;
import com.daddylab.msaiagent.db.aiAgent.enums.BaiLianFileTypeEnum;
import com.daddylab.msaiagent.db.aiAgent.enums.BaiLianIndexDocumentStatusEnum;
import com.daddylab.msaiagent.db.base.Entity;
import com.daddylab.msaiagent.domain.dto.BaiLianFileDealDTO;
import com.daddylab.msaiagent.domain.form.BailianRetrieveForm;
import com.daddylab.msaiagent.domain.request.RemoveRemoteFilesRequest;
import com.daddylab.msaiagent.service.bailian.BaiLianFileService;
import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @className BAiLianFileServiceImpl
 * @date 2025/3/4 11:56
 * @description: TODO
 */
@Slf4j
@Service
public class BaiLianFileServiceImpl implements BaiLianFileService {

  @Autowired private BaiLianService baiLianService;
  @Autowired private BailianFileDao bailianFileDao;
  @Autowired private BailianIndexDocumentDao bailianIndexDocumentDao;
  @Autowired private AliBaiLianConfig aliBaiLianConfig;
  @Autowired private RocketMqProducer rocketMqProducer;

  @Override
  public Boolean exists(BaiLianFileTypeEnum baiLianFileTypeEnum, Long typeId) {
    return bailianFileDao
        .lambdaQuery()
        .eq(BailianFile::getType, baiLianFileTypeEnum)
        .eq(BailianFile::getTypeId, typeId)
        .exists();
  }


  @Override
  public BailianFile getById(Long id) {
    return bailianFileDao.getById(id);
  }

  @Override
  public List<BailianFile> getByIds(List<Long> ids) {
    return bailianFileDao.lambdaQuery().in(BailianFile::getId, ids).list();
  }

    @Override
    public List<BailianFile> listByFileIds(List<String> fileIds) {
        return bailianFileDao.lambdaQuery().in(BailianFile::getFileId, fileIds).list();
    }

    @Override
  public List<BailianFile> getBaiLianFiles(BaiLianFileTypeEnum baiLianFileTypeEnum, Long typeId) {
    return bailianFileDao
        .lambdaQuery()
        .eq(BailianFile::getType, baiLianFileTypeEnum)
        .eq(BailianFile::getTypeId, typeId)
        .list();
  }

  @Override
  public List<BailianFile> getBaiLianFilTypeList(
      BaiLianFileTypeEnum baiLianFileTypeEnum, Long typeId, BaiLianFileSubTypeEnum fileType) {
    return bailianFileDao
        .lambdaQuery()
        .eq(BailianFile::getType, baiLianFileTypeEnum)
        .eq(BailianFile::getTypeId, typeId)
        .eq(BailianFile::getFileType, fileType)
        .list();
  }

  private BailianFile getBaiLianFileBaseInfo(BaiLianFileTypeEnum baiLianFileTypeEnum, Long typeId) {
    return bailianFileDao
        .lambdaQuery()
        .eq(BailianFile::getType, baiLianFileTypeEnum)
        .eq(BailianFile::getTypeId, typeId)
        .eq(BailianFile::getFileType, BaiLianFileSubTypeEnum.BASE_INFO)
        .one();
  }

  @DistributedLock(
      value = "BaiLianFileDealDTO:handler",
      searchKey = DistributedLock.SearchKeyStrategy.PARAMETER_PROPERTY)
  @Override
  public void handler(BaiLianFileDealDTO baiLianFileDealDTO) {
    log.debug("[文件上传到百炼] 开始 baiLianFileDealDTO={}", JsonUtil.toJSONString(baiLianFileDealDTO));
    if (BaiLianFileEventTypeEnum.CREATE.equals(baiLianFileDealDTO.getEvent())) {
      create(baiLianFileDealDTO);
      return;
    }
    if (BaiLianFileEventTypeEnum.DELETE.equals(baiLianFileDealDTO.getEvent())) {
      delete(baiLianFileDealDTO);
      return;
    }
    if (BaiLianFileEventTypeEnum.UPDATE_ALL.equals(baiLianFileDealDTO.getEvent())) {
      // 为了避免更新索引期间无法查询这边不做删除，等新文档索引成功以后再清理重复文件
      // delete(baiLianFileDealDTO);
      upload(baiLianFileDealDTO);
      return;
    }
    if (BaiLianFileEventTypeEnum.UPDATE_BASE.equals(baiLianFileDealDTO.getEvent())) {
      updateBase(baiLianFileDealDTO);
      return;
    }
  }

  private void create(BaiLianFileDealDTO baiLianFileDealDTO) {
    Boolean isExists = exists(baiLianFileDealDTO.getType(), baiLianFileDealDTO.getTypeId());
    if (isExists) {
      log.warn("[文件上传到百炼] 跳过已存在的文件");
      return;
    }
    upload(baiLianFileDealDTO);
  }

  private void upload(BaiLianFileDealDTO baiLianFileDealDTO) {
    // ossUrl地址存储
    if (CollUtil.isNotEmpty(baiLianFileDealDTO.getOssUrl())) {
      int count = 0;
      for (String ossUrl : baiLianFileDealDTO.getOssUrl()) {
        try {
          String ext = StringUtils.getFilenameExtension(ossUrl);
          String finalFilename = String.format("%s.%s", baiLianFileDealDTO.getFilename(), ext);
          if (count > 0) {
            finalFilename = String.format("%s_%d.%s", baiLianFileDealDTO.getFilename(), count, ext);
          }
          finalFilename = filterFilename(finalFilename);

          final List<String> filteredTags = filterTags(baiLianFileDealDTO.getTags());
          final UploadFileResult uploadFileResult =
              baiLianService.uploadOssFile(
                  baiLianFileDealDTO.getType().getModuleEnum(),
                  ossUrl,
                  finalFilename,
                  filteredTags);
          BailianFile bailianFile = new BailianFile();
          bailianFile.setType(baiLianFileDealDTO.getType());
          bailianFile.setTypeId(baiLianFileDealDTO.getTypeId());
          bailianFile.setFileType(BaiLianFileSubTypeEnum.FILE_INFO);
          bailianFile.setUrl(ossUrl);
          bailianFile.setFileId(uploadFileResult.getFileId());
          bailianFile.setFilename(finalFilename);
          bailianFile.setRemoteStatus(BaiLianFileRemoteStatusEnum.SUCCESS);
          bailianFile.setCategoryId(uploadFileResult.getCategoryId());
          bailianFileDao.save(bailianFile);
        } catch (Exception e) {
          log.error(
              "[文件上传到百炼] 文档上传失败，baiLianFileDealDTO={}, ossUrl={}, msg={}",
              LogVar.unnamed(baiLianFileDealDTO).json(),
              ossUrl,
              e.getMessage());
        }
        count++;
      }
    }
    // 处理基本信息
    if (CollUtil.isNotEmpty(baiLianFileDealDTO.getBaseInfo())
        || StrUtil.isNotBlank(baiLianFileDealDTO.getBaseInfoText())) {
      saveBaseInfo(baiLianFileDealDTO);
    }
  }

  private void saveBaseInfo(BaiLianFileDealDTO baiLianFileDealDTO) {
    String filename =
        String.format(
            "%s详情信息_%s.%s",
            baiLianFileDealDTO.getType().getDesc(),
            baiLianFileDealDTO.getFilename(),
            Optional.ofNullable(baiLianFileDealDTO.getBaseInfoFormat()).orElse("txt"));
    filename = filterFilename(filename);
    byte[] baseInfo;
    if (StrUtil.isNotBlank(baiLianFileDealDTO.getBaseInfoText())) {
      baseInfo = baiLianFileDealDTO.getBaseInfoText().getBytes(StandardCharsets.UTF_8);
    } else {
      baseInfo = mapToTxtBytes(baiLianFileDealDTO.getBaseInfo());
    }

    final List<String> tags = filterTags(baiLianFileDealDTO.getTags());
    final UploadFileResult uploadFileResult =
        baiLianService.uploadFile(
            baiLianFileDealDTO.getType().getModuleEnum(), baseInfo, filename, tags);
    BailianFile bailianFile = new BailianFile();
    bailianFile.setType(baiLianFileDealDTO.getType());
    bailianFile.setTypeId(baiLianFileDealDTO.getTypeId());
    bailianFile.setFileType(BaiLianFileSubTypeEnum.BASE_INFO);
    bailianFile.setUrl(uploadFileResult.getOssUrl());
    bailianFile.setFileId(uploadFileResult.getFileId());
    bailianFile.setFilename(filename);
    bailianFile.setRemoteStatus(BaiLianFileRemoteStatusEnum.SUCCESS);
    bailianFile.setCategoryId(uploadFileResult.getCategoryId());
    bailianFileDao.save(bailianFile);
  }

  private static String filterFilename(String filename) {
    if (filename.length() <= 128) {
      return filename;
    }
    final int indexOfExtension = filename.lastIndexOf(".");
    if (indexOfExtension > -1) {
      final String suffix = filename.substring(indexOfExtension);
      return filename.substring(0, 128 - suffix.length()) + suffix;
    } else {
      return filename.substring(0, 128);
    }
  }

  private void updateBase(BaiLianFileDealDTO baiLianFileDealDTO) {
    //    BailianFile fileBaseInfo =
    //        getBaiLianFileBaseInfo(baiLianFileDealDTO.getType(), baiLianFileDealDTO.getTypeId());
    //    if (fileBaseInfo != null) {
    //      deleteAll(fileBaseInfo);
    //    }
    saveBaseInfo(baiLianFileDealDTO);
  }

  /**
   * 彻底删除，删除百炼数据管理下的文件，以及已索引的文件，和本地数据库记录
   *
   * @param bailianFile 百炼文件记录
   */
  @Override
  public void deleteAll(BailianFile bailianFile) {
    deleteRemoteFile(bailianFile);
    deleteRemoteIndexDocs(bailianFile);
    bailianFileDao.removeById(bailianFile);
  }

  @Override
  public void deleteRemoteIndexDocs(BailianFile bailianFile) {
    final List<BailianIndexDocument> indexDocuments =
        bailianIndexDocumentDao
            .lambdaQuery()
            .eq(BailianIndexDocument::getFileId, bailianFile.getFileId())
            .list();
    if (!indexDocuments.isEmpty()) {
      final Map<String, List<BailianIndexDocument>> docGroup =
          indexDocuments.stream()
              .filter(it -> !BaiLianIndexDocumentStatusEnum.DELETED.getValue().equals(it.getStatus()))
              .collect(
                  Collectors.groupingBy(BailianIndexDocument::getIndexId, Collectors.toList()));
      docGroup.forEach(
          (indexId, documents) -> {
            baiLianService.deleteIndexDoc(
                indexId,
                documents.stream()
                    .map(BailianIndexDocument::getFileId)
                    .collect(Collectors.toList()));
            bailianIndexDocumentDao.updateStatus(documents, BaiLianIndexDocumentStatusEnum.DELETED);
          });
    }
  }

  @Override
  public void deleteRemoteFile(BailianFile bailianFile) {
    if (bailianFile.getRemoteStatus() == BaiLianFileRemoteStatusEnum.SUCCESS) {
      baiLianService.deleteFile(bailianFile.getFileId());
      bailianFile.setRemoteStatus(BaiLianFileRemoteStatusEnum.DELETE);
      bailianFileDao.updateById(bailianFile);
    }
  }

  @Override
  public List<BailianFile> batchUploadFiles(BaiLianFileTypeEnum type, List<String> urls) {
    Long now = DateUtil.current();
    String indexId = baiLianService.createIndex(type.getModuleEnum().getValue());
    return Flux.fromIterable(urls).window(10).flatMap(subUrls -> {
      return subUrls.map(url -> uploadFile(type, now, url, StringUtils.getFilename(url), Collections.emptyList())).collectList().doOnNext(bailianFiles -> {
        List<String> fileIds = bailianFiles.stream().map(BailianFile::getFileId).collect(Collectors.toList());
        baiLianService.syncFileIdToIndex(fileIds, indexId);
      });
    }).collectList().block();
  }

  public static void main(String[] args) {
    System.out.println(
        filterFilename(
            "国内食品添加标准详情信息_吐温类[包括聚氧乙烯（20）山梨醇酐单月桂酸酯（又名吐温20），聚氧乙烯（20）山梨醇酐单棕榈酸酯（又名吐温40），聚氧乙烯（20）山梨醇酐单硬脂酸酯（又名吐温60），聚氧乙烯（20）山梨醇酐单油酸酯（又名吐温80）]#1.txt\n"));
  }

  private void delete(BaiLianFileDealDTO baiLianFileDealDTO) {
    List<BailianFile> baiLianFiles =
        getBaiLianFiles(baiLianFileDealDTO.getType(), baiLianFileDealDTO.getTypeId());
    if (baiLianFiles.isEmpty()) {
      return;
    }
    for (BailianFile baiLianFile : baiLianFiles) {
      deleteAll(baiLianFile);
    }
  }

  private void deleteBaiLianIndexDoc(String indexId, List<BailianFile> baiLianFiles) {
    List<String> fileIds =
        baiLianFiles.stream().map(BailianFile::getFileId).collect(Collectors.toList());
    try {
      baiLianService.deleteIndexDoc(indexId, fileIds);
    } catch (Exception e) {
      log.error("[文件上传到百炼] 删除知识库的文件失败 fieldIds={}", fileIds);
    }
  }

  private byte[] mapToTxtBytes(Map<String, String> mp) {
    String content =
        mp.entrySet().stream()
            .map(m -> String.format("%s:%s\r\n", m.getKey(), m.getValue()))
            .collect(Collectors.joining(StrUtil.CRLF));
    return content.getBytes(StandardCharsets.UTF_8);
  }

  @Override
  public BailianFile uploadFile(
      BaiLianFileTypeEnum type, Long typeId, String fileUrl, String filename, List<String> tags) {
    String finalFilename = filename;
    String ext = StringUtils.getFilenameExtension(fileUrl);
    if (!finalFilename.contains(".") && StrUtil.isNotBlank(ext)) {
      finalFilename = String.format("%s.%s", filename, ext);
    }
    finalFilename = filterFilename(finalFilename);
    final List<String> filteredTags = filterTags(tags);
    final UploadFileResult uploadFileResult =
        baiLianService.uploadOssFile(type.getModuleEnum(), fileUrl, finalFilename, filteredTags);
    BailianFile bailianFile = new BailianFile();
    bailianFile.setType(type);
    bailianFile.setTypeId(typeId);
    bailianFile.setFileType(BaiLianFileSubTypeEnum.FILE_INFO);
    bailianFile.setUrl(fileUrl);
    bailianFile.setFileId(uploadFileResult.getFileId());
    bailianFile.setFilename(finalFilename);
    bailianFile.setRemoteStatus(BaiLianFileRemoteStatusEnum.SUCCESS);
    bailianFile.setCategoryId(uploadFileResult.getCategoryId());
    bailianFileDao.save(bailianFile);
    return bailianFile;
  }

  @Override
  public void syncCategoryFileToIndex() {
    final List<BaiLianCategoryVO> categoryList =
        baiLianService.getCategoryList(aliBaiLianConfig.getParentCategoryId());
    final Map<String, List<BaiLianCategoryVO>> pidGroupedCategories =
        categoryList.stream().collect(Collectors.groupingBy(BaiLianCategoryVO::getPid));
    log.debug("[百炼] submitIndex 获取到分类列表，categoryList={}", LogVar.unnamed(categoryList).json());
    final List<AliBaiLianConfig.BaiLianIndexConfig> indexConfigs = aliBaiLianConfig.getIndexConfigs();
    if (CollUtil.isNotEmpty(indexConfigs)) {
      for (AliBaiLianConfig.BaiLianIndexConfig indexConfig : indexConfigs) {
        if (indexConfig.isAutoIndexDisabled()) {
          continue;
        }
        final String indexId = indexConfig.getIndexId();
        if (CollUtil.isNotEmpty(indexConfig.getCategoryIds())) {
          final Set<String> exclusiveCategoryIds =
              indexConfig.getCategoryIds().stream()
                  .filter(it -> it.startsWith("!"))
                  .map(it -> it.substring(1))
                  .collect(Collectors.toSet());
          for (String categoryId : indexConfig.getCategoryIds()) {
            if (categoryId.startsWith("!") || exclusiveCategoryIds.contains(categoryId)) {
              log.debug("[百炼] submitIndex [{} -> {}] 跳过", categoryId, indexId);
              continue;
            }
            try {
              submitCategoryFilesToIndex(
                  pidGroupedCategories,
                  exclusiveCategoryIds,
                  categoryId,
                  indexId,
                  indexConfig.getDocLimitPerSubmit());
            } catch (Exception e) {
              log.error("[百炼] submitIndex [{} -> {}] 索引异常", categoryId, indexId, e);
            }
          }
        }
      }
    }
  }

  private void submitCategoryFilesToIndex(
      Map<String, List<BaiLianCategoryVO>> pidGroupedCategories,
      Set<String> exclusiveCategoryIds,
      String categoryId,
      String indexId,
      int docLimitPerSubmit) {
    final String key = "bailian_submit_" + indexId + "_" + categoryId;
//    final Long savepoint = sysVariableDao.getValue(key, Long.class, 0L);
    log.debug("[百炼] submitIndex [{} -> {}] savepoint:{}", categoryId, indexId, 1);
    final List<BailianFile> bailianFiles =
        getBailianFilesByCategoryId(categoryId, docLimitPerSubmit, 1L);
    if (!bailianFiles.isEmpty()) {
      final List<String> fileIds =
          bailianFiles.stream().map(BailianFile::getFileId).collect(Collectors.toList());
      final String jobId = baiLianService.syncFileIdToIndex(fileIds, indexId);
//      saveSubmitIndexJob(jobId, indexId);
      saveIndexDocs(indexId, bailianFiles);
//      sysVariableDao.setValue(key, bailianFiles.get(bailianFiles.size() - 1).getId().toString());
    } else {
      log.debug("[百炼] submitIndex [{} -> {}] 没有找到待同步文件", categoryId, indexId);
    }
    if (pidGroupedCategories.containsKey(categoryId)) {
      final List<BaiLianCategoryVO> subCategories = pidGroupedCategories.get(categoryId);
      for (BaiLianCategoryVO subCategory : subCategories) {
        if (exclusiveCategoryIds.contains(subCategory.getId())) {
          log.debug("[百炼] submitIndex [{} -> {}] 跳过", categoryId, indexId);
          continue;
        }
        submitCategoryFilesToIndex(
            pidGroupedCategories,
            exclusiveCategoryIds,
            subCategory.getId(),
            indexId,
            docLimitPerSubmit);
      }
    }
  }

  private void saveIndexDocs(String indexId, List<BailianFile> bailianFiles) {
    bailianIndexDocumentDao.saveBatch(
        bailianFiles.stream()
            .map(
                file -> {
                  final BailianIndexDocument bailianIndexDocument = new BailianIndexDocument();
                  bailianIndexDocument.setIndexId(indexId);
                  bailianIndexDocument.setFileId(file.getFileId());
                  bailianIndexDocument.setName(file.getFilename());
                  bailianIndexDocument.setStatus("RUNNING");
                  return bailianIndexDocument;
                })
            .collect(Collectors.toList()));
  }

  private List<BailianFile> getBailianFilesByCategoryId(String categoryId, int limit, Long cursor) {
    return bailianFileDao
        .lambdaQuery()
        .eq(BailianFile::getCategoryId, categoryId)
        .eq(BailianFile::getRemoteStatus, BaiLianFileRemoteStatusEnum.SUCCESS)
        .gt(Entity::getId, cursor)
        .last("limit " + limit)
        .list();
  }

  @Override
  public void runRemoveFile(int num) {
    List<BailianFile> baiLianFiles =
        bailianFileDao
            .lambdaQuery()
            .eq(BailianFile::getRemoteStatus, BaiLianFileRemoteStatusEnum.SUCCESS)
            .eq(BailianFile::getRefCount, 0)
            .notIn(BailianFile::getType, aliBaiLianConfig.getNotRemoveFileTypes())
            .last("limit " + num)
            .list();
    doRemoveFile(baiLianFiles);
    int remainNum = num - baiLianFiles.size();
    if (remainNum <= 0) {
      return;
    }
    List<BailianFile> baiLianFiles1 =
        bailianFileDao
            .lambdaQuery()
            .eq(BailianFile::getDeleteFlag, 1)
            .last("limit " + remainNum)
            .list();
    doRemoveFile(baiLianFiles1);
  }

  private void doRemoveFile(List<BailianFile> baiLianFiles) {
    final RateLimiter rateLimiter = RateLimiter.create(10);
    Flux.fromIterable(baiLianFiles)
        .flatMap(
            baiLianFile ->
                Mono.fromRunnable(
                        () -> {
                          rateLimiter.acquire();
                          if (baiLianFile.getDeleteFlag() != 0) {
                            // 有删除标记的文件，删除全部文件，包括索引文件
                            deleteAll(baiLianFile);
                          } else {
                            deleteRemoteFile(baiLianFile);
                          }
                        })
                    .doOnSuccess(void_ -> log.info("[百炼-清理文件]成功删除文件: {}", baiLianFile.getFileId()))
                    .doOnError(
                        err ->
                            log.error(
                                "[百炼-清理文件]删除文件失败: {}，原因: {}",
                                baiLianFile.getFileId(),
                                err.getMessage()))
                    .retryWhen(
                        Retry.fixedDelay(3, Duration.ofSeconds(2)) // 最多重试3次，间隔2秒
                            .filter(throwable -> throwable instanceof RuntimeException) // 仅重试运行时异常
                        ),
            10)
        .doOnError(err -> log.error("[百炼-清理文件]全局处理异常", err))
        .onErrorContinue((ex, o) -> log.warn("[百炼-清理文件]继续处理其他文件，当前错误: {}", ex.getMessage()))
        .doOnComplete(() -> log.info("[百炼-清理文件]处理结束:{}", baiLianFiles.size()))
        .subscribe();
  }

  //  @DistributedLock(value = "tech-web-admin:baiLianFileStatusHandler")
  //  @Override
  //  public void handlerStatus(BaiLianFileStatusDTO baiLianFileStatusDTO) {
  //    LambdaUpdateChainWrapper<BailianFile> updateChainWrapper = bailianFileDao.lambdaUpdate();
  //    FIleInfoVO fileInfo = baiLianService.getFileInfo(baiLianFileStatusDTO.getFileId());
  //    if (fileInfo == null) {
  //      updateChainWrapper
  //          .eq(BailianFile::getId, baiLianFileStatusDTO.getId())
  //          .set(BailianFile::getLastSearchTime, DateUtil.currentSeconds())
  //          .set(BailianFile::getRemoteStatus, BaiLianFileRemoteStatusEnum.NOT_EXISTS)
  //          .update();
  //      return;
  //    }
  //    if (fileInfo.isSyncToIndex()) {
  //      try {
  //        baiLianService.deleteFile(fileInfo.getFileId());
  //      } catch (Exception ignored) {
  //      }
  //      updateChainWrapper.set(BailianFile::getRemoteStatus, BaiLianFileRemoteStatusEnum.DELETE);
  //    }
  //    updateChainWrapper
  //        .eq(BailianFile::getId, baiLianFileStatusDTO.getId())
  //        .set(BailianFile::getLastSearchTime, DateUtil.currentSeconds())
  //        .update();
  //  }

  @Override
  public void removeRemoteFiles(RemoveRemoteFilesRequest request) {
    final RateLimiter rateLimiter = RateLimiter.create(request.getRateLimit());
    baiLianService.foreachFiles(
        request.getCategoryId(),
        fileList -> {
          for (ListFileResponseBodyDataFileList file : fileList) {
            try {
              rateLimiter.acquire();
              baiLianService.deleteFile(file.getFileId());
              log.debug("[百炼-删除文件]删除成功 fileId={},name={}", file.getFileId(), file.getFileName());
            } catch (Exception e) {
              log.error("[百炼-删除文件]删除异常", e);
            }
          }
          final List<String> fileIds =
              fileList.stream()
                  .map(ListFileResponseBodyDataFileList::getFileId)
                  .collect(Collectors.toList());
          if (CollUtil.isEmpty(fileIds)) {
            return;
          }
          final boolean updateCount =
              bailianFileDao
                  .lambdaUpdate()
                  .in(BailianFile::getFileId, fileIds)
                  .set(BailianFile::getRemoteStatus, BaiLianFileRemoteStatusEnum.DELETE)
                  .update();
          log.debug("[百炼-删除文件]更新本地记录:{}", updateCount);
        },
        request.getPageSize(),
        request.getPageLimit());
  }

  @Override
  public void handleStatusWithReUpload(
      Long bizId, BaiLianFileTypeEnum baiLianFileTypeEnum, Supplier<String> uploadHandler) {
    final List<BailianFile> list =
        bailianFileDao
            .lambdaQuery()
            .eq(BailianFile::getType, baiLianFileTypeEnum)
            .eq(BailianFile::getTypeId, bizId)
            .list();
    if (CollUtil.isEmpty(list)) return;

    for (BailianFile bailianFile : list) {
      FileInfoVO fileInfo = baiLianService.getFileInfo(bailianFile.getFileId());
      if (ObjectUtil.isNull(fileInfo)) {
        bailianFile.setLastSearchTime(DateUtil.currentSeconds());
        bailianFile.setRemoteStatus(BaiLianFileRemoteStatusEnum.NOT_EXISTS);
        bailianFileDao.updateById(bailianFile);
        continue;
      }

      // 上传成功删除文件
      if (fileInfo.isParseSuccess()) {
        try {
          baiLianService.deleteFile(bailianFile.getFileId());
          bailianFile.setRemoteStatus(BaiLianFileRemoteStatusEnum.DELETE);
          bailianFile.setLastSearchTime(DateUtil.currentSeconds());
          bailianFileDao.updateById(bailianFile);
        } catch (Exception e) {
          // ignore
          continue;
        }
      }
      // 解析失败重新上传
      if (fileInfo.isParseFailed()) {
        uploadHandler.get();
      }

      try {
        TimeUnit.SECONDS.sleep(1);
      } catch (InterruptedException e) {
        // ignore
      }
    }
  }

  @Override
  public void markDelete(List<BailianFile> bailianFiles) {
    bailianFileDao.updateBatchById(bailianFiles);
  }

  @Override
  public void update(BailianFile bailianFile) {
    bailianFileDao.updateById(bailianFile);
  }

  @Override
  public void reUpload(String fileId) {
    final List<BailianFile> bailianFiles = bailianFileDao.listByFileId(fileId);
    if (bailianFiles.isEmpty()) {
      return;
    }
    bailianFiles.stream()
        .filter(v -> StrUtil.isNotBlank(v.getUrl()))
        .max(Comparator.comparing(BailianFile::getId))
        .ifPresent(
            bailianFile -> {
              final String url = bailianFile.getUrl();
              if (StrUtil.isNotBlank(url)) {
                final UploadFileResult uploadFileResult =
                    baiLianService.uploadOssFile(
                        bailianFile.getType().getModuleEnum(),
                        bailianFile.getUrl(),
                        bailianFile.getFilename());
                BailianFile newBailianFile = new BailianFile();
                newBailianFile.setType(bailianFile.getType());
                newBailianFile.setTypeId(bailianFile.getTypeId());
                newBailianFile.setFileType(bailianFile.getFileType());
                newBailianFile.setUrl(bailianFile.getUrl());
                newBailianFile.setFileId(uploadFileResult.getFileId());
                newBailianFile.setFilename(bailianFile.getFilename());
                newBailianFile.setRemoteStatus(BaiLianFileRemoteStatusEnum.SUCCESS);
                newBailianFile.setCategoryId(uploadFileResult.getCategoryId());
                bailianFileDao.save(newBailianFile);
              }
            });
  }

  @Override
  public List<RetrieveVO> retrieve(BailianRetrieveForm bailianRetrieveForm) {
    List<BaiLianIndexVO> indexList = baiLianService.getIndexList(bailianRetrieveForm.getBaiLianFileTypeEnum().getModuleEnum().getValue());
    if (indexList.isEmpty()) {
      throw AiAgentException.throwException(AiAgentErrorCodeEnum.BUSINESS_ERROR, "知识库不存在");
    }

    RetrieveQuery retrieveQuery = RetrieveQuery.ofQuery(indexList.getFirst().getId(), bailianRetrieveForm.getKeyword(), bailianRetrieveForm.getSize());
    List<RetrieveVO> retrieveVOList = baiLianService.retrieve(retrieveQuery);
    if (retrieveVOList.isEmpty()) {
      return Collections.emptyList();
    }
    Set<String> fileIds = retrieveVOList.stream().map(RetrieveVO::getDocId).collect(Collectors.toSet());
    Map<String, String> fileIdUrlMap = bailianFileDao.lambdaQuery().eq(BailianFile::getType, bailianRetrieveForm.getBaiLianFileTypeEnum())
            .in(BailianFile::getFileId, fileIds)
            .list().stream()
            .collect(Collectors.toMap(BailianFile::getFileId, BailianFile::getUrl));
    retrieveVOList.forEach(retrieveVO -> retrieveVO.setOssUrl(fileIdUrlMap.getOrDefault(retrieveVO.getDocId(), StrUtil.EMPTY)));
    return retrieveVOList;
  }
}
