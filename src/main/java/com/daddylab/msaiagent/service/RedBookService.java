package com.daddylab.msaiagent.service;

import com.daddylab.msaiagent.common.crawler.XhsCrawler;
import com.daddylab.msaiagent.common.feign.domain.vo.YingDaoTaskQueryVo;
import com.daddylab.msaiagent.db.aiAgent.entity.RedBook;
import com.daddylab.msaiagent.domain.vo.RedBookInfoVO;

import java.util.List;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @className RedBookService
 * @date 2025/4/28 16:14
 * @description: TODO
 */
public interface RedBookService {
    /**
     * 是否已存在
     *
     * @param accountId java.lang.String
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2025/5/8 11:13
     */
    Boolean hasAccount(String accountId);

    Boolean hasEncryptAccount(String encryptAccountId);

    List<RedBook> getAccount(String accountId, Long size);

    List<RedBook> getEncryptAccount(String encryptAccountId, Long size);

    /**
     * 刷新cookie
     *
     * @return void
     * <AUTHOR>
     * @date 2025/5/26 16:56
     */
    void freshCookie();
    /**
     * 爬取详情
     *
     * <AUTHOR>
     * @date 2025/4/10 10:47
     */
    void runCrawlDetail();

    /**
     * 定期刷新
     *
     * @param intervalDay Integer 更新频率 (更新频率为几天)
     * <AUTHOR>
     * @date 2025/5/26 09:45
     */
    void freshCrawlDetail(Integer intervalDay);

    void runCrawlAccountList(String url);

    Consumer<YingDaoTaskQueryVo> disposeCallback();

    public void disposeSearchKeyword(String keyword, Integer size);

    RedBookInfoVO getUrlInfo(String url);
}
