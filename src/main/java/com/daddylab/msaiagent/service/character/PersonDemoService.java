package com.daddylab.msaiagent.service.character;

//import com.daddylab.msaiagent.common.manage.AiClientManage;
import com.daddylab.msaiagent.domain.enums.AIModelEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR> up
 * @date 2025年05月08日 5:48 PM
 */
@Service
@Slf4j
@AllArgsConstructor
public class PersonDemoService {


//    public String test1(String promptStr) {
//        ChatClient chatClient = AiClientManage.buildDefaultClient(AIModelEnum.DASHSCOPE);
//        chatClient.prompt().user(promptStr).call().content();
//        ChatClient.ChatClientRequestSpec clientRequestSpec = chatClient.prompt(promptStr);
////        return clientRequestSpec.call().content();
//
//        final String template = "请问{author}最受欢迎的书是哪本书？什么时候发布的？书的内容是什么？";
//        PromptTemplate promptTemplate = new PromptTemplate(templateResource);
////        PromptTemplate promptTemplate = new PromptTemplate(template);
//        // 动态地将author填充进去
//        Prompt prompt = promptTemplate.create(Map.of("author", author));
//        ChatResponse chatResponse = chatClient.call(prompt);
//        AssistantMessage assistantMessage = chatResponse.getResult().getOutput();
//        return assistantMessage.getContent();
//
//
//    }

}
