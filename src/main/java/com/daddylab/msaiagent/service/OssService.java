package com.daddylab.msaiagent.service;


import com.daddylab.msaiagent.domain.vo.OssSignCommand;
import com.daddylab.msaiagent.domain.vo.OssSignURLCommand;
import com.daddylab.msaiagent.domain.vo.OssSignURLResult;
import com.daddylab.msaiagent.domain.vo.StsTokenResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/9/20
 */
public interface OssService {
    /**
     * 对象访问签名（兼容 GET、PUT）
     * @param commands 签名参数
     * @return 签名后的对象访问链接
     */
    List<String> sign(List<OssSignCommand> commands);

    /**
     * 对OSS对象访问链接签名
     * @param commands 签名参数
     * @return 签名后的对象访问链接
     */
    List<OssSignURLResult> signURL(List<OssSignURLCommand> commands);

    /**
     * 获取STS Token
     */
    StsTokenResult getStsToken();

    /**
     * 获取图片信息
     *
     * @param ossUrl ossUrl
     * @return 图片信息
     */
    Map<String, String> imageInfo(String ossUrl);
}
