package com.daddylab.msaiagent.service;

import com.daddylab.msaiagent.common.feign.domain.vo.YingDaoTaskQueryVo;

import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @className RedbookHotspotService
 * @date 2025/5/7 10:14
 * @description: 小红书热点
 */
public interface RedbookHotspotService {
    /**
     * 获取行业热度榜
     */
    String getRank1(int days);
    /**
     * 获取热词飙升榜
     */
    String getRank2(int days);
    /**
     * 获取热词总量榜
     */
    String getRank3(int days);
    /**
     * 获取新榜热点指数
     */
    String getRank4(int days);
}
