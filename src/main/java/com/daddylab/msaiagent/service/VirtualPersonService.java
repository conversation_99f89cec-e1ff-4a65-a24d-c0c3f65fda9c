package com.daddylab.msaiagent.service;

import com.daddylab.msaiagent.db.aiAgent.entity.VirtualPerson;

/**
 * <AUTHOR>
 * @className VirtualPersonService
 * @date 2025/5/9 10:39
 * @description: TODO
 */
public interface VirtualPersonService {

    /**
     * 根据描述构建一个虚拟的人物
     *
     * @param desc java.lang.String
     * @return com.daddylab.msaiagent.db.aiAgent.entity.VirtualPerson
     * <AUTHOR>
     * @date 2025/5/9 10:40
     */
    VirtualPerson buildVirtualPerson(String desc);

    /**
     * 模仿构建一个虚拟的人物
     *
     * @param prompt java.lang.String 提示词
     * @return com.daddylab.msaiagent.db.aiAgent.entity.VirtualPerson
     * <AUTHOR>
     * @date 2025/5/9 10:55
     */
    VirtualPerson imitationBuildVirtualPerson(String prompt);
}
