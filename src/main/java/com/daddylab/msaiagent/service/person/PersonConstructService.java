package com.daddylab.msaiagent.service.person;

import com.daddylab.msaiagent.db.aiAgent.entity.PersonaAttributeDefinition;
import com.daddylab.msaiagent.domain.dto.PersonAttributeDto;
import com.daddylab.msaiagent.domain.dto.PersonGeneratorAiRespDto;
import com.daddylab.msaiagent.domain.enums.PersonSubjectTypeEnum;
import com.daddylab.msaiagent.domain.dto.PersonCategoryDto;

import java.util.List;
import java.util.Map;

public interface PersonConstructService {

    List<PersonCategoryDto> constructPersonCategory(PersonSubjectTypeEnum subjectTypeEnum);

    List<PersonAttributeDto> constructPersonAttributes(PersonSubjectTypeEnum subjectTypeEnum);

    Map<String, PersonaAttributeDefinition> constructPerson();

    void updateConstruct(PersonGeneratorAiRespDto dto);
}
