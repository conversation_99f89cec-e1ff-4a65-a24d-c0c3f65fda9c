package com.daddylab.msaiagent.service.person.impl;

import com.daddylab.msaiagent.common.utils.JsonUtil;
import com.daddylab.msaiagent.db.aiAgent.entity.AigcJob;
import com.daddylab.msaiagent.db.aiAgent.entity.PersonaCore;
import com.daddylab.msaiagent.db.aiAgent.entity.PersonaDetail;
import com.daddylab.msaiagent.domain.constants.ChatTypeConst;
import com.daddylab.msaiagent.domain.dto.PersonGeneratorAiRespDto;
import com.daddylab.msaiagent.domain.enums.PersonCreationSource;
import com.daddylab.msaiagent.domain.enums.PersonSubjectTypeEnum;
import com.daddylab.msaiagent.domain.form.PersonAiGeneratedForm;
import com.daddylab.msaiagent.domain.form.PersonUpdateByAiForm;
import com.daddylab.msaiagent.domain.form.PersonCreateForm;
import com.daddylab.msaiagent.domain.vo.AiJobVO;
import com.daddylab.msaiagent.domain.vo.PersonInfoVO;
import com.daddylab.msaiagent.prompt.PersonGeneratePrompt;
import com.daddylab.msaiagent.service.*;
import com.daddylab.msaiagent.service.person.PersonConstructService;
import com.daddylab.msaiagent.service.person.PersonGeneratorService;
import com.daddylab.msaiagent.service.person.PersonService;
import com.daddylab.msaiagent.util.PromptStringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class PersonGeneratorServiceImpl implements PersonGeneratorService {

    @Autowired
    private PersonConstructService personConstructService;
    @Autowired
    private PersonService personService;
    @Autowired
    AigcJobService aigcJobService;
    @Autowired
    private ModelChatService modelChatService;

    @Override
    public AiJobVO generatePersonByAi(PersonAiGeneratedForm form) {
        String template = "";
        if (form.getSubjectType() == PersonSubjectTypeEnum.INDIVIDUAL) {
            template = getUserMainPrompt(form);
        }
        if (form.getSubjectType() == PersonSubjectTypeEnum.ORGANIZATION || form.getSubjectType() == PersonSubjectTypeEnum.GROUP_ALIAS) {
            template = getGroupMainPrompt(form);
        }
        if (template.isEmpty()) {
            return null;
        }
        PersonCreateForm personCreateForm = new PersonCreateForm();
        personCreateForm.setSubjectType(form.getSubjectType());
        personCreateForm.setCreationSource(PersonCreationSource.OPERATOR_DESIGNED);
        personCreateForm.setPrimaryMonetizationModel(form.getPrimaryMonetizationModel());
        PersonaCore person = personService.createPerson(personCreateForm);
        AigcJob job = aigcJobService.createJob(person.getId(),ChatTypeConst.AI_GENERATOR_PERSON,0L,form);
        log.info("开始请求ai生成虚拟人人设参数:{}", job.getId());
        aigcJobService.startJobAsync(job, template, content -> extractedPersonDetail(job.getId(), content, person));
        return AiJobVO.fromJob(job);
    }

    @Override
    public AiJobVO guidingQuestionAndUpdatePerson(PersonUpdateByAiForm qaForm) {
        PersonaCore person = personService.getPerson(qaForm.getUid());
        PersonaDetail detail = personService.getPersonDetail(person.getId());
        if (null == detail) {
            log.error("没有找到人设的详细信息");
            return null;
        }
        return guiding(person, detail.getJobId(), qaForm);
    }

    private AiJobVO guiding(PersonaCore person, Long jobId, PersonUpdateByAiForm qaForm) {
        long oldChatId = 0;
        if(null != jobId && jobId > 0) {
            AigcJob oldJob = aigcJobService.getJob(jobId);
            if (null != oldJob) {
                oldChatId = oldJob.getModelChatId();
            }
        }
        String template = "";
        if (person.getSubjectType() == PersonSubjectTypeEnum.INDIVIDUAL.getCode()) {
            template = getUserGuidingAndUpdatePrompt(qaForm, oldChatId > 0);
        }
        if (person.getSubjectType() == PersonSubjectTypeEnum.ORGANIZATION.getCode() || person.getSubjectType() == PersonSubjectTypeEnum.GROUP_ALIAS.getCode()) {
            template = getGroupGuidingAndUpdatePrompt(qaForm, oldChatId > 0);
        }
        if (template.isEmpty()) {
            return null;
        }
        AigcJob job = aigcJobService.createJob(person.getId(),ChatTypeConst.AI_UPDATE_PERSON,oldChatId,qaForm);
        log.info("开始请求ai修改虚拟人人设参数:{}", person.getPersonaUuid());
        aigcJobService.startJobAsync(job, template, content -> extractedPersonDetail(job.getId(), content, person));
        return AiJobVO.fromJob(job);
    }

    private void extractedPersonDetail(Long jobId, String content, PersonaCore person) {
        try {
            log.info("ai 返回人设参数的信息，开始整合:{}", person.getPersonaUuid());
            PersonGeneratorAiRespDto dto = JsonUtil.parseObject(content, PersonGeneratorAiRespDto.class);
            if (null == dto) {
                log.error("ai返回的不是约定格式的json数据");
                aigcJobService.jobError(jobId, "ai返回的不是约定格式的json数据");
                return;
            }
            if (null == dto.getPersonaAttributes() && null != dto.getUpdatedPersonaAttributes()) {
                dto.setPersonaAttributes(dto.getUpdatedPersonaAttributes());
            }
            personService.updatePerson(person.getId(), jobId, dto);
            
            var suggestedData = createSuggestedDataMap(dto);
            aigcJobService.jobUpdateAiInfo(jobId, dto.getClarificationQuestions(), dto.getUpdateNotes(), suggestedData);
            
            log.info("ai 返回人设参数的信息，整合完毕:{}", person.getPersonaUuid());
        } catch (Exception e) {
            log.error("解析ai返回的数据异常{},{}", person.getPersonaUuid(), e.getMessage());
            aigcJobService.jobError(jobId, "解析ai返回的数据异常");
        }
    }

    /**
     * 使用Java 将其他的属性和分类合并为一个Map
     * @param dto AI响应DTO
     * @return 包含建议数据的Map
     */
    private Map<String, Object> createSuggestedDataMap(PersonGeneratorAiRespDto dto) {
        return Map.of(
            "suggestedNewPersonaAttributesDefinition", 
            dto.getSuggestedNewPersonaAttributesDefinition() != null ? dto.getSuggestedNewPersonaAttributesDefinition() : List.of(),
            "suggestedNewCategories", 
            dto.getSuggestedNewCategories() != null ? dto.getSuggestedNewCategories() : List.of()
        );
    }

    private String getInitQuestionAnswer(Map<String, String> initQuestionAndAnswer) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : initQuestionAndAnswer.entrySet()) {
            sb.append("*    **").append(entry.getKey()).append(":**").append("\n").append("```text\n").append(entry.getValue()).append("\n```").append("\n");
        }
        return sb.toString();
    }

    @Override
    public void parseDetailByChatRecord(Long uid) {
        /*PersonaCore person = personService.getPerson(personUuid);
        ModelChat chat = modelChatService.getChat("google/gemini-2.5-pro-preview", ChatTypeConst.AI_GENERATOR_PERSON + "_" + person.getId());
        List<Message> messages = modelChatService.getChatMessages(chat);
        for (int i = 0; i < messages.size(); i++) {
            Message message = messages.get(i);
            if (message.getMessageType() == MessageType.USER) {
                if (message.getText().contains("你是一位经验丰富的人设策划专家、富有创意的故事写手，并且擅长信息结构化与系统设计")) {
                    updateByAiContent(chat.getId(), messages, person, i + 1);
                    continue;
                }
                if (message.getText().contains("你是一位细致入微的人设编辑、信息整合专家、动态调整画像结构的能手，同时也是一位经验丰富的人设策划顾问和深度访谈专家")) {
                    updateByAiContent(chat.getId(), messages, person, i + 1);
                }
            }
        }*/
    }

    private void updateByAiContent(Long chatId, List<Message> messages, PersonaCore person, int detailChatIndex) {
        if (detailChatIndex < 0 || detailChatIndex >= messages.size()) {
            log.error("没有找到解析人设的聊天记录");
            return;
        }
        Message message = messages.get(detailChatIndex);
        if (message.getMessageType() != MessageType.ASSISTANT) {
            log.error("解析人设的聊天记录不是助手的消息");
            return;
        }
        String content = message.getText();
        extractedPersonDetail(chatId, content, person);
    }

    private String getUserMainPrompt(PersonAiGeneratedForm form) {
        String subjectTypeName = form.getSubjectType().getDescription();
        String subjectTypeCode = form.getSubjectType().name();
        String subjectTypeStyle = "真实素人感";
        String monetizationModelCode = form.getPrimaryMonetizationModel().name();
        String monetizationModelName = form.getPrimaryMonetizationModel().getDescription();
        String categoryJson = JsonUtil.toJSONString(personConstructService.constructPersonCategory(form.getSubjectType()));
        String attributeJson = JsonUtil.toJSONString(personConstructService.constructPersonAttributes(form.getSubjectType()));
        String template = PersonGeneratePrompt.USER_GENERATE;
        template = template.replace("[subject_type_name]", subjectTypeName)
                .replace("[subject_type_style]", subjectTypeStyle)
                .replace("[subject_type_code]", subjectTypeCode)
                .replace("[init_question_answer]", getInitQuestionAnswer(form.getInitQuestionAndAnswer()))
                //.replace("[q1_answer_core_identity_role]",form.getCoreIdentityAndRole())
                //.replace("[q2_answer_philosophy_origin_uniqueness]",form.getCoreConceptStory())
                //.replace("[q3_answer_goals_user_value]",form.getContentGoalAndValue())
                .replace("[monetization_model_code]", monetizationModelCode)
                .replace("[monetization_model_name]", monetizationModelName)
                .replace("[monetization_category_focus]", form.getPrimaryMonetizationDescription())
                .replace("[operator_free_description]", form.getAdditionalDescription())
                .replace("[current_persona_category_system_reference_json]", categoryJson)
                .replace("[current_persona_attribute_definition_reference_json]", attributeJson);
        return template;
    }

    private String getGroupMainPrompt(PersonAiGeneratedForm form) {
        String subjectTypeName = form.getSubjectType().getDescription();
        String subjectTypeCode = form.getSubjectType().name();
        String monetizationModelCode = form.getPrimaryMonetizationModel().name();
        String monetizationModelName = form.getPrimaryMonetizationModel().getDescription();
        String categoryJson = JsonUtil.toJSONString(personConstructService.constructPersonCategory(form.getSubjectType()));
        String attributeJson = JsonUtil.toJSONString(personConstructService.constructPersonAttributes(form.getSubjectType()));
        String template = PersonGeneratePrompt.GROUP_GENERATE;
        template = template.replace("[subject_type_name]", subjectTypeName)
                .replace("[subject_type_code]", subjectTypeCode)
                .replace("[init_question_answer]", getInitQuestionAnswer(form.getInitQuestionAndAnswer()))
                //.replace("[q1_answer_core_identity_role]",form.getCoreIdentityAndRole())
                //.replace("[q2_answer_philosophy_origin_uniqueness]",form.getCoreConceptStory())
                //.replace("[q3_answer_goals_user_value]",form.getContentGoalAndValue())
                .replace("[monetization_model_code]", monetizationModelCode)
                .replace("[monetization_model_name]", monetizationModelName)
                .replace("[monetization_category_focus]", form.getPrimaryMonetizationDescription())
                .replace("[operator_free_description]", form.getAdditionalDescription())
                .replace("[current_persona_category_system_reference_json]", categoryJson)
                .replace("[current_persona_attribute_definition_reference_json]", attributeJson);
        return template;
    }

    private String getUserGuidingAndUpdatePrompt(PersonUpdateByAiForm qaForm, boolean hasHistory) {
        String qaPrompt = PersonGeneratePrompt.USER_UPDATE;
        String max = "3";
        PersonSubjectTypeEnum subjectType = PersonSubjectTypeEnum.INDIVIDUAL;
        String subjectTypeName = subjectType.getDescription();
        String attributeFrom = "需要你从上面的对话中提取";
        String categoryJson = "";
        String attributeJson = "";
        String userJson = "";
        String sessionTips = "我们正处在一个持续的对话session中，你已经了解了基本的画像框架和分类体系。";
        if (hasHistory) {
            qaPrompt = PromptStringUtil.removeMarkedContent(qaPrompt);
            attributeFrom = "参考下面的json数据";
            sessionTips = "基本的画像框架和分类体系见下文的定义json结构";
        } else {
            PersonInfoVO personInfo = personService.getPersonInfo(qaForm.getUid());
            categoryJson = "```json\n" + JsonUtil.toJSONString(personConstructService.constructPersonCategory(subjectType)) + "\n```";
            attributeJson = "```json\n" + JsonUtil.toJSONString(personConstructService.constructPersonAttributes(subjectType)) + "\n```";
            userJson = "```json\n" + JsonUtil.toJSONString(personInfo.getPersonaAttributes()) + "\n```";
        }
        return qaPrompt.replace("[max_questions_per_round]", max)
                .replace("[current_persona_category_system_reference_json]", categoryJson)
                .replace("[current_persona_attribute_definition_reference_json]", attributeJson)
                .replace("[session_tips]", sessionTips)
                .replace("[attribute_from]", attributeFrom)
                .replace("[user_attribute]", userJson)
                .replace("[subject_type_name]", subjectTypeName)
                .replace("[user_addition_info]", qaForm.getUserAdditionInfo())
                .replace("[user_answers_to_previous_questions_json]", JsonUtil.toJSONString(qaForm.getUserAnswer()));
    }

    private String getGroupGuidingAndUpdatePrompt(PersonUpdateByAiForm qaForm, boolean hasHistory) {
        String qaPrompt = PersonGeneratePrompt.GROUP_UPDATE;
        String max = "3";
        PersonSubjectTypeEnum subjectType = PersonSubjectTypeEnum.INDIVIDUAL;
        String subjectTypeName = subjectType.getDescription();
        String attributeFrom = "需要你从上面的对话中提取";
        String categoryJson = "";
        String attributeJson = "";
        String userJson = "";
        String sessionTips = "我们正处在一个持续的对话session中，你已经了解了基本的画像框架和分类体系。";
        if (hasHistory) {
            qaPrompt = PromptStringUtil.removeMarkedContent(qaPrompt);
            attributeFrom = "参考下面的json数据";
            sessionTips = "基本的画像框架和分类体系见下文的定义json结构";
        } else {
            PersonInfoVO personInfo = personService.getPersonInfo(qaForm.getUid());
            categoryJson = "```json\n" + JsonUtil.toJSONString(personConstructService.constructPersonCategory(subjectType)) + "\n```";
            attributeJson = "```json\n" + JsonUtil.toJSONString(personConstructService.constructPersonAttributes(subjectType)) + "\n```";
            userJson = "```json\n" + JsonUtil.toJSONString(personInfo.getPersonaAttributes()) + "\n```";
        }
        return qaPrompt.replace("[max_questions_per_round]", max)
                .replace("[current_persona_category_system_reference_json]", categoryJson)
                .replace("[current_persona_attribute_definition_reference_json]", attributeJson)
                .replace("[session_tips]", sessionTips)
                .replace("[attribute_from]", attributeFrom)
                .replace("[user_attribute]", userJson)
                .replace("[subject_type_name]", subjectTypeName)
                .replace("[user_addition_info]", qaForm.getUserAdditionInfo())
                .replace("[user_answers_to_previous_questions_json]", JsonUtil.toJSONString(qaForm.getUserAnswer()));
    }
}
