package com.daddylab.msaiagent.service.person.impl;

import com.daddylab.msaiagent.db.aiAgent.dao.PersonaAttributeCategoryLvl1Dao;
import com.daddylab.msaiagent.db.aiAgent.dao.PersonaAttributeCategoryLvl2Dao;
import com.daddylab.msaiagent.db.aiAgent.dao.PersonaAttributeDefinitionDao;
import com.daddylab.msaiagent.db.aiAgent.entity.PersonaAttributeCategoryLvl1;
import com.daddylab.msaiagent.db.aiAgent.entity.PersonaAttributeCategoryLvl2;
import com.daddylab.msaiagent.db.aiAgent.entity.PersonaAttributeDefinition;
import com.daddylab.msaiagent.domain.dto.PersonAttributeDto;
import com.daddylab.msaiagent.domain.dto.PersonCategoryDto;
import com.daddylab.msaiagent.domain.dto.PersonGeneratorAiRespDto;
import com.daddylab.msaiagent.domain.enums.PersonSubjectTypeEnum;
import com.daddylab.msaiagent.service.person.PersonConstructService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class PersonConstructServiceImpl implements PersonConstructService {

    @Autowired
    private PersonaAttributeCategoryLvl1Dao lvl1Dao;

    @Autowired
    private PersonaAttributeCategoryLvl2Dao lvl2Dao;

    @Autowired
    private PersonaAttributeDefinitionDao attributeDao;


    @Override
    public List<PersonCategoryDto> constructPersonCategory(PersonSubjectTypeEnum subjectTypeEnum) {

        List<PersonaAttributeCategoryLvl1> lv1All = lvl1Dao.lambdaQuery().eq(PersonaAttributeCategoryLvl1::getIsEnabled, 1).list();
        Map<Long, List<PersonaAttributeCategoryLvl2>> lvl2Map = lvl2Dao.lambdaQuery().eq(PersonaAttributeCategoryLvl2::getIsEnabled, 1).list().stream().collect(Collectors.groupingBy(PersonaAttributeCategoryLvl2::getLvl1CategoryId));
        List<PersonCategoryDto> dtoList = new ArrayList<>(lv1All.size());
        for (PersonaAttributeCategoryLvl1 lvl1 : lv1All) {
            if (lvl1.getApplicableSubjectTypesJson() != null && !lvl1.getApplicableSubjectTypesJson().isEmpty()) {
                if (!lvl1.getApplicableSubjectTypesJson().contains(String.valueOf(subjectTypeEnum.getCode()))) {
                    continue;
                }
            }
            PersonCategoryDto dto = new PersonCategoryDto();
            dto.setCategoryName(lvl1.getCategoryName());
            dto.setDescription(lvl1.getDescription());
            dto.setLvl1CategoryId(lvl1.getId());
            List<PersonaAttributeCategoryLvl2> lvl2List = lvl2Map.get(lvl1.getId());
            if (null != lvl2List) {
                for (PersonaAttributeCategoryLvl2 lvl2 : lvl2List) {
                    if (lvl2.getApplicableSubjectTypesJson() != null && !lvl2.getApplicableSubjectTypesJson().isEmpty()) {
                        if (!lvl2.getApplicableSubjectTypesJson().contains(String.valueOf(subjectTypeEnum.getCode()))) {
                            continue;
                        }
                    }
                    dto.addLvl2Category(lvl2);
                }
            }
            dtoList.add(dto);
        }
        return dtoList;
    }

    @Override
    public List<PersonAttributeDto> constructPersonAttributes(PersonSubjectTypeEnum subjectTypeEnum) {
        List<PersonaAttributeCategoryLvl2> lv2All = lvl2Dao.lambdaQuery().eq(PersonaAttributeCategoryLvl2::getIsEnabled, 1).list().stream().filter(lvl2 -> {
            if (lvl2.getApplicableSubjectTypesJson() != null && !lvl2.getApplicableSubjectTypesJson().isEmpty()) {
                return lvl2.getApplicableSubjectTypesJson().contains(String.valueOf(subjectTypeEnum.getCode()));
            }
            return true;
        }).toList();
        List<PersonaAttributeDefinition> attributes = attributeDao.lambdaQuery().in(PersonaAttributeDefinition::getLvl2CategoryId, lv2All.stream().map(PersonaAttributeCategoryLvl2::getId).toList()).eq(PersonaAttributeDefinition::getIsEnabled, 1).list();
        return attributes.stream().map(PersonAttributeDto::new).toList();
    }

    @Override
    public Map<String, PersonaAttributeDefinition> constructPerson() {
        List<PersonaAttributeDefinition> attributes = attributeDao.lambdaQuery().eq(PersonaAttributeDefinition::getIsEnabled, 1).list();
        return attributes.stream().collect(Collectors.toMap(PersonaAttributeDefinition::getAttributeKey,
                attribute -> attribute));
    }

    @Override
    public void updateConstruct(PersonGeneratorAiRespDto dto) {
        if(null == dto){
            return;
        }
        if(null != dto.getSuggestedNewCategories()){
            for (PersonGeneratorAiRespDto.SuggestedNewCategory category : dto.getSuggestedNewCategories()) {
                if(category.getLevel() == 1){
                    newCategoryLv1(category);
                }
                if(category.getLevel() == 2){
                    newCategoryLv2(category);
                }
            }
        }
        if(null != dto.getSuggestedNewPersonaAttributesDefinition()){
            for (PersonGeneratorAiRespDto.SuggestedNewAttribute attribute : dto.getSuggestedNewPersonaAttributesDefinition()) {
                newAttribute(attribute);
            }
        }
    }

    private void newCategoryLv1(PersonGeneratorAiRespDto.SuggestedNewCategory category){
        PersonaAttributeCategoryLvl1 lvl1 = new PersonaAttributeCategoryLvl1();
        lvl1.setCategoryName(category.getSuggestedName());
        lvl1.setDescription(category.getDescription());
        lvl1.setIsEnabled(1);
        lvl1Dao.save(lvl1);
    }

    private void newCategoryLv2(PersonGeneratorAiRespDto.SuggestedNewCategory category){
        PersonaAttributeCategoryLvl2 lvl2 = new PersonaAttributeCategoryLvl2();
        lvl2.setCategoryName(category.getSuggestedName());
        lvl2.setDescription(category.getDescription());
        lvl2.setIsEnabled(1);
        lvl2.setLvl1CategoryId(category.getParentLvl1CategoryIdIfLvl2());
        lvl2Dao.save(lvl2);
    }
    private void newAttribute(PersonGeneratorAiRespDto.SuggestedNewAttribute attribute) {
        PersonaAttributeDefinition definition = new PersonaAttributeDefinition();
        definition.setAttributeName(attribute.getSuggestedAttributeName());
        definition.setDescription(attribute.getSuggestedDescription());
        definition.setAttributeKey(attribute.getSuggestedAttributeKey());
        definition.setIsEnabled(1);
        definition.setDefaultValueStr(attribute.getValueToFill());
        definition.setDataTypeCode(attribute.getSuggestedDataTypeCode());
        definition.setIsMultiValue(attribute.getSuggestedIsMultiValue());
        definition.setStorageLocationHintCode(attribute.getSuggestedStorageLocationHintCode());
        definition.setLvl2CategoryId(attribute.getBelongsToLvl2CategoryId());
        attributeDao.save(definition);
    }
}
