package com.daddylab.msaiagent.service.person;

import com.daddylab.msaiagent.domain.form.PersonAiGeneratedForm;
import com.daddylab.msaiagent.domain.form.PersonUpdateByAiForm;
import com.daddylab.msaiagent.domain.vo.AiJobVO;

public interface PersonGeneratorService {

    AiJobVO generatePersonByAi(PersonAiGeneratedForm form);

    AiJobVO guidingQuestionAndUpdatePerson(PersonUpdateByAiForm qaForm);

    void parseDetailByChatRecord(Long uid);
}
