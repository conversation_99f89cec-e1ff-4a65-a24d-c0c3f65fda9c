package com.daddylab.msaiagent.service.person;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.daddylab.msaiagent.db.aiAgent.entity.PersonaCore;
import com.daddylab.msaiagent.db.aiAgent.entity.PersonaDetail;
import com.daddylab.msaiagent.domain.dto.PersonGeneratorAiRespDto;
import com.daddylab.msaiagent.domain.form.PersonCreateForm;
import com.daddylab.msaiagent.domain.vo.PersonBriefInfoVO;
import com.daddylab.msaiagent.domain.vo.PersonInfoVO;

import java.util.Map;

public interface PersonService {

    PersonaCore getPerson(Long uid);

    PersonaDetail getPersonDetail(Long uid);

    void updatePerson(Long uid, Map<String, Object> personData);

    PersonaCore createPerson(PersonCreateForm form);

    void updatePerson(Long personId,Long jobId, PersonGeneratorAiRespDto dto);

    PersonInfoVO getPersonInfo(Long uid);

    IPage<PersonBriefInfoVO> getPersonList(int pageNum, int pageSize);

    void personArchived(Long uid);
}
