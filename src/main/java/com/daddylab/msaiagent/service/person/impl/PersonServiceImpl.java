package com.daddylab.msaiagent.service.person.impl;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.msaiagent.common.utils.JsonUtil;
import com.daddylab.msaiagent.db.aiAgent.dao.PersonaCoreDao;
import com.daddylab.msaiagent.db.aiAgent.dao.PersonaDetailDao;
import com.daddylab.msaiagent.db.aiAgent.entity.AigcJob;
import com.daddylab.msaiagent.db.aiAgent.entity.PersonaCore;
import com.daddylab.msaiagent.db.aiAgent.entity.PersonaDetail;
import com.daddylab.msaiagent.domain.dto.PersonGeneratorAiRespDto;
import com.daddylab.msaiagent.domain.enums.PersonStatusEnum;
import com.daddylab.msaiagent.domain.form.PersonCreateForm;
import com.daddylab.msaiagent.domain.vo.PersonBriefInfoVO;
import com.daddylab.msaiagent.domain.vo.PersonInfoVO;
import com.daddylab.msaiagent.service.AigcJobService;
import com.daddylab.msaiagent.service.person.PersonConstructService;
import com.daddylab.msaiagent.service.person.PersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class PersonServiceImpl implements PersonService {

    @Autowired
    private PersonaCoreDao coreDao;

    @Autowired
    private PersonaDetailDao detailDao;

    @Autowired
    private PersonConstructService personConstructService;

    @Autowired
    AigcJobService aigcJobService;

    @Override
    public PersonaCore getPerson(Long uid) {
       return coreDao.getById(uid);
    }

    @Override
    public PersonaDetail getPersonDetail(Long uid) {
        return detailDao.lambdaQuery().eq(PersonaDetail::getPersonaId,uid).last("limit 1").one();
    }

    @Override
    public void updatePerson(Long uid, Map<String, Object> personData) {
        PersonaDetail detail = detailDao.lambdaQuery().eq(PersonaDetail::getPersonaId, uid).last("limit 1").one();
        String attributesStr = detail.getContent();
        Map<String, Object> attributes;
        if(null != attributesStr && !attributesStr.isEmpty()){
            attributes = JsonUtil.parseMap(attributesStr);
        }else {
            attributes = new HashMap<>();
        }
        attributes.putAll(personData);
        detail.setContent(JsonUtil.toJSONString(attributes));
        detail.setJobId(0L);
        detailDao.save(detail);
    }

    @Override
    public PersonaCore createPerson(PersonCreateForm form) {
        PersonaCore core = new PersonaCore();
        core.setPersonaUuid(RandomUtil.randomStringUpper(4)+"-"+RandomUtil.randomNumbers(6));
        core.setSubjectType(form.getSubjectType().getCode());
        core.setCreationSource(form.getCreationSource().getCode());
        core.setPrimaryMonetizationModelCode(form.getPrimaryMonetizationModel().getCode());
        core.setStatus(PersonStatusEnum.DRAFT.getCode());
        coreDao.save(core);
        return core;
    }

    @Override
    public void updatePerson(Long personId,Long jobId, PersonGeneratorAiRespDto dto) {
        PersonaCore persona = coreDao.getById(personId);
        if(null == persona){
            return;
        }
        personConstructService.updateConstruct(dto);
        PersonaDetail detail = detailDao.lambdaQuery().eq(PersonaDetail::getPersonaId, personId).last("limit 1").one();
        if(null == detail) {
            detail = new PersonaDetail();
            detail.setPersonaId(personId);
            detail.setPersonaUuid(persona.getPersonaUuid());
            detail.setJobId(jobId);
            if(null != dto && null != dto.getPersonaAttributes()) {
                detail.setContent(JsonUtil.toJSONString(dto.getPersonaAttributes()));
            }
            detailDao.save(detail);
            return;
        }
        detail.setJobId(jobId);
        if(null != dto && null != dto.getPersonaAttributes()) {
            String attributesStr = detail.getContent();
            Map<String, Object> attributes;
            if(null != attributesStr && !attributesStr.isEmpty()){
                attributes = JsonUtil.parseMap(attributesStr);
            }else {
                attributes = new HashMap<>();
            }
            attributes.putAll(dto.getPersonaAttributes());
            detail.setContent(JsonUtil.toJSONString(attributes));
        }
        detailDao.updateById(detail);
    }

    @Override
    public PersonInfoVO getPersonInfo(Long uid) {
        PersonaCore core = coreDao.getById(uid);
        if(null != core){
            PersonInfoVO personInfo = new PersonInfoVO();
            personInfo.setCore(core);
            PersonaDetail detail = detailDao.lambdaQuery().eq(PersonaDetail::getPersonaId, uid).last("limit 1").one();
            if(null != detail){
                Long jobId = detail.getJobId();
                if(null != jobId && jobId > 0){
                    AigcJob job = aigcJobService.getJob(jobId);
                    if(null != job){
                        personInfo.setAiQuestions(JsonUtil.parseObjectList(job.getAiQuestions(),String.class));
                        personInfo.setUpdateNotes(JsonUtil.parseObjectList(job.getUpdateNotes(),String.class));
                    }
                }
                personInfo.setPersonaAttributes(JsonUtil.parseMap(detail.getContent()));
            }
            return personInfo;
        }
        return null;
    }

    @Override
    public IPage<PersonBriefInfoVO> getPersonList(int pageNum, int pageSize) {
        LambdaQueryChainWrapper<PersonaCore> query = coreDao.lambdaQuery();
        IPage<PersonaCore> lists = coreDao.page(new Page<>(pageNum,pageSize), query);
        return lists.convert(core -> {
            PersonBriefInfoVO personBriefInfo = new PersonBriefInfoVO();
            personBriefInfo.setStatus(core.getStatus());
            personBriefInfo.setSubjectType(core.getSubjectType());
            personBriefInfo.setCreationSource(core.getCreationSource());
            personBriefInfo.setPrimaryMonetizationModelCode(core.getPrimaryMonetizationModelCode());
            personBriefInfo.setPersonaUuid(core.getPersonaUuid());
            personBriefInfo.setId(core.getId());
            personBriefInfo.setCorePositioningTagsJson(core.getCorePositioningTagsJson());
            return personBriefInfo;
        });
    }

    @Override
    public void personArchived(Long uid) {
        PersonaCore person = coreDao.getById(uid);
        if(null == person){
            return;
        }
        person.setStatus(PersonStatusEnum.ARCHIVED.getCode());
        person.setDeletedAt(System.currentTimeMillis()/1000);
        coreDao.updateById(person);
    }
}
