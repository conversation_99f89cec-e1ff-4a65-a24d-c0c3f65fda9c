package com.daddylab.msaiagent.service.impl;

import com.daddylab.msaiagent.db.aiAgent.dao.PromptDao;
import com.daddylab.msaiagent.db.aiAgent.entity.Prompt;
import com.daddylab.msaiagent.db.aiAgent.enums.BooleanEnum;
import com.daddylab.msaiagent.db.aiAgent.enums.PromptTypeEnum;
import com.daddylab.msaiagent.service.PromptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * @className PromptServiceImpl
 * <AUTHOR>
 * @date 2025/5/8 13:50
 * @description: TODO 
 */
@Service
public class PromptServiceImpl implements PromptService {
    @Autowired private PromptDao promptDao;

    @Override
    public Prompt getPromptActive(PromptTypeEnum typeEnum) {
        return promptDao.lambdaQuery()
                .eq(Prompt::getType, typeEnum)
                .eq(Prompt::getActive, BooleanEnum.TRUE)
                .one();
    }

    @Override
    public Boolean save(Prompt promptModel) {
        return promptDao.saveOrUpdate(promptModel);
    }
}
