package com.daddylab.msaiagent.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.daddylab.msaiagent.common.base.exception.AiAgentErrorCodeEnum;
import com.daddylab.msaiagent.common.base.exception.AiAgentException;
import com.daddylab.msaiagent.common.utils.JsonUtil;
import com.daddylab.msaiagent.db.aiAgent.dao.ProductMaterialDao;
import com.daddylab.msaiagent.db.aiAgent.dao.SceneFragmentDao;
import com.daddylab.msaiagent.db.aiAgent.entity.ModelChat;
import com.daddylab.msaiagent.db.aiAgent.entity.ProductMaterial;
import com.daddylab.msaiagent.db.aiAgent.entity.SceneFragment;
import com.daddylab.msaiagent.domain.constants.ChatTypeConst;
import com.daddylab.msaiagent.domain.dto.ProductSceneGenerateResult;
import com.daddylab.msaiagent.domain.form.SceneFragmentGeneratorForm;
import com.daddylab.msaiagent.domain.request.ProductSceneGeneratorRequest;
import com.daddylab.msaiagent.prompt.ScenePrompts;
import com.daddylab.msaiagent.service.ModelChatService;
import com.daddylab.msaiagent.service.ProductSceneGeneratorService;
import com.daddylab.msaiagent.util.JsonExtractor;
import com.daddylab.msaiagent.util.JsonSchemaUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.util.json.schema.JsonSchemaGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

@Service
@Slf4j
public class ProductSceneGeneratorServiceImpl implements ProductSceneGeneratorService {
    @Autowired private SceneFragmentDao sceneFragmentDao;
    @Autowired private ModelChatService modelChatService;
    @Autowired private ProductMaterialDao productMaterialDao;

    @Override
    public SceneFragment applyChat(Long sceneId, Long chatId) {
        SceneFragment sceneFragment = sceneFragmentDao.getById(sceneId);
        Assert.notNull(sceneFragment, "场景片段不存在");
        ModelChat chat = modelChatService.getChat(chatId);
        Assert.notNull(chat, "对话不存在");
        List<Message> chatMessages = modelChatService.getChatMessages(chat);
        Message last = chatMessages.getLast();
        updateScene(last.getText(), sceneFragment);
        return sceneFragment;
    }

    @Override
    public SceneFragment generate(ProductSceneGeneratorRequest request) {
        ProductMaterial productMaterial =
                productMaterialDao.getById(request.getProductMaterialId());
        if (productMaterial == null) {
            throw AiAgentException.throwException(
                    AiAgentErrorCodeEnum.RESOURCE_NOT_FOUND, "商品素材不存在");
        }
        SceneFragment sceneFragment = new SceneFragment();
        sceneFragment.setFragmentUuid(IdUtil.fastUUID());
        sceneFragment.setFragmentName("Generating...");
        sceneFragment.setOutputPromptSegment("");
        sceneFragment.setIsEnabled(0);
        sceneFragment.setCreatedAt(DateUtil.currentSeconds());
        sceneFragment.setUpdatedAt(DateUtil.currentSeconds());
        sceneFragment.setCategoryTypeCode(request.getSceneCategorySuggestionCode().getCode());
        sceneFragmentDao.save(sceneFragment);
        ModelChat chat =
                modelChatService.createChat(
                        "google/gemini-2.5-pro-preview",
                        ChatTypeConst.AI_GENERATOR_SCENE_FRAGMENT
                                + "_"
                                + request.getProductMaterialId());
        String prompt = ScenePrompts.SCENE_GENERATE;
        prompt =
                prompt.replace("[target_platform_hint]", request.getTargetPlatformHint())
                        .replace(
                                "[scene_category_name_suggestion]",
                                request.getSceneCategoryNameSuggestion())
                        .replace("[user_input]", JsonSchemaUtils.toJSONStringWithSchema(request))
                        .replace(
                                "[output_schema]",
                                JsonSchemaUtils.generateSchema(ProductSceneGenerateResult.class))
                        .replace(
                                "[product_material]",
                                JsonSchemaUtils.toJSONStringWithSchema(productMaterial));
        modelChatService.startChatAsync(
                chat,
                prompt,
                (String output) -> {
                    updateScene(output, sceneFragment);
                });
        return sceneFragment;
    }

    private void updateScene(String output, SceneFragment sceneFragment) {
        log.info("开始保存场景片段:{}", output);
        String extractJSON = JsonExtractor.extractJson(output);
        log.info("提取场景片段JSON:{}", extractJSON);
        ProductSceneGenerateResult generated =
                JsonUtil.parseObject(extractJSON, ProductSceneGenerateResult.class);
        sceneFragment.setFragmentName(generated.getFragmentName());
        sceneFragment.setDescription(generated.getDescription());
        sceneFragment.setCategoryTypeCode(generated.getCategoryTypeCode().getCode());
        sceneFragment.setSceneElementsJson(JsonUtil.toJSONString(generated.getSceneElementsJson()));
        sceneFragment.setPersonaTagApplicabilityJson(
                JsonUtil.toJSONString(generated.getPersonaTagApplicabilityJson()));
        sceneFragment.setSubjectTypeApplicabilityJson(
                JsonUtil.toJSONString(generated.getSubjectTypeApplicabilityJson()));
        sceneFragment.setCulturalTagsJson(generated.getCulturalTagsJson());
        sceneFragment.setRegionSpecificityTagsJson(generated.getRegionSpecificityTagsJson());
        sceneFragment.setOutputPromptSegment(generated.getOutputPromptSegment());
        sceneFragment.setInteractionPromptTemplate(generated.getInteractionPromptTemplate());
        sceneFragment.setIsInteractiveFocused(generated.getIsInteractiveFocused() ? 1 : 0);
        sceneFragment.setExpansionPointsDefinitionJson(
                generated.getExpansionPointsDefinitionJson());
        sceneFragment.setEmotionalToneTagsJson(generated.getEmotionalToneTagsJson());
        sceneFragment.setNarrativeFunctionTagsJson(generated.getNarrativeFunctionTagsJson());
        sceneFragment.setTimelinessTagsJson(generated.getTimelinessTagsJson());
        sceneFragment.setUniquenessRating(generated.getUniquenessRating());
        sceneFragment.setUsageFrequencyCounter(generated.getUsageFrequencyCounter());
        sceneFragment.setTriggerKeywordsJson(generated.getTriggerKeywordsJson());
        sceneFragment.setUsageGuidelinesOrConstraints(generated.getUsageGuidelinesOrConstraints());
        sceneFragmentDao.updateById(sceneFragment);
    }
}
