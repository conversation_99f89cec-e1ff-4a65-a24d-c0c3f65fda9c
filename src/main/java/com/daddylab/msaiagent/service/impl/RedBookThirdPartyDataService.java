package com.daddylab.msaiagent.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.daddylab.msaiagent.common.utils.JacksonUtil;
import com.daddylab.msaiagent.common.utils.JsonUtil;
import com.daddylab.msaiagent.db.aiAgent.dao.RedBookNoteRankingDao;
import com.daddylab.msaiagent.db.aiAgent.entity.RedBookNoteRanking;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> up
 * @date 2025年05月12日 4:59 PM
 */

@Service
@Slf4j
// 数据价值不大，暂时放弃
@Deprecated
public class RedBookThirdPartyDataService {

    /**
     * https://xh.newrank.cn/notes/notesRank/hot
     */
    private static final String PLATFORM_NAME = "新红";

    @Autowired
    RedBookNoteRankingDao redBookNoteRankingDao;

    private final RestTemplate restTemplate = new RestTemplate();

    private final String authToken = "A969CE2EB13644B4913D157ACE03DBC4";

    private final String authN = "XS4ZpK4USm3o/oY8RGZrgt2PR2KRPlUzo6OeFc+Gl8jEdwHd5VrcBM0oXEQKS7zo";

    private final String nToken = "35c430ef650b459ba2b9c1409148d929";

    private static final String BASE_URL = "https://gw.newrank.cn/api/xh/xdnphb/nr/app/xhs/rank";


    /**
     * 这个可以每天跑
     */
    public void fetchSurgeNoteRank() {
//        for (int i = 2; i < 32; i++) {
//            final LocalDateTime localDateTime = LocalDateTime.now().plusDays(-i);
        // 单日增量榜：指笔记发布后第3天与第2天相比，传播1天后的增量榜单。
        final LocalDateTime localDateTime = LocalDateTime.now().plusDays(-2);
        final String riseDate = DateUtil.format(localDateTime, "yyyy-MM-dd");
        final String staticsTimeVal = DateUtil.format(localDateTime, "yyyyMMdd");
        fetchSurgeNoteRank0(riseDate, staticsTimeVal, 1);
        try {
            TimeUnit.SECONDS.sleep(5);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        // 七日增量榜：指笔记发布后第8天与第1天相比，传播7天后的增量榜单。
        final LocalDateTime localDateTime2 = LocalDateTime.now().plusDays(-8);
        final String riseDate2 = DateUtil.format(localDateTime2, "yyyy-MM-dd");
        final String staticsTimeVal2 = DateUtil.format(localDateTime2, "yyyyMMdd");
        fetchSurgeNoteRank0(riseDate2, staticsTimeVal2, 2);
        try {
            TimeUnit.SECONDS.sleep(5);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

//        }
    }


    /**
     * 延迟两天才能确保拉取到
     * 比如13号拉11号数据。
     *
     * @param riseDate
     * @param staticsTimeVal
     */
    private void fetchSurgeNoteRank0(String riseDate, String staticsTimeVal, Integer dateTye) {
        // 获取笔记热度上升排行榜
        ResponseEntity<String> surgeResponse = getSurgeNoteRank(
                "家居家装",
                "",
                "1",
                riseDate + " 00:00:00+08",
                // 1：单日增量，2：七日增量
                dateTye,
                // 互动增量最高(只能白嫖这个)
                "interactive_count_incr",
                1,
                20);
        final SurgeResponse response = JacksonUtil.parseObject(surgeResponse.getBody(), SurgeResponse.class);
        if (Objects.isNull(response)) return;
        final List<SurgeDataDTO.ListDTO> list = response.getData().getList();
        log.info("拉取小红书笔记热度上升排行榜,日榜,日期:{},数量:{}", riseDate, list.size());
        if (CollUtil.isEmpty(list)) return;

        redBookNoteRankingDao.lambdaUpdate()
                .eq(RedBookNoteRanking::getRankingType, 0)
                .eq(RedBookNoteRanking::getNoteField, 0)
                .eq(RedBookNoteRanking::getStaticsTimeType, 0)
                .eq(RedBookNoteRanking::getStaticsTimeVal, staticsTimeVal)
                .eq(RedBookNoteRanking::getDateType, dateTye)
                .remove();

        final List<RedBookNoteRanking> collect = list.stream().map(val -> {
            RedBookNoteRanking ranking = new RedBookNoteRanking();
            ranking.setRankingType(0);
            ranking.setNoteField(0);
            ranking.setStaticsTimeType(0);
            ranking.setStaticsTimeVal(staticsTimeVal);
            ranking.setSourceData(JsonUtil.toJSONString(val));
            ranking.setSourceId(val.getNoteId());
            ranking.setDateType(dateTye);
            return ranking;
        }).collect(Collectors.toList());
        redBookNoteRankingDao.saveBatch(collect);

    }

    /**
     * 一个月跑一次，只能拉上一个月的。
     */
    public void fetchBusinessNoteRank() {
//        for (int i = 1; i < 6; i++) {
        final LocalDateTime localDateTime = LocalDateTime.now().plusMonths(-1);
        final String riseDate = DateUtil.format(localDateTime, "yyyy-MM") + "-01";
        fetchBusinessNoteRank0(riseDate);
        try {
            TimeUnit.SECONDS.sleep(5);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
//        }
    }


    private void fetchBusinessNoteRank0(String rankDate) {
        // 获取商业笔记排行榜
        ResponseEntity<String> businessResponse = getBusinessNoteRank(
                "家居家装",
                "",
                "3",
                3,
                rankDate,
                "liked_count",
                1,
                20);
        final BusinessResponse response = JacksonUtil.parseObject(businessResponse.getBody(), BusinessResponse.class);
        if (Objects.isNull(response)) return;
        final List<BusinessDataDTO.ListDTO> list = response.getData().getList();
        log.info("拉取小红书获取商业笔记排行榜,月榜,榜单月份:{},数量:{}", rankDate, list.size());
        if (CollUtil.isEmpty(list)) return;

        redBookNoteRankingDao.lambdaUpdate()
                .eq(RedBookNoteRanking::getRankingType, 1)
                .eq(RedBookNoteRanking::getNoteField, 0)
                .eq(RedBookNoteRanking::getStaticsTimeType, 2)
                .eq(RedBookNoteRanking::getStaticsTimeVal, rankDate)
                .remove();


        final List<RedBookNoteRanking> collect = list.stream().map(val -> {
            RedBookNoteRanking ranking = new RedBookNoteRanking();
            ranking.setRankingType(1);
            ranking.setNoteField(0);
            ranking.setStaticsTimeType(2);
            ranking.setStaticsTimeVal(rankDate);
            ranking.setSourceData(JsonUtil.toJSONString(val));
            ranking.setSourceId(val.getId());
            return ranking;
        }).collect(Collectors.toList());
        redBookNoteRankingDao.saveBatch(collect);
    }


    /**
     * 每天跑
     */
    public void fetchLowFanNoteRank() {
        final String runTime = DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd");
        final String staticVal = DateUtil.format(LocalDateTime.now(), "yyyyMMdd");

        ResponseEntity<String> lowFanResponse = getLowFanSurgeRank(
                "家居家装",
                "3",
                "3",
                "interactive_count",
                1,
                20);
        final LowFanResponse response = JacksonUtil.parseObject(lowFanResponse.getBody(), LowFanResponse.class);
        if (Objects.isNull(response)) return;
        final List<LowFanDataDto.ListDTO> list = response.getData().getList();
        log.info("拉取小红书笔记热度上升排行榜,最近7天,拉取执行日期:{},数量:{}", runTime, list.size());
        if (CollUtil.isEmpty(list)) return;

        redBookNoteRankingDao.lambdaUpdate()
                .eq(RedBookNoteRanking::getRankingType, 2)
                .eq(RedBookNoteRanking::getNoteField, 0)
                .eq(RedBookNoteRanking::getStaticsTimeType, 5)
                .eq(RedBookNoteRanking::getStaticsTimeVal, runTime)
                .remove();

        final List<RedBookNoteRanking> collect = list.stream().map(val -> {
            RedBookNoteRanking ranking = new RedBookNoteRanking();
            ranking.setRankingType(2);
            ranking.setNoteField(0);
            ranking.setStaticsTimeType(5);
            ranking.setStaticsTimeVal(staticVal);
            ranking.setSourceData(JsonUtil.toJSONString(val));
            ranking.setSourceId(val.getId());
            return ranking;
        }).collect(Collectors.toList());
        redBookNoteRankingDao.saveBatch(collect);
    }

    /**
     * 获取小红书笔记热度上升排行榜
     *
     * @param noteCategory  笔记类别，如"家居家装"
     * @param noteCategory2 笔记子类别，可为空
     * @param noteType      笔记类型，如"1"
     * @param riseDate      上升日期，格式如"2025-05-11 00:00:00+08"
     * @param dateType      日期类型，如1
     * @param sort          排序字段，如"interactive_count_incr"
     * @param start         起始页码
     * @param size          每页大小
     * @return 响应数据
     */
    public ResponseEntity<String> getSurgeNoteRank(
            String noteCategory,
            String noteCategory2,
            String noteType,
            String riseDate,
            Integer dateType,
            String sort,
            Integer start,
            Integer size) {

        String url = BASE_URL + "/surgeNoteNewRank";

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("noteCategory", noteCategory);
        requestBody.put("noteCategory2", noteCategory2);
        requestBody.put("noteType", noteType);
        requestBody.put("riseDate", riseDate);
        requestBody.put("dateType", dateType);
        requestBody.put("sort", sort);
        requestBody.put("start", start);
        requestBody.put("size", size);

        HttpHeaders headers = createCommonHeaders();

        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

        return restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
    }

    /**
     * 获取小红书商业笔记排行榜
     *
     * @param type      类别，如"家居家装"
     * @param type2     子类别，可为空
     * @param photoType 图片类型，如"3"
     * @param dateType  日期类型，如3
     * @param rankDate  排名日期，格式如"2025-04-01"
     * @param sort      排序字段，如"liked_count"
     * @param start     起始页码
     * @param size      每页大小
     * @return 响应数据
     */
    public ResponseEntity<String> getBusinessNoteRank(
            String type,
            String type2,
            String photoType,
            Integer dateType,
            String rankDate,
            String sort,
            Integer start,
            Integer size) {

        String url = BASE_URL + "/businessNoteRank";

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("type", type);
        requestBody.put("type2", type2);
        requestBody.put("photoType", photoType);
        requestBody.put("dateType", dateType);
        requestBody.put("rankDate", rankDate);
        requestBody.put("sort", sort);
        requestBody.put("start", start);
        requestBody.put("size", size);

        HttpHeaders headers = createCommonHeaders();

        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

        return restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
    }

    /**
     * 获取小红书低粉作者爆发榜
     *
     * @param type      类别，如"家居家装"
     * @param photoType 图片类型，如"3"
     * @param dateType  日期类型，如"3"
     * @param sort      排序字段，如"interactive_count"
     * @param start     起始页码
     * @param size      每页大小
     * @return 响应数据
     */
    public ResponseEntity<String> getLowFanSurgeRank(
            String type,
            String photoType,
            String dateType,
            String sort,
            Integer start,
            Integer size) {

        String url = BASE_URL + "/lowFanSurgeRank";

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("type", type);
        requestBody.put("photoType", photoType);
        requestBody.put("dateType", dateType);
        requestBody.put("sort", sort);
        requestBody.put("start", start);
        requestBody.put("size", size);

        HttpHeaders headers = createCommonHeaders();

        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

        return restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
    }

    /**
     * 创建通用的HTTP请求头
     *
     * @return HTTP头信息
     */
    private HttpHeaders createCommonHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Accept", "*/*");
        headers.set("Accept-Language", "zh-CN,zh;q=0.9");
        headers.set("Connection", "keep-alive");
        headers.set("Origin", "https://xh.newrank.cn");
        headers.set("Referer", "https://xh.newrank.cn/");
        headers.set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36");
        headers.set("n-token", nToken);
        headers.set("request_id", UUID.randomUUID().toString().replace("-", ""));
        headers.set("sec-ch-ua", "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"");
        headers.set("sec-ch-ua-mobile", "?0");
        headers.set("sec-ch-ua-platform", "\"macOS\"");

        // 添加cookie信息
        headers.set("Cookie", "token=" + authToken + "; auth_n=" + authN);

        return headers;
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SurgeResponse {
        private String msg;
        private SurgeDataDTO data;
        private int code;
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SurgeDataDTO {
        private List<ListDTO> list;
        private Integer total;
        private Integer count;
        private String resultCount;
        private String updateTime;

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class ListDTO {
            private String noteId;
            private String rankDate;
            private String rankType;
            private String anaTime;
            private String cover;
            private String createTime;
            private String noteCounterTypeV1;
            private String noteCounterTypeV2;
            private String officialKeyword;
            private String title;
            private String type;
            private String userid;
            private Integer likedCount;
            private Integer likedCountIncr;
            private Integer collectedCount;
            private Integer collectedCountIncr;
            private Integer commentsCount;
            private Integer commentsCountIncr;
            private Integer interactiveCount;
            private Integer interactiveCountIncr;
            private Integer sharedCount;
            private Integer sharedCountIncr;
            private String imageb;
            private String nickname;
            private Integer userAttribute;
            private Integer readedCount;
            private Integer predReadnum;
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BusinessResponse {
        private String msg;
        private BusinessDataDTO data;
        private int code;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BusinessDataDTO {
        private List<ListDTO> list;
        private Integer total;
        private Integer count;
        private String resultCount;
        private String updateTime;

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class ListDTO {
            private String id;
            private String title;
            private String imageb;
            private String type;
            private String nickname;
            private Integer userAttribute;
            private String userid;
            private List<String> officialKeyword;
            private String noteCounterTypeV2;
            private String noteCounterTypeV1;
            private String cover;
            private Integer likedCount;
            private Integer collectedCount;
            private String cooperateName;
            private String createTime;
            private Integer fans;
            private Object readedCount;
            private Integer commentsCount;
            private Integer interactiveCount;
            private Object interactivePercent;
            private String readCount;
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LowFanResponse {
        private String msg;
        private LowFanDataDto data;
        private int code;
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LowFanDataDto {
        private List<ListDTO> list;
        private int total;
        private int count;
        private String resultCount;
        private String update_time;


        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class ListDTO {
            private String id;
            private String title;
            private String imageb;
            private String type;
            private String nickname;
            private int userAttribute;
            private String userid;
            private List<String> officialKeyword;
            private String noteCounterTypeV2;
            private String noteCounterTypeV1;
            private String cover;
            private int likedCount;
            private int collectedCount;
            private int commentsCount;
            private int sharedCount;
            private String createTime;
            private int fans;
            private int interactiveCount;
            private String readCount;
        }
    }

}
