package com.daddylab.msaiagent.service.impl;

import com.daddylab.msaiagent.common.base.exception.AiAgentErrorCodeEnum;
import com.daddylab.msaiagent.common.base.exception.AiAgentException;
import com.daddylab.msaiagent.common.feign.YingDaoFeignClient;
import com.daddylab.msaiagent.common.feign.domain.query.YingDaoStartQuery;
import com.daddylab.msaiagent.common.feign.domain.query.YingDaoTaskResultQuery;
import com.daddylab.msaiagent.common.feign.domain.vo.YingDaoTaskQueryVo;
import com.daddylab.msaiagent.common.feign.domain.vo.YingDaoTaskStartVo;
import com.daddylab.msaiagent.common.feign.response.YingDaoResponse;
import com.daddylab.msaiagent.common.utils.JsonUtil;
import com.daddylab.msaiagent.service.YingDaoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @ClassName YingDaoServiceImpl.java
 * @Description 描述类的作用
 * @date 2023-11-28 17:37
 */
@Slf4j
@Service
public class YingDaoServiceImpl implements YingDaoService {

    @Autowired
    private YingDaoFeignClient yingDaoFeignClient;

    @Override
    public String runTask(String scheduleId) {
        return runTask(scheduleId, Collections.emptyList());
    }

    @Override
    public String runTask(String scheduleId, List<YingDaoStartQuery.ScheduleRelaParams> scheduleRelaParams) {
        try {
            log.info("[调用影刀] 执行影刀任务 scheduleId={}", scheduleId);
            YingDaoResponse<YingDaoTaskStartVo> startResponse = yingDaoFeignClient.start(YingDaoStartQuery.of(scheduleId, scheduleRelaParams));
            if (startResponse == null || !startResponse.getSuccess()) {
                throw AiAgentException.throwException(AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, "影刀调用失败");
            }
            return startResponse.getData().getTaskUuid();
        } catch (Exception e) {
            log.error("[调用影刀] 调用失败 scheduleId={}", scheduleId, e);
        }
        return null;
    }

    @Override
    public void registerCallBack(String taskUuId, long intervalSecond, Consumer<YingDaoTaskQueryVo> handler) {
        Flux.interval(Duration.ofSeconds(intervalSecond), Schedulers.parallel())
                .timeout(Duration.ofMinutes(5))
                .publishOn(Schedulers.boundedElastic())
                .flatMap(tick -> {
                    try {
                        YingDaoResponse<YingDaoTaskQueryVo> yingDaoTaskQueryVoYingDaoResponse = yingDaoFeignClient.queryResult(YingDaoTaskResultQuery.of(taskUuId));
                        log.info("[获取影刀任务] taskUuId={}, res={}, 第{}次获取", taskUuId, JsonUtil.toJSONString(yingDaoTaskQueryVoYingDaoResponse), tick);
                        return Mono.just(yingDaoTaskQueryVoYingDaoResponse);
                    } catch (Exception e) {
                        log.error("[获取影刀任务] 失败 taskUuId={}", taskUuId, e);
                    }
                    return  Mono.just(null);
                })
                .filter(yingDaoTaskQueryVoYingDaoResponse -> yingDaoTaskQueryVoYingDaoResponse != null && yingDaoTaskQueryVoYingDaoResponse.getSuccess())
                .map(YingDaoResponse::getData)
                .takeUntil(YingDaoTaskQueryVo::isFinal)
                .last()
                .subscribe(handler);
    }
}
