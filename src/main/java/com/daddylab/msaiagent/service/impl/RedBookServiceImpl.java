package com.daddylab.msaiagent.service.impl;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import com.daddylab.msaiagent.common.base.exception.AiAgentErrorCodeEnum;
import com.daddylab.msaiagent.common.base.exception.AiAgentException;
import com.daddylab.msaiagent.common.feign.domain.vo.YingDaoTaskQueryVo;
import com.daddylab.msaiagent.common.utils.HtmlUnitUtil;
import com.daddylab.msaiagent.common.utils.JsonUtil;

import cn.hutool.core.util.StrUtil;
import com.daddylab.msaiagent.common.config.property.YingDaoProperty;
import com.daddylab.msaiagent.common.crawler.XhsCrawler;
import com.daddylab.msaiagent.common.crawler.XhsLocalFileCrawler;
import com.daddylab.msaiagent.common.feign.domain.query.YingDaoStartQuery;
import com.daddylab.msaiagent.common.utils.PageUtils;
import com.daddylab.msaiagent.convert.RedBookConvert;
import com.daddylab.msaiagent.db.aiAgent.dao.RedBookDao;
import com.daddylab.msaiagent.db.aiAgent.entity.RedBook;
import com.daddylab.msaiagent.db.aiAgent.enums.BooleanEnum;
import com.daddylab.msaiagent.domain.vo.RedBookInfoVO;
import com.daddylab.msaiagent.domain.vo.XhsAccountListVO;
import com.daddylab.msaiagent.service.RedBookService;
import com.daddylab.msaiagent.service.YingDaoService;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 *
 * @className RedBookServiceImpl
 * <AUTHOR>
 * @date 2025/4/28 16:14
 * @description: TODO 
 */
@Slf4j
@Service
public class RedBookServiceImpl implements RedBookService {
    @Autowired
    private RedBookDao redBookDao;
    @Autowired
    private YingDaoService yingDaoService;
    @Autowired
    private YingDaoProperty yingDaoProperty;
    @Autowired
    private RedissonClient redissonClient;

    @Override
    public Boolean hasAccount(String accountId) {
        return redBookDao.lambdaQuery().eq(RedBook::getAccountId, accountId).exists();
    }

    @Override
    public Boolean hasEncryptAccount(String encryptAccountId) {
        return redBookDao.lambdaQuery().eq(RedBook::getAccountEncryptId, encryptAccountId).exists();
    }

    @Override
    public List<RedBook> getEncryptAccount(String encryptAccountId, Long size) {
        return redBookDao.lambdaQuery()
                .eq(RedBook::getAccountEncryptId, encryptAccountId)
                .eq(RedBook::getIsParser, BooleanEnum.TRUE).last("limit " + size)
                .list();
    }

    @Override
    public List<RedBook> getAccount(String accountId, Long size) {
        return redBookDao.lambdaQuery()
                .eq(RedBook::getAccountId, accountId)
                .eq(RedBook::getIsParser, BooleanEnum.TRUE).last("limit " + size)
                .list();
    }

    @Override
    public void freshCookie() {
        YingDaoProperty.ScheduleRobotConfig xhsAccountList = yingDaoProperty.getXhsAccountList();
        YingDaoStartQuery.ScheduleRelaParams scheduleRelaParams = new YingDaoStartQuery.ScheduleRelaParams();
        scheduleRelaParams.setRobotUuid(xhsAccountList.getRobotUuId());
        scheduleRelaParams.setParams(ListUtil.of(YingDaoStartQuery.ScheduleRelaParams.Params.ofString("url", XhsCrawler.EXPLORE_URL),
                YingDaoStartQuery.ScheduleRelaParams.Params.ofString("cate", "3")));
        String taskUuId = yingDaoService.runTask(xhsAccountList.getScheduleId(), Collections.singletonList(scheduleRelaParams));
        yingDaoService.registerCallBack(taskUuId, 2, disposeCallback());
    }

    @Override
    public void runCrawlDetail() {
        List<RedBook> redBooks = redBookDao.lambdaQuery()
                .eq(RedBook::getIsParser, BooleanEnum.FALSE)
                .list();
        for (RedBook redBook : redBooks) {
            if (StrUtil.isEmpty(redBook.getUrl())) {
                redBook.setIsParser(BooleanEnum.TRUE);
                redBookDao.updateById(redBook);
                continue;
            }
            doParser(redBook);
        }
    }

    public void doParser(RedBook redBook) {
        try {
            XhsCrawler.XhsDetailDTO xhsDetailDTO = XhsCrawler.doParserDetail(redBook.getUrl(), getCookie());
            if (xhsDetailDTO == null) {
                throw AiAgentException.throwException(AiAgentErrorCodeEnum.BUSINESS_ERROR, "爬取失败");
            }
            redBook.setSourceData(xhsDetailDTO.getJson());
            parserJson(redBook);
            redBook.setIsParser(BooleanEnum.TRUE);
            redBookDao.saveOrUpdate(redBook);
        } catch (Exception e) {
            log.error("[爬取小红书数据异常] url={}", redBook.getUrl(), e);
        }
    }

    private void parserJson(RedBook redBook) {
        String sourceData = redBook.getSourceData();
        JsonNode viewStateJSON = JsonUtil.parseObject(sourceData);
        String state = viewStateJSON.at("/note/serverRequestInfo/state").asText();
        if ("fail".equals(state)) {
            throw new RuntimeException("抓取失败");
        }
        String noteId = viewStateJSON.at("/note/currentNoteId").asText();
        redBook.setNoteId(noteId);
        JsonNode item = viewStateJSON.at("/note/noteDetailMap/" + noteId);
        JsonNode note = item.at("/note");
        // video:视频, normal:图文
        String type = note.at("/type").asText();
        String title = note.at("/title").asText();
        String desc = note.at("/desc").asText();
        Long time = note.at("/time").asLong();
        if (StrUtil.isNotEmpty(title)) {
            redBook.setTitle(title);
        }
        String nickname = note.at("/user/nickname").asText();
        String userId = viewStateJSON.at("/user/userId").asText();
        String redId = viewStateJSON.at("/user/userId").asText();
        if (StrUtil.isNotEmpty(redId)) {
            redBook.setAccountId(redId);
        }
        if (StrUtil.isNotEmpty(userId)) {
            redBook.setAccountEncryptId(userId);
        }
        if (StrUtil.isNotEmpty(nickname)) {
            redBook.setAccountName(nickname);
        }
        redBook.setContent(desc);
        redBook.setType("normal".equals(type) ? 1: 2);
        JsonNode imageList = note.at("/imageList");
        ArrayList<String> imageUrlList = Lists.newArrayList();
        for (JsonNode imageItem : imageList) {
            String imageUrl = imageItem.at("/urlDefault").asText();
            imageUrlList.add(imageUrl);
        }
        redBook.setImages(imageUrlList);
        if ("video".equals(type)) {
            String videoUrl = note.at("/video/media/stream/h264/0/masterUrl").asText();
            String videoFormat = note.at("/video/media/stream/h264/0/format").asText();
            if (StrUtil.isEmpty(videoUrl)) {
                videoUrl = note.at("/video/media/stream/h265/0/masterUrl").asText();
                videoFormat = note.at("/video/media/stream/h265/0/format").asText();
            }
            redBook.setVideos(Collections.singletonList(videoUrl));
        }
        JsonNode tagList = note.at("/tagList");
        List<String> tagNameList = new ArrayList<>();
        for (JsonNode jsonNode : tagList) {
            String tagName = jsonNode.at("/name").asText();
            tagNameList.add(tagName);
        }
        redBook.setTags(tagNameList);
        // 点赞数
        String likeCount = note.at("/interactInfo/likedCount").asText();
        // 收藏数
        String collectedCount = note.at("/interactInfo/collectedCount").asText();
        // 评论数
        String commentCount = note.at("/interactInfo/commentCount").asText();
        redBook.setUpvoteNum(likeCount);
        redBook.setCollectNum(collectedCount);
        redBook.setDiscussNum(commentCount);
        redBook.setLastUpdateTime(DateUtil.currentSeconds());
    }

    void freshDynamic(RedBook redBook) {
        String sourceData = redBook.getSourceData();
        JsonNode viewStateJSON = JsonUtil.parseObject(sourceData);
        String state = viewStateJSON.at("/note/serverRequestInfo/state").asText();
        if ("fail".equals(state)) {
            throw new RuntimeException("抓取失败");
        }
        String noteId = viewStateJSON.at("/note/currentNoteId").asText();
        redBook.setNoteId(noteId);
        JsonNode item = viewStateJSON.at("/note/noteDetailMap/" + noteId);
        JsonNode note = item.at("/note");
        // 点赞数
        String likeCount = note.at("/interactInfo/likedCount").asText();
        // 收藏数
        String collectedCount = note.at("/interactInfo/collectedCount").asText();
        // 评论数
        String commentCount = note.at("/interactInfo/commentCount").asText();
        redBook.setUpvoteNum(likeCount);
        redBook.setCollectNum(collectedCount);
        redBook.setDiscussNum(commentCount);
        redBook.setLastUpdateTime(DateUtil.currentSeconds());
    }


    @Override
    public void runCrawlAccountList(String url) {
        YingDaoProperty.ScheduleRobotConfig xhsAccountList = yingDaoProperty.getXhsAccountList();
        YingDaoStartQuery.ScheduleRelaParams scheduleRelaParams = new YingDaoStartQuery.ScheduleRelaParams();
        scheduleRelaParams.setRobotUuid(xhsAccountList.getRobotUuId());
        scheduleRelaParams.setParams(ListUtil.of(YingDaoStartQuery.ScheduleRelaParams.Params.ofString("url", url),
                YingDaoStartQuery.ScheduleRelaParams.Params.ofString("cate", "1")));
        String taskUuId = yingDaoService.runTask(xhsAccountList.getScheduleId(), Collections.singletonList(scheduleRelaParams));
        yingDaoService.registerCallBack(taskUuId, 10, disposeCallback());
    }

    @Override
    public Consumer<YingDaoTaskQueryVo> disposeCallback() {
        return taskQueryVo -> {
            log.info("[收到影刀回调] data={}", JsonUtil.toJSONString(taskQueryVo));
            if (taskQueryVo.getJobDataList().isEmpty()) {
                return;
            }
            YingDaoTaskQueryVo.JobDataList jobDataList = CollUtil.getFirst(taskQueryVo.getJobDataList());
            XhsAccountListVO xhsAccountListVO = outPutToXhsAccountListVO(jobDataList.getRobotParams().getOutputs());
            if (StrUtil.isNotEmpty(xhsAccountListVO.getCookie())) {
                doSaveCookie(xhsAccountListVO.getCookie());
            }
            if (StrUtil.isEmpty(xhsAccountListVO.getAccountId())) {
                return;
            }
            if (hasAccount(xhsAccountListVO.getAccountId())) {
                return;
            }
            doSave(xhsAccountListVO);
        };
    }

    @Override
    public void disposeSearchKeyword(String keyword, Integer size) {
        int batchSize = 20;
        PageUtils.batchCallback(pageIndex -> {
            XhsCrawler.SearchResultDTO searchResultDTO = XhsCrawler.searchKeyword(XhsCrawler.SearchRequestBodyDTO.ofMaxUpvote(pageIndex.intValue(), batchSize, keyword), getCookie());
            if (!searchResultDTO.getSuccess()) {
                throw AiAgentException.throwException(AiAgentErrorCodeEnum.BUSINESS_ERROR, "获取数据失败:" + searchResultDTO.getMsg());
            }
            return PageUtils.PageResponse.of(size.longValue(), searchResultDTO.getData().getItems(), batchSize);
        }, items -> {
            List<RedBook> redBookList = items.stream().map(item -> {
                Integer type = "video".equals(item.getNoteCard().getType()) ? 2 : 1;
                String userId = item.getNoteCard().getUser().getUserId();
                String url = getUrl(userId, item.getId(), item.getXsecToken());
                RedBook redBook = redBookDao.lambdaQuery().eq(RedBook::getUrl, url)
                        .eq(RedBook::getType, 2)
                        .eq(RedBook::getAccountId, userId)
                        .one();
                if (redBook == null ) {
                    redBook = new RedBook();
                    redBook.setAccountId(userId);
                    redBook.setAccountName(item.getNoteCard().getUser().getNickName());
                    redBook.setNoteId(item.getId());
                    redBook.setTitle(item.getNoteCard().getDisplayTitle());
                    redBook.setType(type);
                    redBook.setCateType(2);
                    redBook.setUrl(url);
                    redBook.setTags(Lists.newArrayList());
                    redBook.setImages(Lists.newArrayList());
                    redBook.setVideos(Lists.newArrayList());
                    redBook.setIsParser(BooleanEnum.FALSE);
                    redBook.setSourceData("");
                }
                return redBook;
            }).collect(Collectors.toList());
            redBookDao.saveOrUpdateBatch(redBookList);
        });
    }

    private String getUrl(String userId, String noteId, String xSecToken) {
        return String.format("https://www.xiaohongshu.com/user/profile/%s/%s?xsec_token=%s=&xsec_source=pc_user", userId, noteId, xSecToken);
    }


    void doSaveCookie(String cookie) {
        RBucket<String> bucket = redissonClient.getBucket(XhsCrawler.XHS_COOKIE_KEY);
        bucket.set(cookie, 48, TimeUnit.HOURS);
    }

    String getCookie() {
        RBucket<String> bucket = redissonClient.getBucket(XhsCrawler.XHS_COOKIE_KEY);
        return Optional.ofNullable(bucket.get()).orElse(StrUtil.EMPTY);
    }

    private void doSave(XhsAccountListVO xhsAccountListVO) {
        if (xhsAccountListVO == null) {
            return;
        }

        boolean exists = redBookDao.lambdaQuery().eq(RedBook::getAccountId, xhsAccountListVO.getAccountId()).exists();
        if (exists) {
            return;
        }
        List<RedBook> redBookList = xhsAccountListVO.getContents().stream().map(content -> {
            RedBook redBook = new RedBook();
            redBook.setAccountId(xhsAccountListVO.getAccountId());
            redBook.setAccountName(xhsAccountListVO.getAccountName());
            redBook.setTitle(content.getTitle());
            redBook.setUrl(content.getUrl());
            redBook.setIsParser(BooleanEnum.FALSE);
            return redBook;
        }).collect(Collectors.toList());
        redBookDao.saveBatch(redBookList);
    }

    private XhsAccountListVO outPutToXhsAccountListVO(List<YingDaoTaskQueryVo.JobDataList.RobotParams.Outputs> outputs) {
        if (outputs == null || outputs.size() < 4) {
            return null;
        }
        XhsAccountListVO xhsAccountListVO = new XhsAccountListVO();
        xhsAccountListVO.setAccountId(outputs.getFirst().getValue());
        xhsAccountListVO.setAccountName(outputs.get(1).getValue());
        String contentJson = outputs.get(2).getValue();
        if (StrUtil.isNotEmpty(contentJson)) {
            List<XhsAccountListVO.Contents> contentsList = JsonUtil.parseObjectList(contentJson, XhsAccountListVO.Contents.class);
            xhsAccountListVO.setContents(contentsList);
        }
        xhsAccountListVO.setCookie(outputs.getLast().getValue());
        return xhsAccountListVO;
    }

    @Override
    public void freshCrawlDetail(Integer intervalDay) {
        if (Objects.isNull(intervalDay)) {
            intervalDay = 2;
        }
        long intervalDayBefore =  intervalDay * 86400;
        List<RedBook> redBooks = redBookDao.lambdaQuery()
                .le(RedBook::getLastUpdateTime, intervalDayBefore)
                .list();
        for (RedBook redBook : redBooks) {
            try {
                XhsCrawler.XhsDetailDTO xhsDetailDTO = XhsCrawler.doParserDetail(redBook.getUrl(), getCookie());
                if (xhsDetailDTO == null) {
                    continue;
                }
                redBook.setSourceData(xhsDetailDTO.getJson());
                freshDynamic(redBook);
                redBookDao.updateById(redBook);
            } catch (Exception e) {
                log.error("[爬取小红书数据异常] url={}", redBook.getUrl(), e);
            }
        }
    }

    @Override
    public RedBookInfoVO getUrlInfo(String url) {
        RedBook redBook = redBookDao.lambdaQuery().eq(RedBook::getUrl, url).one();
        if (redBook != null) {
            if (!redBook.getIsParser().toBoolean() || StrUtil.isEmpty(redBook.getNoteId())) {
                doParser(redBook);
            }
            return RedBookConvert.INSTANCE.redBookToInfoVO(redBook);
        }
        redBook = new RedBook();
        redBook.setUrl(url);
        doParser(redBook);
        return RedBookConvert.INSTANCE.redBookToInfoVO(redBook);
    }

    public static void main(String[] args) {
        String cookie = "gid=yYdd8S8iJ0iJyYdd8S8d2MCYKSfSITv7SFkJhuykETq29l88dJvu6M888y4J44K8ijS2yK2d; abRequestId=7191223aad259f588d98d74c1033ac66; a1=1963c32e7269h4imeywasuhgmg86bqd0iou8n4nzt30000540602; webId=349bb92ae27777983dd3fa2becaf6176; web_session=0400698c3176b10990cc1b8d243a4b00fc0f2a; webBuild=4.64.0; x-user-id-creator.xiaohongshu.com=5d4535f9000000001603c09a; customerClientId=844325215919016; customer-sso-sid=68c517509065540684449703rdm0x14pcx5lsttx; access-token-creator.xiaohongshu.com=customer.creator.AT-68c517509065540684449705ylfj1h4mpmx6q1tc; galaxy_creator_session_id=0nSjmEEs9KdhZM61mV5HkUAU1flkQNNIv8Pe; galaxy.creator.beaker.session.id=1748340563264020022607; xsecappid=xhs-pc-web; websectiga=634d3ad75ffb42a2ade2c5e1705a73c845837578aeb31ba0e442d75c648da36a; sec_poison_id=5dae67f5-94c9-4915-860c-19930f804e24; loadts=1748341098102";
//        String url = "https://www.xiaohongshu.com/user/profile/615d153600000000020185d8/63320cf300000000170188c4?xsec_token=ABIf5_BOlxyGs3hlimRu4QTl9epIy1VKwg3tlZhFNd_Vo=&xsec_source=pc_user";
        String exploreUrl = "https://www.xiaohongshu.com/explore/6569fc1c000000003801e81f?xsec_token=ABwPpY4-SX-zkzzm5A3784du-ZyI4wVIBkweTG0uJwDbI=&xsec_source=pc_user";
        String exploreUrl1 = "https://www.xiaohongshu.com/explore/66f48aa2000000002c02d904?xsec_token=ABjat9saHRSLdthO-Dv8WrlGn_CXrEEv37tiXbVpyleZc=&xsec_source=pc_search&source=web_user_page";
//        String crawlingHtml = HtmlUnitUtil.crawlingRedBookHtml(url, cookie);
//        Document document = Jsoup.parse(crawlingHtml);
//        Elements upvoteNumStr = document.selectXpath("//*[@id=\"noteContainer\"]/div[4]/div[3]/div/div/div[1]/div[2]/div/div[1]/span[1]/span[2]");
//        Elements collectNumStr = document.selectXpath("//*[@id=\"note-page-collect-board-guide\"]/span");
//        Elements discussNumStr = document.selectXpath("//*[@id=\"noteContainer\"]/div[4]/div[3]/div/div/div[1]/div[2]/div/div[1]/span[3]/span");
//
        XhsCrawler.XhsDetailDTO xhsDetailDTO = XhsCrawler.doParserDetail(exploreUrl1, cookie);
        System.out.println(
                xhsDetailDTO.getJson()
        );

//        XhsCrawler.SearchResultDTO response = XhsCrawler.searchKeyword(XhsCrawler.SearchRequestBodyDTO.ofMaxUpvote(1, 20, "华润沄璟"), cookie);
//        System.out.println(
//                response
//        );
    }
}
