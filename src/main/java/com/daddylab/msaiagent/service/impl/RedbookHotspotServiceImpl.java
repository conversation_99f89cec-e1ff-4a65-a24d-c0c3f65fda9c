package com.daddylab.msaiagent.service.impl;

import cn.hutool.core.date.DateUtil;
import com.daddylab.msaiagent.common.config.property.YingDaoProperty;
import com.daddylab.msaiagent.common.crawler.XhsCrawler;
import com.daddylab.msaiagent.db.aiAgent.dao.RedBookDao;
import com.daddylab.msaiagent.db.aiAgent.dao.RedbookHotspotDao;
import com.daddylab.msaiagent.db.aiAgent.entity.RedbookHotspot;
import com.daddylab.msaiagent.service.RedbookHotspotService;
import com.daddylab.msaiagent.service.YingDaoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RedbookHotspotServiceImpl implements RedbookHotspotService {
    @Autowired
    private RedbookHotspotDao redbookHotspotDao;

    @Override
    public String getRank1(int days) {
        List<RedbookHotspot> list = redbookHotspotDao.getRank(1,days);
        if (list == null || list.isEmpty()) {
            return "没有数据";
        }
        // 根据startTime来group
        Map<String, List<RedbookHotspot>> groupedByDate = list.stream()
                .collect(Collectors.groupingBy(hotspot -> DateUtil.date(hotspot.getStartTime()*1000L).toString("yyyy年MM月dd日")));
        StringBuilder result = new StringBuilder();
        groupedByDate.forEach((date, hotspots) -> {
            result.append(date).append("，热点如下：\n");
            for (RedbookHotspot hotspot : hotspots) {
                result.append("  ").append(hotspot.getHotword())
                        .append(", 热度").append(hotspot.getHotVal() / 10000).append("万");
                if(hotspot.getHotAttr() == 1){
                    result.append("(搜索飙升)");
                }
                result.append(";\n");
            }
        });
        System.out.println(result.toString());
        return result.toString();
    }

    @Override
    public String getRank2(int days) {
        List<RedbookHotspot> list = redbookHotspotDao.getRank(2,days);
        if (list == null || list.isEmpty()) {
            return "没有数据";
        }
        // 预处理日期范围，减少重复计算
        Map<Long, String> dateRangeCache = list.stream()
                .collect(Collectors.toMap(
                        hotspot -> hotspot.getId(),
                        hotspot -> {
                            String startDate = DateUtil.date(hotspot.getStartTime()*1000L).toString("yyyy年MM月dd日");
                            String endDate = DateUtil.date(hotspot.getEndTime()*1000L).toString("yyyy年MM月dd日");
                            return startDate + " 至 " + endDate;
                        }
                ));
        // 根据日期范围分组
        Map<String, List<RedbookHotspot>> groupedByDateRange = list.stream()
                .collect(Collectors.groupingBy(hotspot -> dateRangeCache.get(hotspot.getId())));
        // 构建结果字符串
        StringBuilder result = new StringBuilder();
        groupedByDateRange.forEach((dateRange, hotspots) -> {
            result.append(dateRange).append("，热点如下：\n");
            for (RedbookHotspot hotspot : hotspots) {
                result.append("  ").append(hotspot.getHotword())
                        .append(", 热度").append(hotspot.getHotVal() / 10000).append("万")
                        .append(", 关联笔记数").append(hotspot.getNoteVal())
                                .append(", 关联笔记数").append(hotspot.getNoteVal())
                                .append(", 关联笔记互动量").append(hotspot.getInteractVal())
                                .append(", 新增笔记数").append(hotspot.getNoteNewVal())
                        .append(", 关联分类").append(hotspot.getNoteNewVal()).append(";\n");
            }
        });
        System.out.println(result.toString());
        return result.toString();
    }

    @Override
    public String getRank3(int days) {
        List<RedbookHotspot> list = redbookHotspotDao.getRank(3,days);
        if (list == null || list.isEmpty()) {
            return "没有数据";
        }
        // 预处理日期范围，减少重复计算
        Map<Long, String> dateRangeCache = list.stream()
                .collect(Collectors.toMap(
                        hotspot -> hotspot.getId(),
                        hotspot -> {
                            String startDate = DateUtil.date(hotspot.getStartTime()*1000L).toString("yyyy年MM月dd日");
                            String endDate = DateUtil.date(hotspot.getEndTime()*1000L).toString("yyyy年MM月dd日");
                            return startDate + " 至 " + endDate;
                        }
                ));
        // 根据日期范围分组
        Map<String, List<RedbookHotspot>> groupedByDateRange = list.stream()
                .collect(Collectors.groupingBy(hotspot -> dateRangeCache.get(hotspot.getId())));
        // 构建结果字符串
        StringBuilder result = new StringBuilder();
        groupedByDateRange.forEach((dateRange, hotspots) -> {
            result.append(dateRange).append("，热点如下：\n");
            for (RedbookHotspot hotspot : hotspots) {
                result.append("  ").append(hotspot.getHotword())
                        .append(", 热度").append(hotspot.getHotVal() / 10000).append("万")
                        .append(", 关联笔记数").append(hotspot.getNoteVal())
                        .append(", 关联笔记数").append(hotspot.getNoteVal())
                        .append(", 关联笔记互动量").append(hotspot.getInteractVal())
                        .append(", 新增笔记数").append(hotspot.getNoteNewVal())
                        .append(", 关联分类").append(hotspot.getNoteNewVal()).append(";\n");
            }
        });
        System.out.println(result.toString());
        return result.toString();
    }

    @Override
    public String getRank4(int days) {
        List<RedbookHotspot> list = redbookHotspotDao.getRank(4,days);
        if (list == null || list.isEmpty()) {
            return "没有数据";
        }
        // 根据startTime来group
        Map<String, List<RedbookHotspot>> groupedByDate = list.stream()
                .collect(Collectors.groupingBy(hotspot -> DateUtil.date(hotspot.getStartTime()*1000L).toString("yyyy年MM月dd日")));
        StringBuilder result = new StringBuilder();
        groupedByDate.forEach((date, hotspots) -> {
            result.append(date).append("，热点如下：\n");
            for (RedbookHotspot hotspot : hotspots) {
                result.append("  ").append(hotspot.getHotword())
                        .append(", 热度").append(hotspot.getHotVal() / 10000).append("万").append(";\n");
            }
        });
        System.out.println(result.toString());
        return result.toString();
    }

}
