package com.daddylab.msaiagent.service.impl;

import cn.hutool.core.lang.Assert;
import com.daddylab.msaiagent.agent.NoteTagAgent;
import com.daddylab.msaiagent.agent.VirtualPersonAgent;
import com.daddylab.msaiagent.db.aiAgent.dao.NoteDao;
import com.daddylab.msaiagent.db.aiAgent.entity.Note;
import com.daddylab.msaiagent.db.aiAgent.entity.Prompt;
import com.daddylab.msaiagent.db.aiAgent.entity.PromptTemplate;
import com.daddylab.msaiagent.db.aiAgent.enums.PromptTypeEnum;
import com.daddylab.msaiagent.domain.enums.AIModelEnum;
import com.daddylab.msaiagent.service.NoteService;
import com.daddylab.msaiagent.service.PromptTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.baomidou.mybatisplus.extension.toolkit.Db.saveOrUpdate;

@Slf4j
@Service
public class NoteServiceImpl implements NoteService {
    @Autowired
    private NoteDao noteDao;
    @Autowired
    private NoteTagAgent noteTagAgent;
    @Autowired
    private PromptTemplateService promptTemplateService;

    @Override
    public Boolean save(Note noteModel) {
        return noteDao.saveOrUpdate(noteModel);
    }

    @Override
    public String buildNoteTag(Note note) {
        // 1. 获取提示词模版
        PromptTemplate promptTemplate = promptTemplateService.getByType(PromptTypeEnum.NOTE_TAG);
        Assert.notNull(promptTemplate, "笔记标签模版不能为空");
        // 2. 获取提示词内容
        String title = note.getTitle();
        String content = note.getContent();
        String prompt = "笔记标题:"+title+"\n笔记内容:"+content;
        // 3. 构建标签
        String tag  = noteTagAgent.buildTag(AIModelEnum.AZURE_OPENAI,promptTemplate.getContent(), prompt);
        note.setTag(tag);
        note.setTagPromptId(promptTemplate.getId());
        note.setTagPromptContent(promptTemplate.getContent());
        save(note);
        return tag;
    }
}
