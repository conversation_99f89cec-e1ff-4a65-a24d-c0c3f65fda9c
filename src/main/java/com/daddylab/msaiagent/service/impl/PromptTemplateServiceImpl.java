package com.daddylab.msaiagent.service.impl;

import com.daddylab.msaiagent.db.aiAgent.dao.PromptTemplateDao;
import com.daddylab.msaiagent.db.aiAgent.entity.PromptTemplate;
import com.daddylab.msaiagent.db.aiAgent.enums.PromptTypeEnum;
import com.daddylab.msaiagent.service.PromptTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * @className PromptTemplateServiceImpl
 * <AUTHOR>
 * @date 2025/5/9 10:59
 * @description: TODO 
 */
@Service
public class PromptTemplateServiceImpl implements PromptTemplateService {

    @Autowired
    private PromptTemplateDao promptTemplateDao;

    @Override
    public PromptTemplate getByType(PromptTypeEnum typeEnum) {
        return promptTemplateDao.lambdaQuery().eq(PromptTemplate::getType, typeEnum).one();
    }
}
