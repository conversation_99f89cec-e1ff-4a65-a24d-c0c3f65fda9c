package com.daddylab.msaiagent.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.daddylab.msaiagent.config.LlmConfig;
import com.daddylab.msaiagent.db.aiAgent.dao.ModelChatDao;
import com.daddylab.msaiagent.db.aiAgent.entity.ModelChat;
import com.daddylab.msaiagent.domain.enums.OpenRouterModelEnum;
import com.daddylab.msaiagent.service.ModelChatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.*;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;

import java.io.IOException;
import java.util.Map;

@Service
@Slf4j
public class ModelChatServiceImpl implements ModelChatService {

    private ConcurrentHashMap<String,OpenAiChatModel> chatModels;

    @Autowired
    LlmConfig llmConfig;

    @Autowired
    private ModelChatDao modelChatDao;

    @Override
    public ModelChat createChat(String modelName, String chatType) {
        ModelChat chat = new ModelChat();
        chat.setChatId(RandomUtil.randomStringUpper(4)+"-"+RandomUtil.randomNumbers(6));
        chat.setModelName(modelName);
        chat.setChatType(chatType);
        chat.setStatus(1);
        chat.setContent("");
        modelChatDao.save(chat);
        return chat;
    }

    public List<Message> getChatMessages(ModelChat chat){
        String content = chat.getContent();
        List<Message> messages = new ArrayList<>();
        
        if(null != content && !content.isEmpty()){
            try {
                // 使用自定义逻辑处理Message接口的反序列化
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode jsonArray = objectMapper.readTree(content);
                
                if (jsonArray.isArray()) {
                    for (JsonNode node : jsonArray) {
                        String type = node.has("type") ? node.get("type").asText() : "";
                        if(type == null || type.isEmpty()){
                            type = node.has("messageType") ? node.get("messageType").asText() : "";
                            type = type.toLowerCase();
                        }
                        String text = node.has("text") ? node.get("text").asText() : "";
                        // 根据类型创建具体的Message实现 fixme 还有很多参数没有兼容 比如 metadata media
                        if (MessageType.USER.getValue().equals(type)) {
                            messages.add(new UserMessage(text));
                        } else if (MessageType.ASSISTANT.getValue().equals(type)) {
                            messages.add(new AssistantMessage(text));
                        }else if (MessageType.SYSTEM.getValue().equals(type)) {
                            messages.add(new SystemMessage(text));
                        }
                        // 可以根据需要添加更多消息类型的处理
                    }
                }
            } catch (IOException e) {
                log.error("解析聊天记录异常", e);
            }
        }
        
        return messages;
    }

    @Override
    public void startChatAsync(ModelChat chat, String promptStr, Consumer<String> callback) {
        OpenAiChatModel model = getChatModel(chat.getModelName());
        if(null == model){
            chat.setErrorContent("模型配置不存在");
            modelChatDao.updateById(chat);
            return;
        }
        List<Message> messages = getChatMessages(chat);
        if(null == messages){
            chat.setErrorContent("解析历史聊天信息异常");
            modelChatDao.updateById(chat);
            return;
        }
        Thread.startVirtualThread(()->{
            UserMessage userMessage = new UserMessage(promptStr);
            messages.add(userMessage);
            Prompt prompt = new Prompt(messages);
            try {
                ChatResponse response = model.call(prompt);
                AssistantMessage message = response.getResult().getOutput();
                messages.add(message);
                // 修改序列化逻辑，确保包含类型信息
                chat.setContent(serializeMessages(messages));
                modelChatDao.updateById(chat);
                callback.accept(message.getText());
            }catch (Exception e){
                chat.setErrorContent("发起聊天异常："+e.getMessage());
                modelChatDao.updateById(chat);
                log.error(e.getMessage(),e);
            }
        });
    }
    
    // 添加一个辅助方法，用于序列化消息列表
    private String serializeMessages(List<Message> messages) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            List<Object> serializedMessages = new ArrayList<>();
            
            for (Message message : messages) {
                if (message instanceof UserMessage) {
                    serializedMessages.add(Map.of(
                        "type", "user",
                        "text", message.getText()
                    ));
                } else if (message instanceof AssistantMessage) {
                    serializedMessages.add(Map.of(
                        "type", "assistant",
                        "text", message.getText()
                    ));
                }
                // 可以添加更多消息类型的处理
            }
            
            return objectMapper.writeValueAsString(serializedMessages);
        } catch (Exception e) {
            log.error("序列化消息列表异常", e);
            return "[]";
        }
    }

    public OpenAiChatModel getChatModel(String modelName) {
        OpenRouterModelEnum modelEnum = OpenRouterModelEnum.getEnumByName(modelName);
        if(null == modelEnum){
            return null;
        }
        if (chatModels == null) {
            chatModels = new ConcurrentHashMap<>();
        }
        if(null == llmConfig.getConfigs() || null == llmConfig.getConfigs().get(modelEnum.getKey())) {
            log.error("Model configuration missing or model not found: {}", modelEnum.getKey());
            return null;
        }
        LlmConfig.LLMConfigDto config = llmConfig.getConfigs().get(modelEnum.getKey());
        OpenAiApi openAiConfig = OpenAiApi.builder().apiKey(config.getApiKey()).baseUrl(config.getBaseUrl()).completionsPath(config.getCompletionsPath())
                .build();
        return chatModels.computeIfAbsent(modelEnum.getName(), k -> OpenAiChatModel.builder().defaultOptions(OpenAiChatOptions.builder().model(modelName).temperature(1.0).topP(0.95).build()).openAiApi(openAiConfig).build());
    }

    @Override
    public ModelChat getChat(String modelName, String chatType) {
       return modelChatDao.lambdaQuery().eq(ModelChat::getModelName, modelName).eq(ModelChat::getChatType, chatType).one();
    }

    @Override
    public ModelChat getChat(Long id) {
        return modelChatDao.getById(id);
    }
}
