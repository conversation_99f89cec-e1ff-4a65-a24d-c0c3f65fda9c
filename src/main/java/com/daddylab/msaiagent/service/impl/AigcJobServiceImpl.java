package com.daddylab.msaiagent.service.impl;

import com.daddylab.msaiagent.common.utils.JsonUtil;
import com.daddylab.msaiagent.db.aiAgent.dao.AigcJobDao;
import com.daddylab.msaiagent.db.aiAgent.entity.AigcJob;
import com.daddylab.msaiagent.db.aiAgent.entity.ModelChat;
import com.daddylab.msaiagent.service.AigcJobService;
import com.daddylab.msaiagent.service.ModelChatService;
import com.daddylab.msaiagent.util.JsonExtractor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Consumer;

@Service
@Slf4j
public class AigcJobServiceImpl implements AigcJobService {

    @Autowired
    AigcJobDao jobDao;

    @Autowired
    private ModelChatService modelChatService;

    @Override
    public AigcJob createJob(Long bizId, String jobType, Long chatId, Object jobData) {
        AigcJob job = new AigcJob();
        job.setBizId(bizId);
        job.setJobType(jobType);
        job.setStatus(0);
        job.setJobData(JsonUtil.toJSONString(jobData));
        job.setModelChatId(chatId);
        if (chatId == null || chatId <= 0) {
            ModelChat chat = modelChatService.createChat("google/gemini-2.5-pro-preview", jobType + "_" + bizId);
            job.setModelChatId(chat.getId());
        }
        jobDao.save(job);
        return job;
    }

    @Override
    public AigcJob getJob(Long jobId) {
        return jobDao.getById(jobId);
    }

    @Override
    public void startJobAsync(AigcJob job, String promptStr, Consumer<String> callback) {
        job.setStatus(1);
        jobDao.lambdaUpdate().eq(AigcJob::getId, job.getId())
                .set(AigcJob::getStatus, 1)
                .update();
        ModelChat chat = modelChatService.getChat(job.getModelChatId());
        if (chat == null) {
            jobError(job.getId(), "未找到对应的对话模型");
            return;
        }
        modelChatService.startChatAsync(chat, promptStr, (content) -> {
            if (content == null || content.isEmpty()) {
                jobError(job.getId(), "AI返回内容为空");
                return;
            }
            // 解析AI返回的内容
            try {
                jobDao.lambdaUpdate().eq(AigcJob::getId, job.getId())
                        .set(AigcJob::getStatus, 2)
                        .update();
                String jsonInfo = JsonExtractor.extractJson(content);
                if (jsonInfo.isEmpty()) {
                    log.error("ai返回的不是有效的json数据");
                    jobError(job.getId(), "ai返回的不是有效的json数据");
                    return;
                }
                callback.accept(jsonInfo);
                jobFinish(job.getId());
            } catch (Exception e) {
                jobError(job.getId(), "解析AI返回内容失败: " + e.getMessage());
            }
        });
    }

    @Override
    public void jobError(Long jobId, String error) {
        jobDao.lambdaUpdate().eq(AigcJob::getId, jobId)
                .set(AigcJob::getStatus, -1)
                .set(AigcJob::getErrorMessage, error)
                .update();
    }

    private void jobFinish(Long jobId) {
        jobDao.lambdaUpdate().eq(AigcJob::getId, jobId).eq(AigcJob::getStatus, 2)
                .set(AigcJob::getStatus, 3)
                .update();
    }

    @Override
    public void jobUpdateAiInfo(Long jobId, List<String> clarificationQuestions, List<String> updateNotes, Object otherData) {
        jobDao.lambdaUpdate().eq(AigcJob::getId, jobId)
                .set(AigcJob::getAiQuestions, JsonUtil.toJSONString(clarificationQuestions))
                .set(AigcJob::getUpdateNotes, JsonUtil.toJSONString(updateNotes))
                .set(AigcJob::getExtraData, JsonUtil.toJSONString(otherData))
                .update();
    }

    @Override
    public AigcJob getFirstJob(Long bizId, String jobType) {
        return null;
    }
}
