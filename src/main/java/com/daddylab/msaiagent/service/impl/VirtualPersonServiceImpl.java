package com.daddylab.msaiagent.service.impl;
import com.daddylab.msaiagent.db.aiAgent.enums.BooleanEnum;

import cn.hutool.core.util.StrUtil;
import com.daddylab.msaiagent.agent.VirtualPersonAgent;
import com.daddylab.msaiagent.db.aiAgent.dao.VirtualPersonDao;
import com.daddylab.msaiagent.db.aiAgent.entity.Prompt;
import com.daddylab.msaiagent.db.aiAgent.entity.PromptTemplate;
import com.daddylab.msaiagent.db.aiAgent.entity.RedBook;
import com.daddylab.msaiagent.db.aiAgent.entity.VirtualPerson;
import com.daddylab.msaiagent.db.aiAgent.enums.PromptTypeEnum;
import com.daddylab.msaiagent.domain.enums.AIModelEnum;
import com.daddylab.msaiagent.service.PromptService;
import com.daddylab.msaiagent.service.PromptTemplateService;
import com.daddylab.msaiagent.service.RedBookService;
import com.daddylab.msaiagent.service.VirtualPersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

/**
 *
 * @className VirtualPersonServiceImpl
 * <AUTHOR>
 * @date 2025/5/9 10:39
 * @description: TODO 
 */
@Service
public class VirtualPersonServiceImpl implements VirtualPersonService {

    @Autowired
    private VirtualPersonDao virtualPersonDao;
    @Autowired
    private RedBookService redBookService;
    @Autowired
    private PromptTemplateService promptTemplateService;
    @Autowired
    private VirtualPersonAgent virtualPersonAgent;
    @Autowired
    private PromptService promptService;


    @Override
    public VirtualPerson buildVirtualPerson(String desc) {

        return null;
    }

    @Override
    public VirtualPerson imitationBuildVirtualPerson(String prompt) {
        PromptTemplate promptTemplate = promptTemplateService.getByType(PromptTypeEnum.VIRTUAL_PERSON);
        Assert.notNull(promptTemplate, "虚拟人模版不能为空");
        Prompt promptModel = new Prompt();
        promptModel.setPromptTemplateId(promptTemplate.getId());
        promptModel.setTitle("系统构建虚拟人");
        promptModel.setType(PromptTypeEnum.VIRTUAL_PERSON);
        promptModel.setActive(BooleanEnum.TRUE);
        promptModel.setContent(prompt);
        promptModel.setRemark("");
        promptService.save(promptModel);
        VirtualPerson virtualPerson = virtualPersonAgent.buildPerson(AIModelEnum.OPENAI, promptTemplate.getContent(), prompt);
        virtualPerson.setModel(AIModelEnum.OPENAI.name());
        virtualPerson.setPromptId(promptModel.getId());
        virtualPerson.setPromptContent(promptModel.getContent());
        virtualPerson.setId(null);
        virtualPersonDao.save(virtualPerson);
        return virtualPerson;
    }
}
