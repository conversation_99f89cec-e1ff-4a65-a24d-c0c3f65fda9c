package com.daddylab.msaiagent.mcp;

//import org.springframework.ai.mcp.client.McpSyncClient;
//import org.springframework.ai.mcp.spring.McpFunctionCallback;
import org.springframework.ai.model.function.FunctionCallback;

import java.util.List;
import java.util.stream.Collectors;

public class McpTools {

   //TODO 引用有问题，先注释掉
    /**
    public static List<FunctionCallback> listToolCallbacks(McpSyncClient client) {
        return client.listTools().tools().stream()
                .map(tool -> new McpFunctionCallback(client, tool))
                .collect(Collectors.toList());
    }**/
}
