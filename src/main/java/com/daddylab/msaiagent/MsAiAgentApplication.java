package com.daddylab.msaiagent;

import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

@EnableFeignClients(basePackages = "com.daddylab.msaiagent.common.feign")
@SpringBootApplication
@Slf4j
public class MsAiAgentApplication {

  public static void main(String[] args) {
    SpringApplication.run(MsAiAgentApplication.class, args);
    log.info(
        "------------- MsAiAgentApplication successfully,active:{} ------------",
        SpringUtil.getActiveProfile());
  }
}
