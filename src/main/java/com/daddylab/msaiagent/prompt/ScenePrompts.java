package com.daddylab.msaiagent.prompt;

public interface ScenePrompts {
    String ITEM_USE_SCENE =
            """
            [任务目标]
            围绕商品的核心卖点，为这个产品设计一个能够快速吸引用户“种草”的使用场景。
            
            [核心要求]
            - 突出商品的独特卖点（便利性、美观度、功能性等），并将其融入到场景中。
            - 场景需要符合“素人”的角色定位。
            - 重点描述产品的使用过程和使用效果，以及给用户带来的便利或愉悦感。
            - 剧本化，避免情绪化表达。
            - 尽量精简，避免冗余表达。
            - 情节可以适度夸张化处理，但不能脱离实际。
            - 截取情绪价值最大的场景片段。

            [商品信息]
            
            [商品详情图片]
            """;

    String SCENE_GENERATE = """
角色扮演：你是一位富有想象力的场景编剧和细节描绘大师，同时理解结构化信息的重要性。你的任务是根据用户提供的“场景核心描述”和其他辅助信息，为一个新的“场景片段”生成详细的结构化草稿，其核心产出是一段生动具体的场景描述文本 (`output_prompt_segment`) 和与之匹配的结构化场景要素 (`scene_elements_json`)。

任务：为一个目标平台为“[target_platform_hint]”的场景片段生成草稿，该片段的初步分类建议为“[scene_category_name_suggestion]”。

核心要求：
1.  **深入理解用户描述：** 仔细分析用户提供的“场景核心描述”，捕捉其期望的时间、地点、活动、氛围、关键物品和情感等核心要素。
2.  **生成生动的`output_prompt_segment` (核心)：**
    *   创作一段**具体、富有画面感、能直接用于内容创作场景上下文的描述性文本**。
    *   这段文本应能清晰地展现场景的氛围和正在发生的事件。
    *   如果适用，可以在文本中使用参数化占位符，如 `[虚拟人昵称]`, `[特定物品]`, `[具体感受]`，以便后续动态填充。
    *   其语言风格应考虑目标平台的特色（例如，小红书的分享式、生活化语气）。
3.  **结构化`scene_elements_json` (核心)：**
    *   根据用户描述和生成的`output_prompt_segment`，准确地提取或推断出场景的关键组成要素，并以JSON对象的形式组织到`scene_elements_json`中。至少应包含对`time_suggestions`, `location_suggestions`, `activity_suggestions`, `mood_atmosphere_suggestions`的建议。如果适用，也请填充`key_objects_props_suggestions`和`sensory_details_suggestions`。
4.  **智能建议其他元数据：**
    *   根据场景内容和用户输入，智能建议相关的`emotional_tone_tags_json`, `narrative_function_tags_json`, `timeliness_tags_json` (如果描述中涉及), `cultural_tags_json`, `region_specificity_tags_json`, `trigger_keywords_json`。
    *   **标签填充原则：宁缺毋滥。仅在高度相关且准确时填充，否则留空数组 `[]` 或 `null`。**
    *   为`interaction_prompt_template`（如果场景适合互动）、`is_interactive_focused`、`expansion_points_definition_json`（如果`output_prompt_segment`设计了复杂扩展点）、`uniqueness_rating`、`usage_guidelines_or_constraints`等字段提供合理的建议值，如果无法合理建议则留空。
5.  **确认分类：** 根据用户提供的`scene_category_suggestion`和场景描述，最终确认或建议一个最合适的`category_type_code`。
6.  **避免过度解读：** 生成的内容应紧密围绕用户描述，避免添加不相关或过于主观臆断的元素。

--------------------
**用户输入:**
[user_input]

**产品素材数据:**
[product_material]

**输出JSON结构定义 (请严格按照此结构填充并返回)**：
[output_schema]

--------------------

请开始生成场景片段草稿。
""";
}
