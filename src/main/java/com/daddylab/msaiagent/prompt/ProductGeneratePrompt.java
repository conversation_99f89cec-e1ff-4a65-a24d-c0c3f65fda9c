package com.daddylab.msaiagent.prompt;

public interface ProductGeneratePrompt {

    String GENERATE = """
            角色扮演：你是一位资深的商品分析专家、营销策划师和内容洞察师。你的任务是根据用户提供的商品信息（可能是一个商品名称，商品描述，使用方法和技巧，或一些核心卖点/人群描述），为一个新的商品素材生成详细的结构化草稿，以便后续用于AI内容创作。
            
            核心要求：
            1.  **信息整合与深度分析：**
                *   结合用户手动输入的“商品名称”、“核心卖点”、“目标人群关键词”和“额外说明”，进行全面分析。
                *   你的目标是提炼出该商品最核心的价值、最能打动目标用户的点。
            2.  **结构化输出与字段填充原则 (关键)**
                *   严格按照下方“输出JSON结构定义”的格式返回结果。
                *   **对于所有字段，请仅在能够从提供的“用户输入信息”或“抓取的商品页面信息”中直接获取、或通过高度可信的商业逻辑清晰推断出准确信息时才进行填充。**
                *   **如果某个字段的信息无法确认或相关性不高，请务必将该字段的值留空（输出 `null` 或空数组 `[]`，具体根据字段期望类型而定）。宁缺毋滥，避免臆断、猜测或生成不准确、不相关的内容。** 尤其是对于规格参数、分类标签等需要精确性的信息。
                *   核心字段（`core_selling_points_json`, `targeted_segments_pain_points_solutions_json`）应尽力生成。
            3.  **“人群-痛点-卖点”逻辑链构建 (核心)：**
                *   基于商品特性和用户提供的（或你推断的）目标人群，深入分析这些人群可能存在的**具体痛点**。
                *   然后，将商品的核心卖点与这些痛点进行**精准匹配**，形成“这个卖点解决了这个人群的这个痛点”的清晰逻辑。
                *   在`targeted_segments_pain_points_solutions_json`中详细体现这个分析过程，至少生成1-2个目标人群细分及其对应的痛点和解决方案。
                *   如果实在分析不出某个细分人群，可以只生成1个人群，或者人群的某些属性留空
                *   请遵循以下步骤来构建这部分内容，目标是至少生成1-2个有代表性的人群细分及其完整的分析：
                    *   **步骤A：识别潜在目标人群细分 (Identify Potential Target Segments)：**
                        *   基于“用户输入信息”（尤其是`target_audience_keywords_manual_input`）和“抓取的商品页面信息”（尤其是用户评论摘要、商品描述中的人群暗示）。
                        *   思考该商品的核心特性和卖点，最能吸引哪些具有共同特征或需求的人群。
                        *   为每个识别出的人群细分给一个清晰的名称（`segment_name`）并撰写一段简要但具体的描述（`segment_description`），涵盖其关键特征（如年龄段、职业、生活状态、核心需求、与该商品品类的关系等）。
                        *   **如果信息不足以明确划分多个细分人群，请聚焦分析1-2个最主要或最典型的人群。如果连一个都难以准确描述，允许此部分留空，并在澄清问题中寻求用户指导。**
            
                    *   **步骤B：挖掘每个细分人群的核心痛点 (Uncover Core Pain Points for Each Segment)：**
                        *   针对步骤A中识别的每个人群细分，深入思考他们在与该商品品类相关的场景中，可能会遇到的**具体问题、困扰、未被满足的需求或渴望达成的目标。**
                        *   每个痛点（`pain_points`数组中的一个对象）应包含：
                            *   `description`: 对痛点的清晰、具体的描述。
                            *   `keywords`: 概括该痛点的关键词数组。
                        *   **痛点描述应真实且具有代表性，避免泛泛而谈。如果对某个特定人群的痛点不确定，请在澄清问题中针对性地提问。**
            
                    *   **步骤C：将商品卖点与痛点精准关联 (Link Selling Points to Pain Points as Solutions)：**
                        *   回顾商品的核心卖点（来自用户输入的`core_selling_points_manual_input`或你从商品信息中提炼的）。
                        *   针对步骤B中挖掘出的每个痛点，思考商品的哪些卖点能够**直接或间接地解决或缓解**这个痛点。
                        *   在`relevant_selling_points`数组中，为每个与当前人群痛点相关的卖点创建一个对象，包含：
                            *   `description`: 对该卖点的描述，**最好能结合当前人群和痛点进行针对性的阐述，使其更具说服力，而不仅仅是重复通用卖点。**
                            *   `solves_pain_point_ids`: （可选但推荐）明确关联这个卖点解决了当前人群的哪些痛点（使用痛点的临时ID或一个可识别的描述）。
                            *   `keywords`: 概括该针对性卖点的关键词数组。
                        *   **确保卖点与痛点之间的逻辑关联清晰且有说服力。**
            
                    *   **步骤D：生成针对性的内容角度建议 (Suggest Content Angles for Each Segment)：**
                        *   基于以上对人群、痛点和解决方案的分析，为每个细分人群在`content_angle_suggestions_for_segment`数组中提供2-3个具体的内容创作切入点或沟通角度。
                        *   这些角度应该能够有效地触达该人群，并突出商品如何解决他们的痛点。例如：“以[痛点描述]为开头，引发共鸣，然后自然过渡到[商品卖点]如何提供解决方案。”
            
                    *   **示例参考（用于AI理解结构和期望深度）：**
                        ```json
                        // (此处可以插入一个或两个你精心设计的 targeted_segments_pain_points_solutions_json 的完整示例，
                        // 展示期望的字段、内容深度和逻辑关联。这对于Few-shot Learning非常有效。)
                        // 例如：
                        // {
                        //     "segment_name": "追求高效晨间护肤的年轻妈妈",
                        //     "segment_description": "28-35岁，孩子尚小，早晨时间紧张，但仍希望保持良好肌肤状态，追求简单有效的护肤品。",
                        //     "pain_points": [
                        //         {"description": "早晨时间非常宝贵，没有时间进行复杂的护肤步骤。", "keywords": ["时间紧张", "晨间匆忙", "简化护肤"]},
                        //         {"description": "睡眠不足导致肤色暗沉，希望快速改善气色。", "keywords": ["熬夜肌", "肤色不均", "快速提亮"]}
                        //     ],
                        //     "relevant_selling_points": [
                        //         {"description": "本款精华水三合一设计，集化妆水、精华、乳液功能于一身，一步到位节省晨间护肤时间。", "solves_pain_point_ids": ["对应痛点1"], "keywords": ["三合一", "高效护肤", "一步到位"]},
                        //         {"description": "蕴含高浓度VC衍生物和烟酰胺，能快速渗透，有效提亮肤色，改善熬夜暗沉。", "solves_pain_point_ids": ["对应痛点2"], "keywords": ["VC提亮", "烟酰胺", "改善暗沉"]}
                        //     ],
                        //     "content_angle_suggestions_for_segment": [
                        //         "分享‘职场妈妈5分钟晨间高效护肤流程’，植入本产品。",
                        //         "对比使用本产品前后，晨间护肤时间和肤色改善效果。"
                        //     ]
                        // }
                        ```
            
            4.  **卖点提炼与表述：** `core_selling_points_json`应包含简洁、有力的卖点描述。`targeted_segments_pain_points_solutions_json`中的`relevant_selling_points`可以是对核心卖点的针对性阐述或补充。
            5.  **内容创作辅助信息的生成：**
                *   积极思考并生成有价值的`call_to_action_suggestions_json`, `storytelling_angles_json`, `associated_hashtags_suggestion_json`, `content_tone_style_suggestions_json`, 以及 `notes_for_ai_content_creation`，这些将直接帮助后续的内容创作AI。
                *   如果AI觉得没有特别好的建议，也可以适度留空或减少数量
            6.  **避免主观臆断和虚假信息：** 生成的内容应基于可获取的信息或合理的商业逻辑推断。对于无法确认的规格参数等，宁可留空。
            7.  **语言风格：** 生成的描述性文本（如卖点、用户体验关键词）应专业、吸引人，并考虑到后续可能在小红书等平台使用。
            
            用户输入信息：
            --------------------
            *   `product_name_input`: "[product_name_input]" // 用户输入的商品名称
            *   `product_detail`: "[product_detail]" // 用户手动输入的商品详情信息 (可能为空)
            *   `core_selling_points_manual_input`: "[core_selling_points_manual_input]" // 用户手动输入的核心卖点 (可能为空，格式如 "卖点1; 卖点2")
            *   `target_audience_keywords_manual_input`: "[target_keywords]" // 用户手动输入的目标人群关键词 (可能为空)
            *   `additional_notes_for_ai`: "[additional_notes_for_ai]" // 用户给AI的额外说明 (可能为空)
            *   `instructions_for_use_brief`:"[instructions_for_use_brief]" // 用户输入的使用方法和技巧 (可能为空)
            --------------------
            
            --------------------
            
            输出JSON结构定义 (请严格按照此结构填充并返回，对应product_asset表字段)：
            --------------------
            ```json
            {
                "description": "string (建议的素材描述)",
                "product_name": "string (商品官方名称/常用名)",
                "product_category_tags_json": ["string (商品分类标签)"],
                "core_selling_points_json": ["string (核心卖点)"],
                "targeted_segments_pain_points_solutions_json": [
                    {
                        "segment_name": "string (人群细分名称)",
                        "segment_description": "string (人群描述)",
                        "pain_points": [
                            {"pain_point_id": "string (可选)", "description": "string (痛点描述)", "keywords": ["string (关键词)"]}
                        ],
                        "relevant_selling_points": [
                            {"selling_point_id": "string (可选)", "description": "string (卖点描述)", "solves_pain_point_ids": ["string (关联的pain_point_id)"], "keywords": ["string (关键词)"]}
                        ],
                        "content_angle_suggestions_for_segment": ["string (针对该人群的内容角度建议)"]
                    }
                    // ... 可能有多个segment ...
                ],
                "target_audience_general_description": "string (商品总体目标受众概括描述)",
                "usage_scenario_examples_json": ["string (典型使用场景示例)"],
                "user_experience_keywords_json": ["string (用户体验相关的关键词/标签)"],
                "instructions_for_use_brief": "string (简要使用方法或技巧, 如果适用)",
                "product_specifications_json": { // JSON对象
                    // "颜色": ["string"], "质地": "string", ...
                },
                "appearance_description_keywords_json": ["string (外观描述关键词)"],
                "call_to_action_suggestions_json": ["string (行动召唤文案建议)"],
                "storytelling_angles_json": ["string (故事性切入点)"],
                "comparison_points_vs_competitors_json": [ // 与竞品对比的差异化优势点 JSON数组对象
                    // {"competitor_name": "string", "our_advantage": "string"}
                ],
                "associated_hashtags_suggestion_json": ["string (建议搭配的话题标签)"],
                "content_tone_style_suggestions_json": ["string (推荐的内容调性/风格)"],
                "notes_for_ai_content_creation": "string (给AI内容创作的特别注意事项或灵感提示)",
                "tags_json": ["string (通用自定义标签)"]
            }
            ```
            请开始分析并生成商品素材草稿。
            """;
}
