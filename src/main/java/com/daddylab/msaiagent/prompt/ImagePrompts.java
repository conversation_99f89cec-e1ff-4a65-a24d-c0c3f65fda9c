package com.daddylab.msaiagent.prompt;

public interface ImagePrompts {
    String Extracting_Information =
"""
## Task: Extracting Information from Images Using a VLM

You are tasked with extracting as much information as possible from an image using a Vision-Language Model (VLM). Please use the following prompt to guide the VLM:

---

**Prompt for VLM:**

Analyze the provided image and extract all identifiable information. If the image contains any text, extract all original text exactly as it appears, without any modifications. Present all extracted information in a structured JSON format. Include details such as objects, scenes, activities, relationships, and any other relevant elements present in the image.

**Prompt for Detailed Image Analysis:**

Analyze the provided image in detail and extract as much information as possible. Structure your response to cover the following aspects:

1.  **Overall Scene Description:**
    * Provide a concise summary of what the image depicts.
    * Describe the main subject(s) and their central activity or state.
    * What is the overall mood or atmosphere of the image (e.g., joyful, somber, busy, tranquil)?

2.  **Objects and Entities:**
    * Identify all significant objects, animals, and people visible in the image.
    * For each, provide a brief description, including attributes like color, size (relative or estimated), shape, texture, and material if discernible.
    * Note any branding, logos, or specific model/type information visible on objects.

3.  **Setting and Environment:**
    * Describe the location and environment (e.g., indoor/outdoor, urban/rural, specific room type, natural landscape).
    * Detail the background and foreground elements.
    * What are the lighting conditions (e.g., bright daylight, dim, artificial lighting, shadows)?
    * What is the apparent time of day and weather conditions, if applicable?

4.  **Text and Symbols:**
    * Identify and transcribe any text visible in the image (e.g., signs, labels, papers, screens).
    * Describe any symbols, icons, or non-textual markings and their potential meaning within the context of the image.

5.  **Composition and Perspective:**
    * Describe the camera angle and perspective (e.g., eye-level, low angle, bird's-eye view).
    * How are the elements arranged in the image? Is there a clear focal point?
    * Note any interesting compositional techniques used (e.g., rule of thirds, leading lines, symmetry).

6.  **Actions and Interactions:**
    * Describe any actions being performed by people or animals.
    * How are the different elements in the image interacting with each other?
    * Is there a sense of motion or stillness?

7.  **Contextual Understanding and Inferences:**
    * What might be the purpose or context behind this image (e.g., a candid photo, a staged advertisement, a documentary shot, an artistic piece)?
    * What can be inferred about the situation, the relationships between people (if present), or the events that might have led to this scene?
    * Are there any cultural or historical elements suggested by the image?
    * What might be happening just outside the frame or what could happen next?

8.  **Details and Subtleties:**
    * Point out any small but potentially significant details that might be easily overlooked.
    * Are there any anomalies or unusual elements in the scene?

9.  **Potential Ambiguities:**
    * Identify any parts of the image or elements within it that are ambiguous or open to multiple interpretations.

10. **Key Information Summary (Optional - request if needed in a specific format):**
    * Provide a bulleted list of the 5-10 most critical pieces of information or observations from the image.
    * If applicable, attempt to categorize the image (e.g., portrait, landscape, event photography, product shot).

Please be as thorough and descriptive as possible in your response. If certain aspects are not applicable or cannot be determined, please state that.

---

**Requirements:**
- Output must be in JSON format, and must not contain any additional text or dialogue.
- If the image contains text, ensure all original text is extracted without any changes.
- Extract all possible information, including but not limited to objects, text, activities, and relationships.

---

**JSON Schema for VLM Image Analysis Output:**

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "title": "VLM Image Analysis Output",
  "type": "object",
  "properties": {
    "overall_scene_description": {
      "type": "object",
      "properties": {
        "summary": { "type": "string" },
        "main_subjects": { "type": "string" },
        "mood": { "type": "string" }
      },
      "required": ["summary", "main_subjects", "mood"]
    },
    "objects_and_entities": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "type": { "type": "string" },
          "description": { "type": "string" },
          "attributes": {
            "type": "object",
            "properties": {
              "color": { "type": "string" },
              "size": { "type": "string" },
              "shape": { "type": "string" },
              "texture": { "type": "string" },
              "material": { "type": "string" }
            },
            "additionalProperties": false
          },
          "branding_or_logo": { "type": "string" }
        },
        "required": ["type", "description"]
      }
    },
    "setting_and_environment": {
      "type": "object",
      "properties": {
        "location": { "type": "string" },
        "background_elements": { "type": "string" },
        "foreground_elements": { "type": "string" },
        "lighting_conditions": { "type": "string" },
        "time_and_weather": { "type": "string" }
      },
      "required": ["location", "background_elements", "foreground_elements", "lighting_conditions", "time_and_weather"]
    },
    "text_and_symbols": {
      "type": "object",
      "properties": {
        "text": {
          "type": "array",
          "items": { "type": "string" }
        },
        "symbols_and_icons": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "symbol": { "type": "string" },
              "meaning": { "type": "string" }
            },
            "required": ["symbol", "meaning"]
          }
        }
      },
      "required": ["text", "symbols_and_icons"]
    },
    "composition_and_perspective": {
      "type": "object",
      "properties": {
        "camera_angle": { "type": "string" },
        "element_arrangement": { "type": "string" },
        "focal_point": { "type": "string" },
        "composition_techniques": { "type": "string" }
      },
      "required": ["camera_angle", "element_arrangement", "focal_point", "composition_techniques"]
    },
    "actions_and_interactions": {
      "type": "object",
      "properties": {
        "actions": { "type": "string" },
        "interactions": { "type": "string" },
        "motion_or_stillness": { "type": "string" }
      },
      "required": ["actions", "interactions", "motion_or_stillness"]
    },
    "contextual_understanding_and_inferences": {
      "type": "object",
      "properties": {
        "purpose_or_context": { "type": "string" },
        "inferences": { "type": "string" },
        "cultural_or_historical_elements": { "type": "string" },
        "outside_frame_or_next": { "type": "string" }
      },
      "required": ["purpose_or_context", "inferences", "cultural_or_historical_elements", "outside_frame_or_next"]
    },
    "details_and_subtleties": {
      "type": "object",
      "properties": {
        "notable_details": { "type": "string" },
        "anomalies_or_unusual_elements": { "type": "string" }
      },
      "required": ["notable_details", "anomalies_or_unusual_elements"]
    },
    "potential_ambiguities": {
      "type": "string"
    },
    "key_information_summary": {
      "type": "array",
      "items": { "type": "string" }
    },
    "image_category": {
      "type": "string"
    }
  },
  "required": [
    "overall_scene_description",
    "objects_and_entities",
    "setting_and_environment",
    "text_and_symbols",
    "composition_and_perspective",
    "actions_and_interactions",
    "contextual_understanding_and_inferences",
    "details_and_subtleties",
    "potential_ambiguities"
  ]
}
```

""";

    String Extracting_Information_v2 =
            """
            ## Task: Extracting Information from Images

            You are tasked with extracting as much information as possible from an image.

            ---

            **Instructions:**

            Analyze the provided image in detail and extract as much information as possible. Structure your response to cover the following aspects:

            1.  **Overall Scene Description:**
                * Provide a concise summary of what the image depicts.
                * Describe the main subject(s) and their central activity or state.
                * What is the overall mood or atmosphere of the image (e.g., joyful, somber, busy, tranquil)?

            2.  **Objects and Entities:**
                * Identify all significant objects, animals, and people visible in the image.
                * For each, provide a brief description, including attributes like color, size (relative or estimated), shape, texture, and material if discernible.
                * Note any branding, logos, or specific model/type information visible on objects.

            3.  **Setting and Environment:**
                * Describe the location and environment (e.g., indoor/outdoor, urban/rural, specific room type, natural landscape).
                * Detail the background and foreground elements.
                * What are the lighting conditions (e.g., bright daylight, dim, artificial lighting, shadows)?
                * What is the apparent time of day and weather conditions, if applicable?

            4.  **Text and Symbols:**
                * Identify and transcribe any text visible in the image (e.g., signs, labels, papers, screens).
                * Describe any symbols, icons, or non-textual markings and their potential meaning within the context of the image.

            5.  **Composition and Perspective:**
                * Describe the camera angle and perspective (e.g., eye-level, low angle, bird's-eye view).
                * How are the elements arranged in the image? Is there a clear focal point?
                * Note any interesting compositional techniques used (e.g., rule of thirds, leading lines, symmetry).

            6.  **Actions and Interactions:**
                * Describe any actions being performed by people or animals.
                * How are the different elements in the image interacting with each other?
                * Is there a sense of motion or stillness?

            7.  **Contextual Understanding and Inferences:**
                * What might be the purpose or context behind this image (e.g., a candid photo, a staged advertisement, a documentary shot, an artistic piece)?
                * What can be inferred about the situation, the relationships between people (if present), or the events that might have led to this scene?
                * Are there any cultural or historical elements suggested by the image?
                * What might be happening just outside the frame or what could happen next?

            8.  **Details and Subtleties:**
                * Point out any small but potentially significant details that might be easily overlooked.
                * Are there any anomalies or unusual elements in the scene?

            9.  **Potential Ambiguities:**
                * Identify any parts of the image or elements within it that are ambiguous or open to multiple interpretations.

            10. **Key Information Summary:**
                * Provide a bulleted list of the 5-10 most critical pieces of information or observations from the image.
                * attempt to categorize the image (e.g., portrait, landscape, event photography, product shot).

            Please be as thorough and descriptive as possible in your response. If certain aspects are not applicable or cannot be determined, please state that.

            **Strict Constraints:**
            - If the image contains text, ensure all original text is extracted without any changes.
            - Extract all possible information, including but not limited to objects, text, activities, and relationships.

            """;

}
