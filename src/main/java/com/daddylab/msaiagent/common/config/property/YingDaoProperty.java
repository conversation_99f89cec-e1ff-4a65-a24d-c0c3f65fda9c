package com.daddylab.msaiagent.common.config.property;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "yingdao")
public class YingDaoProperty {

    private String accessKeyId;

    private String accessKeySecret;


    /**
     * 小红书账号列表任务
     */
    private ScheduleRobotConfig xhsAccountList;

    @Data
    public static class ScheduleRobotConfig {
        private String scheduleId;
        private String robotUuId;
    }
}
