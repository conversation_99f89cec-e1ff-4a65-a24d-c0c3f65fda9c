package com.daddylab.msaiagent.common.config.property;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@ConfigurationProperties(prefix = "daddylab.job")
public class JobProperty {
    private String adminAddress;
    private String accessToken;
    private String appName;
    private String address;
    private String ip;
    private Integer port;
    private String logPath;
    private Integer logRetentionDays;
}
