package com.daddylab.msaiagent.common.config;

import com.daddylab.job.core.executor.impl.XxlJobSpringExecutor;
import com.daddylab.msaiagent.common.config.property.JobProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.Assert;

@Configuration
@EnableConfigurationProperties(JobProperty.class)
@ConditionalOnClass(XxlJobSpringExecutor.class)
@ConditionalOnProperty(prefix = "daddylab.job", name = {"adminAddress", "port"})
public class JobAutoConfig {

  @Bean
  public XxlJobSpringExecutor xxlJobExecutor(JobProperty jobAutoConfig) {
    Assert.notNull(jobAutoConfig.getPort(), "job executor port不得为空");
    XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
    xxlJobSpringExecutor.setAdminAddresses(jobAutoConfig.getAdminAddress());
    xxlJobSpringExecutor.setAppname(jobAutoConfig.getAppName());
    xxlJobSpringExecutor.setAddress(jobAutoConfig.getAddress());
    xxlJobSpringExecutor.setIp(jobAutoConfig.getIp());
    xxlJobSpringExecutor.setPort(jobAutoConfig.getPort());
    xxlJobSpringExecutor.setAccessToken(jobAutoConfig.getAccessToken());
    xxlJobSpringExecutor.setLogPath(jobAutoConfig.getLogPath());
    xxlJobSpringExecutor.setLogRetentionDays(jobAutoConfig.getLogRetentionDays());
    return xxlJobSpringExecutor;
  }
}
