package com.daddylab.msaiagent.common.crawler;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.msaiagent.common.utils.JsonUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import kong.unirest.GetRequest;
import kong.unirest.Unirest;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 *
 * @className XhsCrawler
 * <AUTHOR>
 * @date 2025/4/28 17:11
 * @description: TODO 
 */
public class XhsCrawler {

    public static final String WINDOW_INITIAL_STATE = "window.__INITIAL_STATE__=";

    public static final String XHS_COOKIE_KEY = "xhs:account:cookie";

    public static final String SEARCH_URL = "https://edith.xiaohongshu.com/api/sns/web/v1/search/notes";
    public static final String EXPLORE_URL = "https://www.xiaohongshu.com/explore";

    public static RestTemplate xhsRestTemplate() {
        return xhsRestTemplate(StrUtil.EMPTY);
    }

    public static RestTemplate xhsRestTemplate(String cookie) {
        return new RestTemplateBuilder()
                .interceptors(
                        (request, body, execution) -> {
                            final HttpHeaders headers = request.getHeaders();
                            headers.add(
                                    "User-Agent",
                                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36");
                            headers.add("Referer", "https://www.xiaohongshu.com/");
                            if (StrUtil.isNotEmpty(cookie)) {
                                headers.add("Cookie", cookie);
                            }
                            return execution.execute(request, body);
                        })
                .build();
    }
    public static XhsDetailDTO doParserDetail(String url) {
        return doParserDetail(url, StrUtil.EMPTY);
    }


    public static String doParserUnirest(String url, String cookie) {
        GetRequest request = Unirest.get(url)
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36")
                .header("Referer", "https://www.xiaohongshu.com/");
        if  (StrUtil.isNotEmpty(cookie)) {
            request.header("Cookie", cookie);
        }
        return request.asString().getBody();
    }

    public static String doParserRestTemplate(String url, String cookie) {
        RestTemplate restTemplate = xhsRestTemplate(cookie);
        ResponseEntity<String> exploreViewResponse =
                restTemplate.getForEntity(url, String.class);
        return exploreViewResponse.getBody();
    }

    public static XhsDetailDTO doParserDetail(String url, String cookie) {
        String exploreViewHtml = doParserUnirest(url, cookie);
        Objects.requireNonNull(exploreViewHtml, "获取页面失败");
        final Document exploreViewDocument = Jsoup.parse(exploreViewHtml);
        final Elements scripts = exploreViewDocument.select("script");
        String content = null;
        for (Element script : scripts) {
            final String scriptText = script.data();
            if (scriptText.startsWith(WINDOW_INITIAL_STATE)) {
                 content = scriptText.substring(WINDOW_INITIAL_STATE.length()).replaceAll("undefined", "null");
                 continue;
            }
        }
        if (!checkJsonIsValid(content)) {
            return null;
        }
        XhsDetailDTO xhsDetailDTO = new XhsDetailDTO();
        xhsDetailDTO.setJson(content);
        return xhsDetailDTO;
    }

    private static Boolean checkJsonIsValid(String json) {
        JsonNode viewStateJSON = JsonUtil.parseObject(json);
        String state = viewStateJSON.at("/note/serverRequestInfo/state").asText();
        if ("fail".equals(state)) {
            throw new RuntimeException("抓取失败");
        }
        String noteId = viewStateJSON.at("/note/currentNoteId").asText();
        return StrUtil.isNotEmpty(noteId);
    }


    /**
     * 搜索接口
     *
     * @param searchRequestBodyDTO com.daddylab.msaiagent.common.crawler.XhsCrawler.SearchRequestBodyDTO
     * @return com.daddylab.msaiagent.common.crawler.XhsCrawler.SearchResultDTO
     * <AUTHOR>
     * @date 2025/5/26 11:10
     */
    public static SearchResultDTO searchKeyword(SearchRequestBodyDTO searchRequestBodyDTO, String cookie) {
        RestTemplate restTemplate = xhsRestTemplate(cookie);
        return restTemplate.postForObject(SEARCH_URL, searchRequestBodyDTO, SearchResultDTO.class);
    }


    @Data
    public static class XhsDetailDTO {
        private String json;

        public XhsDetailDTO ofContent(String content) {
            XhsDetailDTO xhsDetailDTO = new XhsDetailDTO();
            xhsDetailDTO.setJson(content);
            return xhsDetailDTO;
        }
    }


    @NoArgsConstructor
    @Data
    public static class SearchRequestBodyDTO {

        @JsonProperty("keyword")
        private String keyword;
        @JsonProperty("page")
        private Integer page;
        @JsonProperty("page_size")
        private Integer pageSize;
        @JsonProperty("search_id")
        private String searchId;
        @JsonProperty("sort")
        private String sort = "general";
        @JsonProperty("note_type")
        private Integer noteType;
        @JsonProperty("ext_flags")
        private List<?> extFlags;
        @JsonProperty("filters")
        private List<Filters> filters;
        @JsonProperty("geo")
        private String geo;
        @JsonProperty("image_formats")
        private List<String> imageFormats;

        @NoArgsConstructor
        @Data
        public static class Filters {
            @JsonProperty("tags")
            private List<String> tags;
            @JsonProperty("type")
            private String type;
            public static Filters of(List<String> tags, String type) {
                Filters filters = new Filters();
                filters.setTags(tags);
                filters.setType(type);
                return filters;
            }
        }

      public static SearchRequestBodyDTO ofMaxUpvote(Integer pageIndex, Integer pageSize, String keyword) {
          SearchRequestBodyDTO searchRequestBodyDTO = new SearchRequestBodyDTO();
          searchRequestBodyDTO.setKeyword(keyword);
          searchRequestBodyDTO.setPage(pageIndex);
          searchRequestBodyDTO.setPageSize(pageSize);
          searchRequestBodyDTO.setSearchId("2eumbyqx2yqt3yph5at1b");
          searchRequestBodyDTO.setSort("general");
          searchRequestBodyDTO.setNoteType(0);
          searchRequestBodyDTO.setExtFlags(Lists.newArrayList());

          ArrayList<Filters> filters = Lists.newArrayList();
          filters.add(Filters.of(Lists.newArrayList("popularity_descending"), "sort_type"));
          filters.add(Filters.of(Lists.newArrayList("不限"), "filter_note_type"));
          filters.add(Filters.of(Lists.newArrayList("不限"), "filter_note_time"));
          filters.add(Filters.of(Lists.newArrayList("不限"), "filter_note_range"));
          filters.add(Filters.of(Lists.newArrayList("不限"), "filter_pos_distance"));
          searchRequestBodyDTO.setFilters(filters);
          searchRequestBodyDTO.setGeo("");
          searchRequestBodyDTO.setImageFormats(ListUtil.of("jpg", "webp", "avif"));
          return searchRequestBodyDTO;
      }


    }

    @NoArgsConstructor
    @Data
    public static class SearchResultDTO {

        @JsonProperty("code")
        private Integer code;
        @JsonProperty("success")
        private Boolean success;
        @JsonProperty("msg")
        private String msg;
        @JsonProperty("data")
        private Data data;

        @NoArgsConstructor
        @lombok.Data
        public static class Data {
            @JsonProperty("has_more")
            private Boolean hasMore;
            @JsonProperty("items")
            private List<Items> items;

            @NoArgsConstructor
            @lombok.Data
            public static class Items {
                @JsonProperty("id")
                private String id;
                @JsonProperty("model_type")
                private String modelType;
                @JsonProperty("note_card")
                private NoteCard noteCard;
                @JsonProperty("xsec_token")
                private String xsecToken;
                @JsonProperty("rec_query")
                private RecQuery recQuery;

                @NoArgsConstructor
                @lombok.Data
                public static class NoteCard {
                    @JsonProperty("type")
                    private String type;
                    @JsonProperty("display_title")
                    private String displayTitle;
                    @JsonProperty("user")
                    private User user;
                    @JsonProperty("interact_info")
                    private InteractInfo interactInfo;
                    @JsonProperty("cover")
                    private Cover cover;
                    @JsonProperty("image_list")
                    private List<ImageList> imageList;
                    @JsonProperty("corner_tag_info")
                    private List<CornerTagInfo> cornerTagInfo;

                    @NoArgsConstructor
                    @lombok.Data
                    public static class User {
                        @JsonProperty("nick_name")
                        private String nickName;
                        @JsonProperty("avatar")
                        private String avatar;
                        @JsonProperty("user_id")
                        private String userId;
                        @JsonProperty("nickname")
                        private String nickname;
                        @JsonProperty("xsec_token")
                        private String xsecToken;
                    }

                    @NoArgsConstructor
                    @lombok.Data
                    public static class InteractInfo {
                        @JsonProperty("liked_count")
                        private String likedCount;
                        @JsonProperty("collected")
                        private Boolean collected;
                        @JsonProperty("collected_count")
                        private String collectedCount;
                        @JsonProperty("comment_count")
                        private String commentCount;
                        @JsonProperty("shared_count")
                        private String sharedCount;
                        @JsonProperty("liked")
                        private Boolean liked;
                    }

                    @NoArgsConstructor
                    @lombok.Data
                    public static class Cover {
                        @JsonProperty("height")
                        private Integer height;
                        @JsonProperty("width")
                        private Integer width;
                        @JsonProperty("url_default")
                        private String urlDefault;
                        @JsonProperty("url_pre")
                        private String urlPre;
                    }

                    @NoArgsConstructor
                    @lombok.Data
                    public static class ImageList {
                        @JsonProperty("height")
                        private Integer height;
                        @JsonProperty("width")
                        private Integer width;
                        @JsonProperty("info_list")
                        private List<InfoList> infoList;

                        @NoArgsConstructor
                        @lombok.Data
                        public static class InfoList {
                            @JsonProperty("image_scene")
                            private String imageScene;
                            @JsonProperty("url")
                            private String url;
                        }
                    }

                    @NoArgsConstructor
                    @lombok.Data
                    public static class CornerTagInfo {
                        @JsonProperty("type")
                        private String type;
                        @JsonProperty("text")
                        private String text;
                    }
                }

                @NoArgsConstructor
                @lombok.Data
                public static class RecQuery {
                    @JsonProperty("title")
                    private String title;
                    @JsonProperty("source")
                    private Integer source;
                    @JsonProperty("word_request_id")
                    private String wordRequestId;
                    @JsonProperty("queries")
                    private List<Queries> queries;

                    @NoArgsConstructor
                    @lombok.Data
                    public static class Queries {
                        @JsonProperty("id")
                        private String id;
                        @JsonProperty("name")
                        private String name;
                        @JsonProperty("search_word")
                        private String searchWord;
                    }
                }
            }
        }
    }



}
