package com.daddylab.msaiagent.common.crawler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.emoji.EmojiUtil;
import com.daddylab.msaiagent.common.utils.DownloadUtils;
import com.daddylab.msaiagent.common.utils.JsonUtil;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.*;

@Slf4j
@Component
public class XhsLocalFileCrawler {

  public static final String WINDOW_INITIAL_STATE = "window.__INITIAL_STATE__=";
  public static final String FILE_BASE_PATH = "/Users/<USER>/Desktop/小红书笔记/";


  /**
   * 抓取某个账号的笔记
   *
   * @param accountUrl java.lang.String 账号首页地址
   * @return java.util.List<com.daddylab.tech.admin.service.script.redbook.service.XhsLocalFileCrawler.AccountUrlDTO>
   * <AUTHOR>
   * @date 2025/4/16 10:10
   */
  public List<AccountUrlDTO> doCrawlerAccount(String accountUrl) {
    XhsCrawler.XhsDetailDTO xhsDetailDTO = XhsCrawler.doParserDetail(accountUrl);
    if (xhsDetailDTO == null) {
      return Collections.emptyList();
    }
    JsonNode jsonNode = JsonUtil.parseObject(xhsDetailDTO.getJson());
    if (jsonNode == null) {
      return Collections.emptyList();
    }
    JsonNode baseInfo = jsonNode.at("/user/userPageData/basicInfo");
    String accountId = baseInfo.get("redId").asText();
    String accountName = baseInfo.get("nickname").asText();
    JsonNode notes = jsonNode.at("/user/notes");
    if (notes != null) {
      JsonNode jsonNode1 = notes.get(0);
    }
    return null;
  }

  public void doWork(String url) {
    try {
      final RestTemplate restTemplate = XhsCrawler.xhsRestTemplate();
      final ResponseEntity<String> exploreViewResponse =
          restTemplate.getForEntity(url, String.class);
      final String exploreViewHtml = exploreViewResponse.getBody();
      Objects.requireNonNull(exploreViewHtml, "获取页面失败");

      final Document exploreViewDocument = Jsoup.parse(exploreViewHtml);
      final Elements scripts = exploreViewDocument.select("script");
      for (Element script : scripts) {
        final String scriptText = script.data();
        if (scriptText.startsWith(WINDOW_INITIAL_STATE)) {
          String jsonStr =
              scriptText.substring(WINDOW_INITIAL_STATE.length()).replaceAll("undefined", "null");
          final JsonNode viewStateJSON = JsonUtil.parseObject(jsonStr);
          final String state = viewStateJSON.at("/note/serverRequestInfo/state").asText();
          if ("fail".equals(state)) {
             throw new RuntimeException("抓取失败");
          }
          final String noteId = viewStateJSON.at("/note/currentNoteId").asText();
          final JsonNode item = viewStateJSON.at("/note/noteDetailMap/" + noteId);
          final JsonNode note = item.at("/note");
          // video:视频, normal:图文
          final String type = note.at("/type").asText();
          if ("video".equals(type)) {
            parseVideo(url, restTemplate, noteId, note);
            return;
          }
          if ("normal".equals(type)) {
            parseNormal(url, restTemplate, noteId, note);
            return;
          }
        }
      }
      throw new RuntimeException("未解析到有效内容");
    } catch (Exception e) {
      log.error("[内容中心][小红书]小红书数据抓取异常", e);
      throw new RuntimeException("未解析到有效内容");
    }
  }

  private void parseNormal(
      String uri, RestTemplate restTemplate, String noteId, JsonNode note) throws IOException {
    final String title = note.at("/title").asText();
    final String desc = note.at("/desc").asText();
    final Long time = note.at("/time").asLong();
    final String nickname = note.at("/user/nickname").asText();
    final JsonNode imageList = note.at("/imageList");
    String path = FILE_BASE_PATH + EmojiUtil.removeAllEmojis(title);
    if (!FileUtil.exist(path)) {
      FileUtil.mkdir(path);
    }

    String markDown = toMarkDown(RedBookMarkdownTemplateDTO.of(title, desc, DateUtil.format(new Date(time), DatePattern.NORM_DATETIME_FORMATTER)));
    FileUtil.writeUtf8String(markDown, path + "/" + noteId + ".md");

    int i = 0;
    for (JsonNode imageItem : imageList) {
      i++;
      final String imageUrl = imageItem.at("/urlDefault").asText();
      String imageFormat = FileNameUtil.getSuffix(URLUtil.getPath(imageUrl));
      if (StrUtil.isEmpty(imageFormat)) {
        imageFormat = "jpg";
      }
      final String saveImagePath = path + "/" + noteId + "_" + i + "." + imageFormat;
      DownloadUtils.download(restTemplate, imageUrl, saveImagePath);
    }
  }

  private void parseVideo(String uri, RestTemplate restTemplate, String noteId, JsonNode note)
      throws IOException {
    final String title = note.at("/title").asText();
    final String desc = note.at("/desc").asText();
    final Long time = note.at("/time").asLong();
    final String nickname = note.at("/user/nickname").asText();
    String videoUrl = note.at("/video/media/stream/h264/0/masterUrl").asText();
    String videoFormat = note.at("/video/media/stream/h264/0/format").asText();
    if (StrUtil.isEmpty(videoUrl)) {
      videoUrl = note.at("/video/media/stream/h265/0/masterUrl").asText();
      videoFormat = note.at("/video/media/stream/h265/0/format").asText();
    }

    String path = FILE_BASE_PATH + EmojiUtil.removeAllEmojis(title);
    if (!FileUtil.exist(path)) {
      FileUtil.mkdir(path);
    }
    final String saveVideoPath = path + "/" + noteId + "." + videoFormat;
    DownloadUtils.download(restTemplate, videoUrl, saveVideoPath);
    String markDown = toMarkDown(RedBookMarkdownTemplateDTO.of(title, desc, DateUtil.format(new Date(time), DatePattern.NORM_DATETIME_FORMATTER)));
    FileUtil.writeUtf8String(markDown, path + "/" + noteId + ".md");
  }

  static String MARK_DOWN_TEMPLATE = "## **标题**\n" +
          "\n" +
          "{title}\n" +
          "\n" +
          "## 发布时间\n" +
          "\n" +
          "> ### {publishTime}\n" +
          "\n" +
          "## 内容\n" +
          "\n" +
          "> {content}";

  @AllArgsConstructor(staticName = "of")
  @Data
  public static class RedBookMarkdownTemplateDTO {
    private String title;
    private String content;
    private String publishTime;
  }


  public String toMarkDown(RedBookMarkdownTemplateDTO redBookMarkdownTemplateDTO) {
    Map<String, Object> params = BeanUtil.beanToMap(redBookMarkdownTemplateDTO);
    return StrUtil.format(MARK_DOWN_TEMPLATE, params);
  }


  @Data
  public static class AccountUrlDTO {
    private String accountId;
    private String accountName;
    private String url;
    private String title;
    private Integer star;
  }
}
