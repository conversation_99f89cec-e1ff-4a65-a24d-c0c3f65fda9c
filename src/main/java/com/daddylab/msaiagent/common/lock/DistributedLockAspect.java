package com.daddylab.msaiagent.common.lock;


import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Class  DistributeLockAspect
 *
 * @Date 2021/8/8上午10:04
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class DistributedLockAspect {

    @Autowired
    private RedissonClient redissonClient;

    @Around("@annotation(com.daddylab.tech.common.lock.DistributedLock)")
    public Object addLock(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) proceedingJoinPoint.getSignature();
        Method method = methodSignature.getMethod();
        DistributedLock distributedLock = method.getAnnotation(DistributedLock.class);

        StringBuilder lockKey = buildLockKey(proceedingJoinPoint, method, distributedLock);

        log.debug("DistributedLockKey={}", lockKey);
        RLock rLock = redissonClient.getLock(lockKey.toString());

        try {
            boolean lockAcquire;
            long waitTime = distributedLock.waitTime();
            long leaseTime = distributedLock.leaseTime();
            if (waitTime == 0L) {
                lockAcquire = rLock.tryLock();
            } else {
                lockAcquire = rLock.tryLock(waitTime, leaseTime, TimeUnit.SECONDS);
            }

            if (!lockAcquire) {
                if (distributedLock.nullValueOnLockFail()) {
                    return null;
                }
                throw new LockFailException(distributedLock.msg());
            }
            return proceedingJoinPoint.proceed();
        } catch (InterruptedException e) {

            if (distributedLock.nullValueOnLockFail()) {
                return null;
            }
            throw new LockFailException(distributedLock.msg());
        } finally {

            if (rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
    }

    @NonNull
    private StringBuilder buildLockKey(ProceedingJoinPoint proceedingJoinPoint, Method method, DistributedLock distributedLock) throws IllegalAccessException {
        String key = StringUtils.isNotBlank(distributedLock.value())
                ? distributedLock.value()
                : method.getName();

        final DistributedLock.SearchKeyStrategy searchKeyStrategy = distributedLock.searchKey();
        final boolean isSearchParam = searchKeyStrategy == DistributedLock.SearchKeyStrategy.PARAMETER;
        final boolean isSearchParameterProp = searchKeyStrategy == DistributedLock.SearchKeyStrategy.PARAMETER_PROPERTY;
        final boolean isSearchMultiParameterProp = searchKeyStrategy == DistributedLock.SearchKeyStrategy.MULTI_PARAMETER_PROPERTY;

        StringBuilder lockKey = new StringBuilder(getKey(key));
        if (searchKeyStrategy != DistributedLock.SearchKeyStrategy.NONE) {
            Parameter[] methodParameters = method.getParameters();
            Object[] args = proceedingJoinPoint.getArgs();

            if (ArrayUtils.isNotEmpty(methodParameters)) {
                for (int i = 0; i < methodParameters.length; i++) {
                    DistributedLockKey distributedLockKey = methodParameters[i].getAnnotation(DistributedLockKey.class);
                    if (Objects.nonNull(distributedLockKey)) {
                        lockKey.append(":").append(args[i]);

                        if (isSearchParam) {
                            break;
                        }
                    } else {
                        if (isSearchParameterProp
                                || isSearchMultiParameterProp) {

                            for (Field declaredField : args[i].getClass().getDeclaredFields()) {
                                DistributedLockKey fieldAnnotation = declaredField.getAnnotation(DistributedLockKey.class);
                                if (Objects.nonNull(fieldAnnotation)) {
                                    if (!declaredField.isAccessible())
                                        declaredField.setAccessible(true);
                                    final Object value = declaredField.get(args[i]);
                                    if (value == null) {
                                        continue;
                                    }
                                    lockKey.append(":").append(value);

                                    if (isSearchParameterProp) {
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return lockKey;
    }

    private String getKey(String key) {
        return "lock:" + key;
    }

}
