package com.daddylab.msaiagent.common.rocketmq;

/**
 * <AUTHOR>
 * @className RocketMQConstants
 * @date 2024/9/5 10:14
 * @description: TODO
 */
public interface RocketMQTopicConstants {
    /**
     * 重试型延迟任务
     */
    String JAVA_AI_AGENT_DELAY_TIMER_COMMON_RETRY_TOPIC = "${spring.profiles.active}_${spring.application.name}_delayTimerCommonRetryTopic";


    String JAVA_AI_AGENT_RETRY_DELAY_TIMER_HANDLER_TOPIC = "${spring.profiles.active}_${spring.application.name}_retryDelayTimerHandlerTopic";



    /**
     * 老爸标准文件解析
     */
    String DADDY_STANDARD_FILE_PARSER_TOPIC = "${spring.profiles.active}_${spring.application.name}_daddyStandardFileParserV3Topic";

    /**
     * 标准同步到es
     */
    String STANDARD_SYNC_ES_TOPIC = "${spring.profiles.active}_${spring.application.name}_standardSyncESTopic";

    /**
     * 全库标准文件解析
     */
    String STANDARD_FILE_PARSER_TOPIC = "${spring.profiles.active}_${spring.application.name}_standardFileParserV3Topic";

    /**
     * 标准动态数据变更
     */
    String STANDARD_DYNAMIC_DATA_CHANGE_TOPIC = "${spring.profiles.active}_${spring.application.name}_standardDynamicDataChangeTopic";

    /**
     * 搜索内容日志处理
     */
    String SEARCH_CONTENT_LOG_TOPIC = "${spring.profiles.active}_${spring.application.name}_searchContentLogTopic";
    /**
     * 搜索记录处理
     */
    String SEARCH_LOG_DEAL_TOPIC = "${spring.profiles.active}_${spring.application.name}_searchLogDealTopic";

    /**
     * 标准变更
     */
    String STANDARD_UPDATE_TOPIC = "${spring.profiles.active}_${spring.application.name}_standardUpdateTopic";
    /**
     * 百炼文件处理
     */
    String BAI_LIAN_FILE_TOPIC = "${spring.profiles.active}_${spring.application.name}_baiLianFileTopic";

    /**
     * 百炼文件处理
     */
    String BAI_LIAN_FILE_STATUS_DELETE_TOPIC = "${spring.profiles.active}_${spring.application.name}_baiLianFileStatusDeleteTopic";

    /**
     * 百炼文件切片处理
     */
    String BAI_LIAN_FILE_INDEX_SLICE_TOPIC = "${spring.profiles.active}_${spring.application.name}_baiLianFileIndexSliceTopic";

    /**
     * 老爸标准解析处理
     */
    String DADDY_STANDARD_TREE_TOPIC = "${spring.profiles.active}_${spring.application.name}_daddyStandardTreeTopic";
    /**
     * 百炼业务实体变更
     */
    String BAI_LIAN_ENTITY_CHANGE_TOPIC = "${spring.profiles.active}_${spring.application.name}_baiLianEntityChangeTopic";

    String BAI_LIAN_CHAT_SUMMARIZE_TOPIC = "${spring.profiles.active}_${spring.application.name}_baiLianChatSummarizeTopic";

}
