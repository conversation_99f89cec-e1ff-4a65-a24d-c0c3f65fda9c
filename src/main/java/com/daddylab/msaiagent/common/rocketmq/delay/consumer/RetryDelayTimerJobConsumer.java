package com.daddylab.msaiagent.common.rocketmq.delay.consumer;

import cn.hutool.core.date.DateUtil;
import com.daddylab.msaiagent.common.rocketmq.RocketMQDelayLevelEnum;
import com.daddylab.msaiagent.common.rocketmq.RocketMQTopicConstants;
import com.daddylab.msaiagent.common.rocketmq.RocketMqProducer;
import com.daddylab.msaiagent.common.rocketmq.delay.RetryDelaySupport;
import com.daddylab.msaiagent.common.rocketmq.delay.RetryDelayTimerJobDTO;
import com.daddylab.msaiagent.common.rocketmq.delay.RocketMQRetryDelayTimerProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RocketMQMessageListener(
        topic = RocketMQTopicConstants.JAVA_AI_AGENT_DELAY_TIMER_COMMON_RETRY_TOPIC,
        consumerGroup = RocketMQTopicConstants.JAVA_AI_AGENT_DELAY_TIMER_COMMON_RETRY_TOPIC
)
public class RetryDelayTimerJobConsumer implements RocketMQListener<RetryDelayTimerJobDTO> {

    @Autowired
    private RocketMqProducer rocketMQGenericProducer;
    @Autowired
    private RocketMQRetryDelayTimerProducer rocketMQRetryDelayTimerProducer;

    @Override
    public void onMessage(RetryDelayTimerJobDTO retryDelayTimerJobDto) {
        RocketMQDelayLevelEnum rocketMQDelayLevelEnum = RetryDelaySupport.calculateLevel(retryDelayTimerJobDto.getExecuteAt(), DateUtil.currentSeconds());
        if (RocketMQDelayLevelEnum.LEVEL_NONE.equals(rocketMQDelayLevelEnum)) {
            rocketMQGenericProducer.syncSend(retryDelayTimerJobDto.getPayload(), retryDelayTimerJobDto.getTargetTopic());
            return;
        }
        // 发送延时消息
        rocketMQRetryDelayTimerProducer.syncRetrySend(retryDelayTimerJobDto, rocketMQDelayLevelEnum);
    }
}
