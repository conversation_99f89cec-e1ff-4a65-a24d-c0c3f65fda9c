package com.daddylab.msaiagent.common.rocketmq.delay.consumer;

import com.daddylab.msaiagent.common.rocketmq.RocketMQTopicConstants;
import com.daddylab.msaiagent.common.rocketmq.delay.RocketMQDelayTimerMessage;
import com.daddylab.msaiagent.common.rocketmq.dispatcher.DelayTimeDispatcher;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RocketMQMessageListener(
        topic = RocketMQTopicConstants.JAVA_AI_AGENT_RETRY_DELAY_TIMER_HANDLER_TOPIC,
        consumerGroup = RocketMQTopicConstants.JAVA_AI_AGENT_RETRY_DELAY_TIMER_HANDLER_TOPIC
)
public class RetryDelayTimerHandlerConsumer implements RocketMQListener<RocketMQDelayTimerMessage> {

    @Autowired
    private DelayTimeDispatcher delayTimeDispatcher;

    @Override
    public void onMessage(RocketMQDelayTimerMessage message) {
        log.info("[延迟消息调度消息] message={}", message);
        delayTimeDispatcher.dispatcherAsync(message);
    }
}
