package com.daddylab.msaiagent.common.rocketmq;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/3/3
 */
@ConfigurationProperties(prefix = "rocketmq.extra")
@Data
@RefreshScope
public class RocketMQExtraProperties {
  private Map<String, ConsumerProperties> consumers;
  private String robotKey = "2b115d24-85c3-4e6d-a554-f58fc585d09a";
}
