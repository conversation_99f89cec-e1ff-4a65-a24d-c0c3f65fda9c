package com.daddylab.msaiagent.common.rocketmq.delay;

import com.daddylab.msaiagent.common.base.enums.DelayTimerTypeEnum;
import com.daddylab.msaiagent.common.lock.DistributedLockKey;
import lombok.Data;

import java.io.Serializable;

@Data
public class RocketMQDelayTimerMessage implements Serializable {
    private static final long serialVersionUID = -5777111035068808624L;

    public static RocketMQDelayTimerMessage of(Long expectTime, String businessName, DelayTimerTypeEnum delayTimerTypeEnum) {
        RocketMQDelayTimerMessage rocketMQDelayTimerMessage = new RocketMQDelayTimerMessage();
        rocketMQDelayTimerMessage.setExpectTime(expectTime);
        rocketMQDelayTimerMessage.setBusinessName(businessName);
        rocketMQDelayTimerMessage.setDelayTimerTypeEnum(delayTimerTypeEnum);
        rocketMQDelayTimerMessage.setAttachment("");
        return rocketMQDelayTimerMessage;
    }
    /**
     * 执行时间(毫秒)
     */
    private Long expectTime;
    /**
     * 业务名称(一般对应主键ID)
     */
    @DistributedLockKey
    private String businessName;
    /**
     * 延迟任务类型
     */
    @DistributedLockKey
    private DelayTimerTypeEnum delayTimerTypeEnum;

    /**
     * 附件 ，带带我
     */
    private String attachment;


}
