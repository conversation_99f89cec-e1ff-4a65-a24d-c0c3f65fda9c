package com.daddylab.msaiagent.common.rocketmq.delay.annotations;




import com.daddylab.msaiagent.common.base.enums.DelayTimerTypeEnum;

import java.lang.annotation.*;

/**
 * DelayTimeHandler 和 @DelayTimeMapping配合使用
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
@Inherited
public @interface DelayTimeHandler {

    /**
     * specific information enum
     *
     * @return
     */
    DelayTimerTypeEnum value();
}
