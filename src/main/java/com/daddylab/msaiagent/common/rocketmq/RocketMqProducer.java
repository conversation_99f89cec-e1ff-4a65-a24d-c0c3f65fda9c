package com.daddylab.msaiagent.common.rocketmq;

import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.GenericMessage;
import org.springframework.stereotype.Component;

/**
 *
 * @className RocketMqProducer
 * <AUTHOR>
 * @date 2024/9/5 09:53
 * @description: TODO 
 */
@Slf4j
@Component
public class RocketMqProducer {

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    int DEFAULT_SEND_MSG_TIMEOUT = 3000;

    private final SendCallback asyncHandlerCallback = new SendCallback() {
        @Override
        public void onSuccess(SendResult sendResult) {
            log.info("[消息发送成功] 消息：{}", sendResult);
        }

        @Override
        public void onException(Throwable throwable) {
            log.error("[消息发送失败] 消息：{}", throwable.getMessage());
        }
    };


    /**
     * 同步发送消息
     *
     * @param msgContent java.lang.Object
     * @param topic java.lang.String
     * <AUTHOR>
     * @date 2024/9/5 10:04
     */
    public void syncSend(Object msgContent, String topic) {
        syncSend(msgContent, topic, "*");
    }


    private void syncSend(Object msgContent, String topic, String tag) {
        Message<Object> finalMsg = new GenericMessage<>(msgContent);
        try {
            rocketMQTemplate.syncSend(getDestinationEnv(topic, tag, false), finalMsg);
        } catch (Exception e) {
            log.error("同步发送消息失败，消息，消息：{}。错误信息：{}", finalMsg.getPayload(), e.getMessage());
        }
    }


    /**
     * 同步发送消息
     *
     * @param msgContent java.lang.Object
     * @param topic java.lang.String
     * <AUTHOR>
     * @date 2024/9/5 10:04
     */
    public void syncSendOrderly(Object msgContent, String topic) {
        Message<Object> finalMsg = new GenericMessage<>(msgContent);
        try {
            rocketMQTemplate.syncSendOrderly(getDestinationEnv(topic, "*", false), finalMsg, topic);
        } catch (Exception e) {
            log.error("同步发送消息失败，消息，消息：{}。错误信息：{}", finalMsg.getPayload(), e.getMessage());
        }
    }

    /**
     * 异步发送消息
     *
     * @param msgContent java.lang.Object
     * @param topic java.lang.String
     * <AUTHOR>
     * @date 2024/9/5 10:13
     */
    public void asyncSend(Object msgContent, String topic) {
        asyncSend(msgContent, topic, "*");
    }

    private void asyncSend(Object msgContent, String topic, String tag) {
        Message<Object> finalMsg = new GenericMessage<>(msgContent);
        try {
            rocketMQTemplate.asyncSend(getDestinationEnv(topic, tag, false), finalMsg, asyncHandlerCallback);
        } catch (Exception e) {
            log.error("同步发送消息失败，消息，消息：{}。错误信息：{}", finalMsg.getPayload(), e.getMessage());
        }
    }


    public void syncSendMessageNoEnv(Object msgContent, String topic) {
        Message<Object> finalMsg = new GenericMessage<>(msgContent);
        try {
            rocketMQTemplate.syncSend(getDestinationEnv(topic, "*", false), finalMsg);
        } catch (Exception e) {
            log.error("同步发送消息失败，消息，消息：{}。错误信息：{}", finalMsg.getPayload(), e.getMessage());
        }
    }

    public void syncSendDelay(Object msgContent, final String topic, RocketMQDelayLevelEnum delayLevel) {
        syncSendDelay(msgContent, topic, "*", delayLevel);
    }

    private void syncSendDelay(final Object msgContent, final String topic, final String tag, final RocketMQDelayLevelEnum delayLevel) {
        Message<Object> finalMsg = new GenericMessage<>(msgContent);
        try {
         rocketMQTemplate.syncSend(getDestination(topic, tag), new GenericMessage<>(msgContent), DEFAULT_SEND_MSG_TIMEOUT, delayLevel.getValue());
        } catch (Exception e) {
            log.error("同步发送消息失败，消息 错误信息：{}", e.getMessage());
        }
    }

    public void syncSendDelayNoEnv(final Object msgContent, final String topic, final String tag, final RocketMQDelayLevelEnum delayLevel) {
        SendResult sendResult = null;
        String failMsg = "";
        Message<Object> finalMsg = new GenericMessage<>(msgContent);
        try {
            sendResult = rocketMQTemplate.syncSend(getDestinationEnv(topic, tag, false), new GenericMessage<>(msgContent), DEFAULT_SEND_MSG_TIMEOUT,
                    delayLevel.getValue());

        } catch (Exception var2) {
            log.error("同步发送消息失败，消息 错误信息：{}", var2.getMessage());
        }
    }

    private String getDestination(String topic, String tag) {
        return getDestinationEnv(topic, tag, true);
    }

    private String getDestinationEnv(String topic, String tag, boolean needEnv) {
        if (needEnv) {
            topic = SpringUtil.getActiveProfile() + "_" + topic;
        }
        String destination = topic;
        if (StringUtils.isNotBlank(tag)) {
            destination = topic + ":" + tag;
        }
        return destination;
    }
}
