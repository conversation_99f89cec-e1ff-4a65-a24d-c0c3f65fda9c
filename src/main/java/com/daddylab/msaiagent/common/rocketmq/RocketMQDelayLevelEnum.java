package com.daddylab.msaiagent.common.rocketmq;

import com.daddylab.msaiagent.common.base.enums.IIntegerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum RocketMQDelayLevelEnum implements IIntegerEnum {
    LEVEL_NONE(0, "立即执行", "立即执行"),
    LEVEL_01(1, "延迟1s", "延迟1s"),
    LEVEL_02(2, "延迟5s", "延迟5s"),
    LEVEL_03(3, "延迟10s", "延迟10s"),
    LEVEL_04(4, "延迟30s", "延迟30s"),
    LEVEL_05(5, "延迟1m", "延迟1m"),
    LEVEL_06(6, "延迟2m", "延迟2m"),
    LEVEL_07(7, "延迟3m", "延迟3m"),
    LEVEL_08(8, "延迟4m", "延迟4m"),
    LEVEL_09(9, "延迟5m", "延迟5m"),
    LEVEL_10(10, "延迟6m", "延迟6m"),
    LEVEL_11(11, "延迟7m", "延迟7m"),
    LEVEL_12(12, "延迟8m", "延迟8m"),
    LEVEL_13(13, "延迟9m", "延迟9m"),
    LEVEL_14(14, "延迟10m", "延迟10m"),
    LEVEL_15(15, "延迟20m", "延迟20m"),
    LEVEL_16(16, "延迟30m", "延迟30m"),
    LEVEL_17(17, "延迟1h", "延迟1h"),
    LEVEL_18(18, "延迟2h", "延迟2h"),
    ;
    private Integer value;
    private String name;
    private String desc;
}
