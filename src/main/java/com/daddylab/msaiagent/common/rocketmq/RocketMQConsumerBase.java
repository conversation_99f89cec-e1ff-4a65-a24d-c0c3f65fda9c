package com.daddylab.msaiagent.common.rocketmq;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.msaiagent.common.log.LogVar;
import com.daddylab.msaiagent.common.log.Slf4js;
import com.daddylab.msaiagent.common.utils.JacksonUtil;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.UtilAll;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.apache.rocketmq.spring.core.RocketMQPushConsumerLifecycleListener;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.event.Level;
import org.springframework.cloud.endpoint.event.RefreshEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.ResolvableType;
import org.springframework.core.env.Environment;

import java.lang.reflect.Type;
import java.time.Duration;
import java.util.Arrays;
import java.util.Optional;

/**
 * RocketMQConsumer基类，拓展特性如下：
 *
 * <p>1. 可通过 <code>rocketmq.extra</code>
 * 属性（Map类型，KEY为consumer的name，默认是类名首字母小写，可在子类覆盖name方法自定义）配置消费者的消费位点、最小最大线程数、批量拉取消息数量、停机等待、暂时挂起消费者等等。
 *
 * <p>2. 防止消息重复消费，当消息设置KEYS的时候，KEYS优先作为防重复的键，未设置则使用mq自动生成的msgId
 *
 * @param <T>
 */
@lombok.extern.slf4j.Slf4j
public abstract class RocketMQConsumerBase<T>
    implements RocketMQListener<MessageExt>,
        RocketMQPushConsumerLifecycleListener,
        ApplicationListener<RefreshEvent>,
        EnvironmentAware {

  private static final String MSG_CONSUMED_PREFIX = "msg:consumed:";

  private volatile boolean init = false;

  protected RocketMQMessageListenerAnnotationWrapper annotation;
  protected Type msgBodyType;
  protected final RedissonClient redissonClient;
  protected final RocketMQExtraProperties rocketMQExtraProperties;
  protected final ConsumerProperties consumerProperties;
  protected DefaultMQPushConsumer consumer;

  public RocketMQConsumerBase(
      RedissonClient redissonClient, RocketMQExtraProperties rocketMQExtraProperties) {
    this.redissonClient = redissonClient;
    this.rocketMQExtraProperties = rocketMQExtraProperties;
    this.consumerProperties = Optional.ofNullable(rocketMQExtraProperties.getConsumers()).map(consumers -> consumers.get(name())).orElse(null);
  }

  protected Environment environment;

  @Override
  public void setEnvironment(@NotNull Environment environment) {
    this.environment = environment;
  }

//  protected QyWeixinWebhookClient robotAPI;

  public String name() {
    return StrUtil.lowerFirst(ClassUtil.getClassName(this, true));
  }

  @Override
  public void onApplicationEvent(@NotNull RefreshEvent event) {
    if (this.consumer != null) {
      final Optional<ConsumerProperties> consumerPropertiesOptional =
          Optional.ofNullable(consumerProperties);
      final Boolean suspend =
          consumerPropertiesOptional.map(ConsumerProperties::getSuspend).orElse(false);
      if (suspend) {
        this.consumer.suspend();
      } else {
        this.consumer.resume();
      }
    }
  }

  /**
   * 需要对消费者做一些定制化操作的，在子类重写这个方法
   *
   * @param consumer DefaultMQPushConsumer
   */
  @Override
  public void prepareStart(DefaultMQPushConsumer consumer) {
    this.consumer = consumer;
    final Optional<ConsumerProperties> consumerPropertiesOptional =
        Optional.ofNullable(consumerProperties);
    consumerPropertiesOptional
        .map(ConsumerProperties::getExtraTopics)
        .filter(CollUtil::isNotEmpty)
        .ifPresent(
            topics -> {
              for (String[] topic : topics) {
                try {
                  log.info(
                      "Consumer subscribe topic, listenerBeanName:{}, topic:{}",
                      name(),
                      Arrays.toString(topic));
                  this.consumer.subscribe(topic[0], topic.length == 1 ? "*" : topic[1]);
                } catch (MQClientException e) {
                  throw new RuntimeException(e);
                }
              }
            });
    consumerPropertiesOptional
        .map(ConsumerProperties::getConsumerGroup)
        .map(environment::resolvePlaceholders)
        .ifPresent(consumer::setConsumerGroup);
    consumerPropertiesOptional
        .map(ConsumerProperties::getConsumeThreadMin)
        .ifPresent(consumer::setConsumeThreadMin);
    consumerPropertiesOptional
        .map(ConsumerProperties::getConsumeThreadMax)
        .ifPresent(consumer::setConsumeThreadMax);
    consumerPropertiesOptional
        .map(ConsumerProperties::getConsumeFromWhere)
        .ifPresent(consumer::setConsumeFromWhere);
    consumerPropertiesOptional
        .map(ConsumerProperties::getConsumeTimestampOffset)
        .map(v -> UtilAll.timeMillisToHumanString3(System.currentTimeMillis() + v))
        .ifPresent(consumer::setConsumeTimestamp);
    consumerPropertiesOptional
        .map(ConsumerProperties::getConsumeMessageBatchMaxSize)
        .ifPresent(consumer::setConsumeMessageBatchMaxSize);
    consumerPropertiesOptional
        .map(ConsumerProperties::getPullBatchSize)
        .ifPresent(consumer::setPullBatchSize);
    consumerPropertiesOptional
        .map(ConsumerProperties::getPullInterval)
        .ifPresent(consumer::setPullInterval);
    consumerPropertiesOptional
        .map(ConsumerProperties::getAwaitTerminationMillisWhenShutdown)
        .ifPresent(consumer::setAwaitTerminationMillisWhenShutdown);
    consumerPropertiesOptional
        .map(ConsumerProperties::getConsumeTimeout)
        .ifPresent(consumer::setConsumeTimeout);
    if (consumerPropertiesOptional.map(ConsumerProperties::getSuspend).orElse(false)) {
      consumer.suspend();
    }
  }

  @Override
  @SuppressWarnings("unchecked")
  public final void onMessage(MessageExt msg) {
    if (consumerProperties != null) {
      if (consumerProperties.getSuspend()) {
        log(msg, Level.INFO, "Consumer is suspended");
        return;
      }
      if (consumerProperties.getSkip()) {
        log(msg, Level.DEBUG, "Consumers are configured to skip messages");
        return;
      }
    }
    tryInit();
    final String bodyStr = msg.getBody() == null ? "" : new String(msg.getBody());
    final int bodyMaxLen = getBodyMaxLen();
    log(
        msg,
        Level.INFO,
        "message received",
        LogVar.named("body", bodyStr).json(bodyMaxLen));
    RBucket<Object> bucket = redissonClient.getBucket(MSG_CONSUMED_PREFIX + getMsgRepeatKey(msg));
    if (consumerProperties != null
        && consumerProperties.getPreventRepeatConsumeWithInSeconds() > bodyMaxLen) {
      if (bucket.isExists()) {
        log(msg, Level.WARN, "message repeat");
        return;
      }
    }

    T msgBody;
    try {
      if (msgBodyType instanceof Class && ((Class<?>) msgBodyType).isAssignableFrom(byte[].class)) {
        msgBody = (T) msg.getBody();
      } else if (msgBodyType instanceof Class
          && ((Class<?>) msgBodyType).isAssignableFrom(String.class)) {
        msgBody = (T) bodyStr;
      } else if (msgBodyType instanceof Class
          && ((Class<?>) msgBodyType).isAssignableFrom(Void.class)) {
        msgBody = null;
      } else {
        msgBody = JacksonUtil.parseObject(bodyStr, msgBodyType);
      }
    } catch (Exception e) {
      log(
          msg,
          Level.ERROR,
          "parse message body error",
          LogVar.named("body", bodyStr).json(bodyMaxLen),
          e);
      return;
    }
    try {
      handle(msg, msgBody);
      log(msg, Level.INFO, "handle success");
      if (consumerProperties != null) {
        final Integer preventRepeatConsumeWithInSeconds =
            consumerProperties.getPreventRepeatConsumeWithInSeconds();
        if (preventRepeatConsumeWithInSeconds > 0) {
          bucket
              .setIfAbsentAsync(true, Duration.ofSeconds(preventRepeatConsumeWithInSeconds))
              .exceptionally(
                  e -> {
                    log(msg, Level.ERROR, "message consumed flag error", e);
                    return false;
                  });
        }
      }
    } catch (Throwable e) {
      log(
          msg,
          Level.ERROR,
          "message handle error",
          LogVar.named("body", bodyStr).json(bodyMaxLen),
          e);
      alert(bodyStr, e);
      throw new MqMessageHandleException(e.getMessage(), msg);
    }
  }

  private void alert(String body, Throwable e) {
    final String content =
            StrUtil.subWithLength(
                    "rocketmq message handle error:"
                            + ExceptionUtil.stacktraceToOneLineString(e, 200)
                            + "\n"
                            + body,
                    0,
                    3000);
  }

  private int getBodyMaxLen() {
    final Logger logger = getLogger();
    return logger.isDebugEnabled() ? 16384 : (logger.isInfoEnabled() ? 4096 : 0);
  }

  private String getMsgRepeatKey(MessageExt msg) {
    return msg.getKeys() != null ? msg.getKeys() : msg.getMsgId();
  }

  /**
   * 子类可覆盖自定义实现日志存储
   *
   * @param msg MQ消息体
   * @param level 日志级别
   * @param logMsg 日志消息
   * @param args 参数
   */
  protected void log(MessageExt msg, Level level, String logMsg, Object... args) {
    Slf4js.log(
        getLogger(),
        level,
        "MQConsumer",
        ArrayUtil.addAll(
            new Object[] {
              LogVar.unnamed(logMsg),
              LogVar.named("topic", msg.getTopic()),
              LogVar.named("tags", msg.getTags()),
              LogVar.named("msgId", msg.getMsgId()),
              LogVar.named("msgKeys", msg.getKeys())
            },
            args));
  }

  private Logger getLogger() {
    return LoggerFactory.getLogger(this.getClass());
  }

  private void tryInit() {
    if (!init) {
      synchronized (this) {
        if (!init) {
          final Class<?> clazz = this.getClass();
          final ResolvableType resolvableType = ResolvableType.forClass(this.getClass());
          msgBodyType = resolvableType.getSuperType().getGeneric(0).getType();
          RocketMQMessageListener annotation = clazz.getAnnotation(RocketMQMessageListener.class);
          this.annotation = new RocketMQMessageListenerAnnotationWrapper(annotation);
          init = true;
        }
      }
    }
  }

  /**
   * 收到消息后的处理逻辑
   *
   * @param msg 消息
   * @param body 消息体反序列化对象
   */
  protected abstract void handle(MessageExt msg, @Nullable T body);

  class RocketMQMessageListenerAnnotationWrapper {

    RocketMQMessageListener annotation;

    public RocketMQMessageListenerAnnotationWrapper(RocketMQMessageListener annotation) {
      this.annotation = annotation;
    }

    public String getConsumerGroup() {
      return environment.resolvePlaceholders(annotation.consumerGroup());
    }
  }
}
