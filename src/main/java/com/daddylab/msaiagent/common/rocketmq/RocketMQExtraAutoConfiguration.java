package com.daddylab.msaiagent.common.rocketmq;

import org.apache.rocketmq.client.MQAdmin;
import org.apache.rocketmq.spring.autoconfigure.RocketMQTransactionConfiguration;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2023/3/6
 */
@EnableConfigurationProperties(RocketMQExtraProperties.class)
@Configuration
@ConditionalOnClass({MQAdmin.class})
@ConditionalOnProperty(prefix = "rocketmq", value = "name-server", matchIfMissing = true)
@AutoConfigureAfter({RocketMQTransactionConfiguration.class})
public class RocketMQExtraAutoConfiguration {

  @Bean
  public RocketMqTemplateAspect rocketMqTemplateAspect(
      ObjectProvider<RocketMqTemplateAspect.RocketMqTemplateAspectCustomizer> customizer) {
    final RocketMqTemplateAspect rocketMqTemplateAspect = new RocketMqTemplateAspect();
    customizer.ifAvailable(c -> c.customize(rocketMqTemplateAspect));
    return rocketMqTemplateAspect;
  }
}
