package com.daddylab.msaiagent.common.rocketmq.dispatcher;

import cn.hutool.core.util.StrUtil;
import com.daddylab.msaiagent.common.base.enums.DelayTimerTypeEnum;
import com.daddylab.msaiagent.common.rocketmq.delay.RocketMQDelayTimerMessage;
import com.daddylab.msaiagent.common.rocketmq.delay.annotations.DelayTimeHandler;
import com.daddylab.msaiagent.common.rocketmq.delay.annotations.DelayTimeMapping;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ReflectionUtils;
import org.springframework.web.method.HandlerMethod;

import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Class  DelayTimeDispatcher
 *
 * @Date 2022/8/15下午2:26
 * <AUTHOR>
 */
@Component
@Slf4j
public class DelayTimeDispatcher implements InitializingBean, ApplicationContextAware, DisposableBean {

    private final Map<DelayTimerTypeEnum, List<HandlerMethod>> HANDLER_METHOD_CONCURRENT_HASH_MAP = new ConcurrentHashMap<>();


    private ApplicationContext applicationContext;


    /**
     * 调度Task
     *
     * @param rocketMQDelayTimerMessage RocketMQDelayTimerMessage
     * @date 2024/3/21 11:13
     * <AUTHOR>
     */
    public void dispatcher(RocketMQDelayTimerMessage rocketMQDelayTimerMessage) {
        DelayTimerTypeEnum delayTimerTypeEnum = rocketMQDelayTimerMessage.getDelayTimerTypeEnum();
        List<HandlerMethod> handlerMethods = HANDLER_METHOD_CONCURRENT_HASH_MAP.get(delayTimerTypeEnum);
        if (CollectionUtils.isEmpty(handlerMethods)) {
            return;
        }
        for (HandlerMethod handlerMethod : handlerMethods) {
            invokeMethod(delayTimerTypeEnum, handlerMethod, rocketMQDelayTimerMessage);
        }
    }

    /**
     * 异步调用
     *
     * @param rocketMQDelayTimerMessage RocketMQDelayTimerMessage
     * @date 2024/3/21 11:15
     * <AUTHOR>
     */
    public void dispatcherAsync(RocketMQDelayTimerMessage rocketMQDelayTimerMessage) {
        Thread.startVirtualThread(() -> dispatcher(rocketMQDelayTimerMessage)).start();
    }

    /**
     *
     * 调度方法 ()
     *
     * @examples
     *
     *    handler 参数类型
     *
     *    eg:1
     *    @DelayTimeHandler(DelayTimerTypeEnum.TEST_1)
     *     public void test(Long businessId) {
     *         log.info("【test1】任务开始执行，id={}", id);
     *     }
     *
     *     eg:2 若businessId和executeAt 同时存在则，注意保持顺序(businessId在前，executeAt在后)
     *     @DelayTimeHandler(DelayTimerTypeEnum.TEST_2)
     *     public void test1(Long businessId, Long executeAt) {
     *         log.info("【test2】任务开始执行，id={}", id);
     *     }
     *     eg 3:
     *     @DelayTimeHandler(DelayTimerTypeEnum.TEST_2)
     *     public void test2() {
     *         log.info("无参");
     *     }
     *     eg:4
     *     @DelayTimeHandler(DelayTimerTypeEnum.TEST_3)
     *     public void test3(DelayTimerTypeEnum delayEnum, RocketMQDelayTimerMessage rocketMqDelayTimeMessage) {
     *         log.info("注入特殊参数 RocketMQDelayTimerMessage");
     *     }
     *     eg:5
     *     @DelayTimeHandler(DelayTimerTypeEnum.TEST_4)
     *     public void test4(DelayTimerTypeEnum delayEnum, RocketMQDelayTimerMessage rocketMqDelayTimeMessage, Long businessId, Long executeAt) {
     *         log.info("注入所有的参数");
     *     }
     *     eg:6
     *     @DelayTimeHandler(DelayTimerTypeEnum.TEST_5)
     *     public void test5(String businessName) {
     *         log.info("businessName为字符串的场景");
     *     }
     *
     * @param delayTimerTypeEnum
     * @param handlerMethod
     * @param rocketMQDelayTimerMessage
     */
    private void invokeMethod(DelayTimerTypeEnum delayTimerTypeEnum, HandlerMethod handlerMethod, RocketMQDelayTimerMessage rocketMQDelayTimerMessage) {
        Object bean = handlerMethod.getBean();
        Method method = handlerMethod.getMethod();
        Object[] args = new Object[method.getParameterCount()];
        Class<?>[] argTypes = method.getParameterTypes();
        Deque<Object> params = new ArrayDeque<>();
        params.add(rocketMQDelayTimerMessage.getBusinessName());
        params.add(rocketMQDelayTimerMessage.getExpectTime());
        for (int i = 0; i < args.length; i++) {
            if (argTypes[i].isAssignableFrom(DelayTimerTypeEnum.class)) {
                args[i] = delayTimerTypeEnum;
            } else if (argTypes[i].isAssignableFrom(RocketMQDelayTimerMessage.class)) {
                args[i] = rocketMQDelayTimerMessage;
            } else if (argTypes[i].isAssignableFrom(Long.class)) {
                if (!params.isEmpty()) {
                    Object e = params.pop();
                    if (e instanceof Long) {
                        args[i] = e;
                    } else if (StrUtil.isNumeric(String.valueOf(e))) {
                        args[i] = Long.parseLong(String.valueOf(e));
                    } else {
                        args[i] = 0L;
                    }
                } else {
                    if (StrUtil.isNumeric(rocketMQDelayTimerMessage.getBusinessName())) {
                        args[i] = Long.parseLong(rocketMQDelayTimerMessage.getBusinessName());
                    } else {
                        args[i] = rocketMQDelayTimerMessage.getExpectTime();
                    }
                }
            } else if (argTypes[i].isAssignableFrom(String.class)) {
                args[i] = rocketMQDelayTimerMessage.getBusinessName();
                params.pop();
            } else {
                args[i] = null;
            }
        }
        ReflectionUtils.invokeMethod(method, bean, args);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Map<String, Object> beansWithAnnotation = applicationContext.getBeansWithAnnotation(DelayTimeMapping.class);
        beansWithAnnotation.forEach((name, mappingBean) -> {
            for (Method declaredMethod : mappingBean.getClass().getDeclaredMethods()) {
                DelayTimeHandler delayTimeHandler = AnnotationUtils.findAnnotation(declaredMethod, DelayTimeHandler.class);
                if (Objects.isNull(delayTimeHandler)) {
                    continue;
                }
                HANDLER_METHOD_CONCURRENT_HASH_MAP.compute(delayTimeHandler.value(), (key, values) -> {
                    if (Objects.isNull(values)) {
                        return new CopyOnWriteArrayList<HandlerMethod>(Lists.newArrayList(new HandlerMethod(mappingBean, declaredMethod)));
                    }
                    values.add(new HandlerMethod(mappingBean, declaredMethod));
                    return values;
                });
            }
        });
    }

    /**
     * 判断事件是否存在
     *
     * @param delayTimerTypeEnum
     * @return
     */
    public Boolean isExists(DelayTimerTypeEnum delayTimerTypeEnum) {
        return HANDLER_METHOD_CONCURRENT_HASH_MAP.containsKey(delayTimerTypeEnum);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public void destroy() throws Exception {
        HANDLER_METHOD_CONCURRENT_HASH_MAP.clear();
    }
}
