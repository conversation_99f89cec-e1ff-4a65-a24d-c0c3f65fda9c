package com.daddylab.msaiagent.common.rocketmq;

import org.apache.rocketmq.common.message.MessageExt;

/**
 * <AUTHOR>
 * @since 2023/3/6
 */
public class MqMessageHandleException extends RuntimeException {
  private final MessageExt mqMessage;

  public MqMessageHandleException(String message, MessageExt mqMessage) {
    super(message);
    this.mqMessage = mqMessage;
  }

  public MessageExt getMqMessage() {
    return mqMessage;
  }
}
