package com.daddylab.msaiagent.common.rocketmq.delay;

import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.daddylab.msaiagent.common.rocketmq.RocketMQDelayLevelEnum;
import com.daddylab.msaiagent.common.rocketmq.RocketMQTopicConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RocketMQRetryDelayTimerProducer implements EnvironmentAware {
    /**
     * 发送消息默认超时时间，单位：毫秒
     */
    private static final Long DEFAULT_SEND_MSG_TIMEOUT = 3000L;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    private Environment environment;
    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }

    /**
     * 发送消息(常规消息)
     *
     * @param message RocketMQDelayTimerMessage
     * @date 2023/12/11 11:45
     * <AUTHOR>
     */
    public void syncSend(RocketMQDelayTimerMessage message) {
        Message<RetryDelayTimerJobDTO> payloadMessage = MessageBuilder.withPayload(RetryDelayTimerJobDTO.of(resolveTopic(RocketMQTopicConstants.JAVA_AI_AGENT_RETRY_DELAY_TIMER_HANDLER_TOPIC), message.getExpectTime(), message)).build();
        RocketMQDelayLevelEnum rocketMQDelayLevelEnum = RetryDelaySupport.calculateLevel(message.getExpectTime(), DateUtil.currentSeconds());
        SendResult sendResult = rocketMQTemplate.syncSend(getDestination(RocketMQTopicConstants.JAVA_AI_AGENT_DELAY_TIMER_COMMON_RETRY_TOPIC, "*"),
                payloadMessage, DEFAULT_SEND_MSG_TIMEOUT, rocketMQDelayLevelEnum.getValue());
        log.info("[延迟任务] 发送状态={}", sendResult.getSendStatus());
    }


    /**
     * 发送消息
     *
     * @param topic String
     * @param message RocketMQDelayTimerMessage
     * @date 2023/12/11 11:45
     * <AUTHOR>
     */
    public void syncSendSelfTopic(String topic, RocketMQDelayTimerMessage message) {
        Message<RetryDelayTimerJobDTO> payloadMessage = MessageBuilder.withPayload(RetryDelayTimerJobDTO.of(topic, message.getExpectTime(), message)).build();
        RocketMQDelayLevelEnum rocketMQDelayLevelEnum = RetryDelaySupport.calculateLevel(message.getExpectTime(), DateUtil.currentSeconds());
        SendResult sendResult = rocketMQTemplate.syncSend(getDestination(RocketMQTopicConstants.JAVA_AI_AGENT_DELAY_TIMER_COMMON_RETRY_TOPIC, "*"),
                payloadMessage, DEFAULT_SEND_MSG_TIMEOUT, rocketMQDelayLevelEnum.getValue());
        log.info("[延迟任务] 发送状态={}", sendResult.getSendStatus());
    }

    /**
     * 异步消息发送(常规消息)
     *
     * @param message RetryDelayTimerJobDto
     * @param topic String
     * @date 2024/3/20 16:11
     * <AUTHOR>
     */
    public void asyncSendSelfTopic(String topic, RocketMQDelayTimerMessage message) {
        Long executeAt = message.getExpectTime();
        Message<RetryDelayTimerJobDTO> payloadMessage = MessageBuilder.withPayload(RetryDelayTimerJobDTO.of(topic, executeAt, message)).build();
        SendCallback sendCallback = new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info("【延迟任务】异步发送消息成功。message={}, result={}", payloadMessage, sendResult);
            }
            @Override
            public void onException(Throwable te) {
                log.error("【延迟任务】异步发送消息异常。message={}", payloadMessage, te);
            }
        };
        RocketMQDelayLevelEnum rocketMQDelayLevelEnum = RetryDelaySupport.calculateLevel(executeAt, DateUtil.currentSeconds());
        rocketMQTemplate.asyncSend(getDestination(RocketMQTopicConstants.JAVA_AI_AGENT_DELAY_TIMER_COMMON_RETRY_TOPIC, "*"),
                payloadMessage, sendCallback, DEFAULT_SEND_MSG_TIMEOUT, rocketMQDelayLevelEnum.getValue());
    }

    /**
     * 发送重试任务
     *
     * @param message RetryDelayTimerJobDto
     * @param level RocketMQDelayLevelEnum
     * @date 2024/3/20 16:39
     * <AUTHOR>
     */
    public void syncRetrySend(RetryDelayTimerJobDTO message, RocketMQDelayLevelEnum level) {
        Message<RetryDelayTimerJobDTO> payloadMessage = MessageBuilder.withPayload(message).build();
        rocketMQTemplate.syncSend(getDestination(RocketMQTopicConstants.JAVA_AI_AGENT_DELAY_TIMER_COMMON_RETRY_TOPIC, ""),
                payloadMessage, DEFAULT_SEND_MSG_TIMEOUT, level.getValue());
    }

    protected final String getDestination(String topic, String tag) {
        return getDestinationEnv(topic, tag, false);
    }

    protected final String getDestinationEnv(String topic, String tag, boolean needEnv) {
        if (needEnv) {
            topic = SpringUtil.getActiveProfile() + "_" + topic;
        }
        String destination = topic;
        if (StringUtils.isNotBlank(tag)) {
            destination = topic + ":" + tag;
        }
        return destination;
    }

    private String resolveTopic(String topic) {
        return environment.resolvePlaceholders(topic);
    }
}
