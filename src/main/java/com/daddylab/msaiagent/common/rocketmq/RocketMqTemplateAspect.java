package com.daddylab.msaiagent.common.rocketmq;

import com.daddylab.msaiagent.common.log.LogVar;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.BiConsumer;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;

import javax.annotation.Nonnull;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

/**
 * RocketMQTemplate 方法执行切面，拦截对发送方法的调用做全局管理，增加特性如下：
 *
 * <p>1. 增加了对发送路由的处理钩子，默认实现会进行环境变量解析替换
 *
 * <p>2. 增加了方法调用成功、异常事件的钩子，默认实现是打印日志到控制台
 *
 * <AUTHOR>
 * @since 2023/3/6
 */
@Aspect
@Slf4j
public class RocketMqTemplateAspect implements EnvironmentAware {

  @Getter @Setter
  private Function<String, String> destinationHandler =
      v -> this.environment.resolvePlaceholders(v);

  @Getter @Setter
  private BiConsumer<ExecutionContext, Object> successHandler =
      (c, o) -> {
        log.info("rocketmq call success >> {} << {}", c.toString(), LogVar.unnamed(o).limit(300));
      };

  @Getter @Setter
  private BiConsumer<ExecutionContext, Throwable> exceptionHandler =
      (c, e) -> {
        log.error("rocketmq call error >> {}", c.toString(), e);
      };

  private final List<String> interceptMethods =
      Arrays.asList(
          "sendOneWayOrderly",
          "sendAndReceive",
          "syncSend",
          "sendOneWay",
          "sendMessageInTransaction",
          "syncSendOrderly",
          "asyncSend",
          "asyncSendOrderly");

  private Environment environment;

  @Override
  public void setEnvironment(@Nonnull Environment environment) {
    this.environment = environment;
  }

  @Around(value = "execution(public * org.apache.rocketmq.spring.core.RocketMQTemplate.*(..))")
  public Object around(ProceedingJoinPoint pjp) throws Throwable {
    final MethodSignature signature = (MethodSignature) pjp.getSignature();
    final String name = signature.getName();
    final Object[] args = pjp.getArgs();
    boolean isAsyncSend = false;
    final ExecutionContext executionContext = new ExecutionContext(pjp);
    if (interceptMethods.contains(name)) {
      final Class<?>[] parameterTypes = signature.getParameterTypes();
      for (int i = 0; i < parameterTypes.length; i++) {
        if (i == 0) {
          args[0] = destinationHandler.apply((String) args[0]);
        }
        if (SendCallback.class.isAssignableFrom(parameterTypes[i])) {
          isAsyncSend = true;
          args[i] =
              new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                  successHandler.accept(executionContext, sendResult);
                }

                @Override
                public void onException(Throwable e) {
                  exceptionHandler.accept(executionContext, e);
                }
              };
          break;
        }
      }
    }
    try {
      final Object result = pjp.proceed(args);
      if (!isAsyncSend) {
        successHandler.accept(executionContext, result);
      }
      return result;
    } catch (Throwable e) {
      exceptionHandler.accept(executionContext, e);
      throw e;
    }
  }

  @FunctionalInterface
  public interface RocketMqTemplateAspectCustomizer {
    void customize(RocketMqTemplateAspect aspect);
  }
}
