package com.daddylab.msaiagent.common.rocketmq.delay;



import com.daddylab.msaiagent.common.rocketmq.RocketMQDelayLevelEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @class RetryDelayUtils.java
 * @description 描述类的作用
 * @date 2024-03-20 15:32
 */
public class RetryDelaySupport {

    static int Delay_None_Second = 0;
    static int Delay_1_Second = 1;           // 1s
    static int Delay_2_Second = 5;           // 5s
    static int Delay_3_Second = 10;          // 10s
    static int Delay_4_Second = 30;          // 30s
    static int Delay_5_Second = 60;          // 1m
    static int Delay_6_Second = 60 * 2;      // 2m
    static int Delay_7_Second = 60 * 3;      // 3m
    static int Delay_8_Second = 60 * 4;      // 4m
    static int Delay_9_Second = 60 * 5;      // 5m
    static int Delay_10_Second = 60 * 6;      // 6m
    static int Delay_11_Second = 60 * 7;      // 7m
    static int Delay_12_Second = 60 * 8;      // 8m
    static int Delay_13_Second = 60 * 9;      // 9m
    static int Delay_14_Second = 60 * 10;     // 10m
    static int Delay_15_Second = 60 * 20;     // 20m
    static int Delay_16_Second = 60 * 30;     // 30m
    static int Delay_17_Second = 60 * 60;     // 1h
    static int Delay_18_Second = 60 * 60 * 2; // 2h
    private static final Map<Integer, RocketMQDelayLevelEnum>  delaySecondLevelMap = new HashMap<>();
    private static final Integer[] delaySecond = new Integer[]{
            Delay_1_Second,
            Delay_2_Second,
            Delay_3_Second,
            Delay_4_Second,
            Delay_5_Second,
            Delay_6_Second,
            Delay_7_Second,
            Delay_8_Second,
            Delay_9_Second,
            Delay_10_Second,
            Delay_11_Second,
            Delay_12_Second,
            Delay_13_Second,
            Delay_14_Second,
            Delay_15_Second,
            Delay_16_Second,
            Delay_17_Second,
            Delay_18_Second,
    };



    static {
        delaySecondLevelMap.put(Delay_1_Second, RocketMQDelayLevelEnum.LEVEL_01);
        delaySecondLevelMap.put(Delay_2_Second, RocketMQDelayLevelEnum.LEVEL_02);
        delaySecondLevelMap.put(Delay_3_Second, RocketMQDelayLevelEnum.LEVEL_03);
        delaySecondLevelMap.put(Delay_4_Second, RocketMQDelayLevelEnum.LEVEL_04);
        delaySecondLevelMap.put(Delay_5_Second, RocketMQDelayLevelEnum.LEVEL_05);
        delaySecondLevelMap.put(Delay_6_Second, RocketMQDelayLevelEnum.LEVEL_06);
        delaySecondLevelMap.put(Delay_7_Second, RocketMQDelayLevelEnum.LEVEL_07);
        delaySecondLevelMap.put(Delay_8_Second, RocketMQDelayLevelEnum.LEVEL_08);
        delaySecondLevelMap.put(Delay_9_Second, RocketMQDelayLevelEnum.LEVEL_09);
        delaySecondLevelMap.put(Delay_10_Second, RocketMQDelayLevelEnum.LEVEL_10);
        delaySecondLevelMap.put(Delay_11_Second, RocketMQDelayLevelEnum.LEVEL_11);
        delaySecondLevelMap.put(Delay_12_Second, RocketMQDelayLevelEnum.LEVEL_12);
        delaySecondLevelMap.put(Delay_13_Second, RocketMQDelayLevelEnum.LEVEL_13);
        delaySecondLevelMap.put(Delay_14_Second, RocketMQDelayLevelEnum.LEVEL_14);
        delaySecondLevelMap.put(Delay_15_Second, RocketMQDelayLevelEnum.LEVEL_15);
        delaySecondLevelMap.put(Delay_16_Second, RocketMQDelayLevelEnum.LEVEL_16);
        delaySecondLevelMap.put(Delay_17_Second, RocketMQDelayLevelEnum.LEVEL_17);
        delaySecondLevelMap.put(Delay_18_Second, RocketMQDelayLevelEnum.LEVEL_18);
    }


    /**
     * 计算
     *
     * @param executeTime long
     * @param now long
     * @return int
     * @date 2024/3/20 15:54
     * <AUTHOR>
     */
    public static RocketMQDelayLevelEnum calculateLevel(long executeTime, long now) {
        long diff = executeTime - now;
        if (diff <= 0) {
            return RocketMQDelayLevelEnum.LEVEL_NONE;
        }
        for (int i = delaySecond.length - 1; i >= 0; i--) {
            if (diff < delaySecond[i]) {
                continue;
            }
            RocketMQDelayLevelEnum rocketMQDelayLevelEnum = delaySecondLevelMap.get(delaySecond[i]);
            if (rocketMQDelayLevelEnum == null) {
                return RocketMQDelayLevelEnum.LEVEL_NONE;
            }
            return rocketMQDelayLevelEnum;
        }
        return RocketMQDelayLevelEnum.LEVEL_NONE;
    }
}
