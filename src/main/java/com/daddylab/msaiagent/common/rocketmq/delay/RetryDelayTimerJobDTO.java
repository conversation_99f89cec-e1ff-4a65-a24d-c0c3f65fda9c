package com.daddylab.msaiagent.common.rocketmq.delay;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @class RetryDelayTimerJobDTO.java
 * @description 描述类的作用
 * @date 2024-03-20 15:27
 */
@NoArgsConstructor
@AllArgsConstructor(staticName = "of")
@Data
public class RetryDelayTimerJobDTO implements Serializable {
    /**
     * 目标主题
     */
    private String targetTopic;
    /**
     * 期望执行时间(秒级)
     */
    private Long executeAt;
    /**
     * 实际body
     */
    private RocketMQDelayTimerMessage payload;
}
