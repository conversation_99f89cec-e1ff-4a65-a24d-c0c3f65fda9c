package com.daddylab.msaiagent.common.rocketmq;


import com.daddylab.msaiagent.common.log.LogVar;
import lombok.Getter;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;

import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @since 2023/3/6
 */
@Getter
public class ExecutionContext {

  private final String methodName;

  private final String methodSignature;

  private final Class<?>[] parameterTypes;

  private final String[] parameterNames;

  private final Object[] args;

  public ExecutionContext(ProceedingJoinPoint pjp) {
    final MethodSignature signature = (MethodSignature) pjp.getSignature();
    methodName = signature.getName();
    methodSignature = signature.toShortString();
    parameterNames = signature.getParameterNames();
    parameterTypes = signature.getParameterTypes();
    args = pjp.getArgs();
  }

  @Override
  public String toString() {
    StringJoiner argBuilder = new StringJoiner(", ");
    for (Object arg : args) {
      argBuilder.add(LogVar.unnamed(arg).build().toString());
    }
    return String.format("%s(%s)", methodName, argBuilder);
  }
}
