package com.daddylab.msaiagent.common.rocketmq;

import lombok.Data;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/20
 */
@Data
public class ConsumerProperties {

    /** 是否启用此消费者 */
    private Boolean suspend = false;
    /** 接受到消息后不做处理直接回传已消费 */
    private Boolean skip = false;
    /** 避免在多少秒内重复消费（为0不启用此特性） */
    private Integer preventRepeatConsumeWithInSeconds = 600;

    private List<String[]> extraTopics;
    private String consumerGroup;
    private Integer consumeThreadMin;
    private Integer consumeThreadMax;
    private ConsumeFromWhere consumeFromWhere;
    private Integer consumeTimestampOffset;
    private Long consumeTimeout;
    private Long pullInterval;
    private Integer pullBatchSize;
    private Integer consumeMessageBatchMaxSize;
    private Long awaitTerminationMillisWhenShutdown;
}
