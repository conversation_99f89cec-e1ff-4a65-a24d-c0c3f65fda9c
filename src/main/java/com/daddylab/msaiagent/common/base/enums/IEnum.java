package com.daddylab.msaiagent.common.base.enums;

import com.fasterxml.jackson.annotation.JsonValue;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

public interface IEnum<T extends Serializable>
    extends com.baomidou.mybatisplus.annotation.IEnum<T> {

  static <T extends IEnum<?>> T getEnumByValue(Class<T> enumClass, Object value) {
    return getEnumOptByValue(enumClass, value).orElse(null);
  }

  static <T extends IEnum<?>> Optional<T> getEnumOptByValue(Class<T> enumClass, Object value) {
    if (value == null) {
      return Optional.empty();
    }
    return Arrays.stream(enumClass.getEnumConstants())
        .filter(constant -> Objects.equals(constant.getValue().toString(), value.toString()))
        .findFirst();
  }

  static <T extends IEnum<?>> T getEnumByName(Class<T> enumClass, String value) {
    return getEnumOptByName(enumClass, value).orElse(null);
  }

  static <T extends IEnum<?>> Optional<T> getEnumOptByName(Class<T> enumClass, String value) {
    return Arrays.stream(enumClass.getEnumConstants())
        .filter(
            constant -> {
              if (constant instanceof Enum) {
                return Objects.equals(((Enum<?>) constant).name(), value);
              }
              return false;
            })
        .findFirst();
  }

  static <T extends IEnum<?>> T getEnumByDesc(Class<T> enumClass, String value) {
    return getEnumOptByDesc(enumClass, value).orElse(null);
  }

  static <T extends IEnum<?>> Optional<T> getEnumOptByDesc(Class<T> enumClass, String value) {
    return Arrays.stream(enumClass.getEnumConstants())
        .filter(
            constant -> {
              if (constant != null) {
                return Objects.equals(constant.getDesc(), value);
              }
              return false;
            })
        .findFirst();
  }

  /**
   * 枚举值
   *
   * @return T
   */
  @JsonValue
  T getValue();

  /**
   * 描述
   *
   * @return java.lang.String
   */
  String getDesc();
}
