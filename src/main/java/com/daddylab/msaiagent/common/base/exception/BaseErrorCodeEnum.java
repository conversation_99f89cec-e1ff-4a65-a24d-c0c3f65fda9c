package com.daddylab.msaiagent.common.base.exception;

import lombok.Getter;

@Getter
public enum BaseErrorCodeEnum implements ErrorCode {

    SUCCESS(ErrorTypeEnum.SYSTEM_ERROR,200,"成功"),
    SYSTEM_ERROR(ErrorTypeEnum.SYSTEM_ERROR,500,"系统异常"),
    BUSINESS_ERROR(ErrorTypeEnum.SYSTEM_ERROR,501,"业务异常"),
    USER_NOT_LOGIN(ErrorTypeEnum.SYSTEM_ERROR,401, "用户未登录"),
    SELLER_INFO_NOT_FOUND(ErrorTypeEnum.SYSTEM_ERROR,401, "用户未绑定商家"),
    RESOURCE_NOT_FOUND(ErrorTypeEnum.SYSTEM_ERROR,404, "资源不存在"),
    OVER_CALL_FREQUENCY(ErrorTypeEnum.SYSTEM_ERROR,429, "调用频率过高"),
    SYSTEM_BUSY(ErrorTypeEnum.SYSTEM_ERROR,600, "系统繁忙,请稍候再试"),
    ARGUMENT_NOT_VALID(ErrorTypeEnum.SYSTEM_ERROR,650001, "请求参数校验不通过"),
    UPLOAD_FILE_SIZE_LIMIT(ErrorTypeEnum.SYSTEM_ERROR,650002, "上传文件大小超过限制"),
    DUPLICATE_PRIMARY_KEY(ErrorTypeEnum.SYSTEM_ERROR,650003,"唯一键冲突"),
    REGION_LIST_ERROR(ErrorTypeEnum.SYSTEM_ERROR,650004,"区域列表获取异常"),
    IMAGE_UPLOAD_FAIL(ErrorTypeEnum.SYSTEM_ERROR,650005,"图片上传失败"),
    VIDEO_UPLOAD_FAIL(ErrorTypeEnum.SYSTEM_ERROR,650006,"视频上传失败"),
    VIDEO_UPLOAD_FIRST_IMAGE_FAIL(ErrorTypeEnum.SYSTEM_ERROR,650007,"视频首图上传失败"),
    BUSINESS_CHECK_FAILED(ErrorTypeEnum.BIZ_ERROR,650008,"业务上检查不通过"),
    BUSINESS_OPERATE_FAILED(ErrorTypeEnum.BIZ_ERROR,650009,"业务操作失败"),
    MERCHANT_MOBILE_FAILED(ErrorTypeEnum.BIZ_ERROR,650010,"手机号码错误"),
    THERE_ARE_ILLEGAL_CONTENTS(ErrorTypeEnum.BIZ_ERROR,650011,"%s存在违规内容，请修改后提交！%s"),
    EXCEL_UPLOAD_FAIL(ErrorTypeEnum.SYSTEM_ERROR,650005,"Excel上传失败"),
    FILE_UPLOAD_FAIL(ErrorTypeEnum.SYSTEM_ERROR,650006,"一般文件上传失败"),
    STRING_CONVERT_NUMBER_FAIL(ErrorTypeEnum.SYSTEM_ERROR, 650007, "参数转换错误"),
    BUSINESS_OPERATE_FAILED_PLEASE_TRY_AGAIN(ErrorTypeEnum.BIZ_ERROR,650012,"业务操作失败, 请重试"),
    ;







    ;

    private ErrorTypeEnum type;
    private int code;
    private String msg;

    BaseErrorCodeEnum(ErrorTypeEnum type, int code, String msg) {
        this.type = type;
        this.code = code;
        this.msg = msg;
    }
}
