package com.daddylab.msaiagent.common.base.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 *
 * @className FileDTO
 * <AUTHOR>
 * @date 2024/9/3 14:53
 * @description: TODO 
 */
@NoArgsConstructor
@AllArgsConstructor(staticName = "of")
@Data
@ApiModel(value = "文件信息", description = "文件信息")
public class FileDTO implements DTO {

    @NotEmpty(message = "文件名称不能为空")
    @ApiModelProperty("文件名称")
    private String filename;

    @NotEmpty(message = "文件路径不能为空")
    @ApiModelProperty("文件路径")
    private String url;
}
