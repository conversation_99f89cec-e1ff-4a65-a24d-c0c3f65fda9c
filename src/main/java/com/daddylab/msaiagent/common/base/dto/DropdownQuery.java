package com.daddylab.msaiagent.common.base.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @since 2022/3/31
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("通用下拉查询")
public class DropdownQuery extends BasePageQuery {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("名称")
    @Size(max = 50, min = 1, message = "名称长度应在[1,50]")
    private String name;


}
