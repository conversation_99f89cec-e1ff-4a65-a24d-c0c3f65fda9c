package com.daddylab.msaiagent.common.base.response;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.msaiagent.common.base.exception.AiAgentErrorCodeEnum;
import com.daddylab.msaiagent.common.base.exception.AiAgentException;
import com.daddylab.msaiagent.common.base.exception.ErrorCode;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 通用返回对象
 * @param <T>
 */
@Getter
public class Result<T> {

    public static final int SUCCESSFUL_CODE = 0;

    public static final String SUCCESSFUL_MSG = "success";

    /**
     * 状态码
     */
    private Integer code;

    private Boolean flag;

    /**
     * 描述信息
     */
    private String msg;

    /**
     * 返回数据
     */
    private T data;

    public Result() {

    }

    /**
     * @param errorType 错误类型
     */
    public Result(ErrorCode errorType) {
        this.code = errorType.getCode();
        this.msg = errorType.getMsg();
        this.flag = false;
    }

    /**
     * @param  AiAgentException
     */
    public Result(AiAgentException AiAgentException) {
        this.code = AiAgentException.getCode();
        this.msg = AiAgentException.getMsg();
        this.flag = false;
    }

    /**
     * @param errorType 错误类型
     * @param data 返回数据
     */
    public Result(ErrorCode errorType, T data) {
        this.msg = errorType.getMsg();
        this.code = errorType.getCode();
        this.data = data;
        this.flag = false;
    }

    /**
     * @param errorType 错误类型
     * @param msg 描述信息
     */
    public Result(ErrorCode errorType, String msg) {
        this.msg = errorType.getMsg();
        this.code = errorType.getCode();
        this.msg = msg;
        this.flag = false;
    }

    /**
     * 内部使用，用于构造成功的结果
     *
     * @param code 状态码
     * @param msg  描述信息
     * @param data 返回数据
     */
    private Result(int code, String msg, T data, boolean flag) {
        this.code = code;
        this.msg = msg;
        this.data = data;
        this.flag = flag;
    }
    /**
     * 内部使用，用于构造成功的结果
     *
     * @param code 状态码
     * @param msg  描述信息
     * @param data 返回数据
     */
    private Result(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
        this.flag = true;
    }

    /**
     * 成功并返回结果数据
     *
     * @param data 返回数据
     * @return Result
     */
    public static <E> Result<E> success(E data) {
        return new Result<>(SUCCESSFUL_CODE, SUCCESSFUL_MSG, data);
    }

    /**
     * 成功
     *
     * @return Result
     */
    public static <E> Result<E> success() {
        return success(null);
    }

    /**
     * 系统异常没有返回数据
     *
     * @return Result
     */
    public static <E> Result<E> fail() {
        return new Result<>(AiAgentErrorCodeEnum.SYSTEM_ERROR);
    }

    /**
     * 系统异常没有返回数据
     *
     * @param techErrorCodeEnum 系统异常
     * @return Result
     */
    public static <E> Result<E> fail(AiAgentErrorCodeEnum techErrorCodeEnum) {
        return fail((ErrorCode) techErrorCodeEnum, null);
    }

    /**
     * 系统异常
     *
     * @param techErrorCodeEnum 系统异常
     * @return Result
     */
    public static <E> Result<E> fail(AiAgentErrorCodeEnum techErrorCodeEnum, String msg) {
        return fail((ErrorCode) techErrorCodeEnum, msg);
    }

    /**
     * 系统异常类并返回结果数据
     *
     * @param data 返回数据
     * @return Result
     */
    public static <E> Result<E> fail(AiAgentErrorCodeEnum techErrorCodeEnum, E data) {
        return new Result<>(techErrorCodeEnum.getCode(), techErrorCodeEnum.getMsg(), data);
    }

    /**
     * 系统异常类并返回结果数据
     *
     * @param errorType 错误类型
     * @param data      返回数据
     * @return Result
     */
    public static <E> Result<E> fail(ErrorCode errorType, E data) {
        return new Result<>(errorType, data);
    }

    /**
     * 系统异常类并返回自定义描述信息
     *
     * @param errorType 错误类型
     * @param msg      描述信息
     * @return Result
     */
    public static <E> Result<E> fail(ErrorCode errorType, String msg) {
        return new Result<>(errorType, msg);
    }

    /**
     * 系统异常类并返回结果数据
     *
     * @param errorType 错误类型
     * @return Result
     */
    public static <E> Result<E> fail(ErrorCode errorType) {
        return new Result<>(errorType);
    }

    /**
     * 系统异常类并返回结果数据
     *
     * @param data  返回数据
     * @return Result
     */
    public static <E> Result<E> fail(E data) {
        return new Result<>(AiAgentErrorCodeEnum.SYSTEM_ERROR, data);
    }
    /**
     * 系统异常类并返回结果数据
     *
     * @param AiAgentException  返回数据
     * @return Result
     */
    public static <E> Result<E> fail(AiAgentException AiAgentException) {
        return new Result<>(AiAgentException);
    }

    public static <T> Result<IPage<T>> emptyPage(Long c, Long s) {
        IPage<T> pp = new Page<>(c, s, 0);
        pp.setRecords(new ArrayList<>());
        return Result.success(pp);
    }

    public static <T> Result<IPage<T>> resultPage(Long c, Long s, List<T> resList, Long t) {
        IPage<T> pp = new Page<>(c, s, t);
        pp.setRecords(resList);
        return Result.success(pp);
    }
}
