package com.daddylab.msaiagent.common.base.dto;

import com.fasterxml.jackson.annotation.JsonIncludeProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/9/14
 */
@Data
@JsonIncludeProperties({"timeStart", "timeEnd"})
public class TimestampRange implements DTO {
  @ApiModelProperty("开始时间")
  @NotNull
  private Long timeStart;

  @ApiModelProperty("结束时间")
  @NotNull
  private Long timeEnd;
}
