package com.daddylab.msaiagent.common.base.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * @className FileDTO
 * <AUTHOR>
 * @date 2024/9/3 14:53
 * @description: TODO 
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "文件信息", description = "文件信息")
public class FileParserDTO extends FileDTO {

    @ApiModelProperty("文件解析后内容(文本或者html)")
    private String content;
}
