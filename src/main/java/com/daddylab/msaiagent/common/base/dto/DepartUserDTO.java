package com.daddylab.msaiagent.common.base.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 *
 * @className DepartUserDTO
 * <AUTHOR>
 * @date 2024/10/15 14:29
 * @description: TODO 
 */
@Data
@ApiModel(value = "DepartUserDTO", description = "部门用户")
public class DepartUserDTO implements Serializable {
    @NotNull(message = "部门ID不能为空")
    @ApiModelProperty("部门ID")
    private Long departId;
    @NotEmpty(message = "名称不能为空")
    @ApiModelProperty("名称(部门名称或用户名称)")
    private String name;
    @ApiModelProperty("用户ID")
    private Long userId;
}
