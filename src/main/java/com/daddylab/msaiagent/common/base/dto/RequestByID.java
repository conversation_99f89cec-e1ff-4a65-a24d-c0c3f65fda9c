package com.daddylab.msaiagent.common.base.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/9/14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class RequestByID<T> extends BaseRequestDTO {
  @ApiModelProperty(value = "ID")
  @NotNull
  private T id;
}
