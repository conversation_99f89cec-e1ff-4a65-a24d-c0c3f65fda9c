package com.daddylab.msaiagent.common.oss;

import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2023/10/11
 */
@Configuration
@EnableConfigurationProperties(OssConfig.class)
public class OssAutoConfiguration {
  @Bean
  public OssGateway ossGateway(OssConfig ossConfig) {
    return new OssGatewayImpl(ossConfig);
  }
}
