package com.daddylab.msaiagent.common.oss.models;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.URLUtil;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/10/13
 */
@Builder
@Getter
public class SaveAs implements OssProcessModel {
    @Override
    public String command() {
        return "sys/saveas";
    }

    /** 目标Object名称，名称需经过URL安全的Base64编码。具体操作，请参见水印编码。 */
    String o;

    public void setO(String o) {
        this.o = URLUtil.encode(Base64.encode(o));
    }

    /** 目标Bucket名称，名称需经过URL安全的Base64编码。如果不指定目标Bucket，则默认保存至原文件所在Bucket。 */
    String b;

    public void setB(String b) {
        this.b = URLUtil.encode(Base64.encode(b));
    }
}
