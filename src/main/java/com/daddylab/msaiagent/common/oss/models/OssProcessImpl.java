package com.daddylab.msaiagent.common.oss.models;

import cn.hutool.core.bean.BeanUtil;

import java.util.Map;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @since 2023/10/13
 */
public class OssProcessImpl implements OssProcess {
    private final StringJoiner command;
    private OssProcessImpl parent;

    public OssProcess setParent(OssProcessImpl parent) {
        this.parent = parent;
        return this;
    }

    public OssProcessImpl(String command) {
        this.command = new StringJoiner(",", command + ",", "");
    }

    public OssProcessImpl(OssProcessModel model) {
        this(model.command());
        final Map<String, Object> modelParams = BeanUtil.beanToMap(model);
        for (Map.Entry<String, Object> entry : modelParams.entrySet()) {
            if (entry.getValue() == null) {
                continue;
            }
            param(entry.getKey(), entry.getValue().toString());
        }
    }

    @Override
    public String toString() {
        if (parent == null) {
            return command.toString();
        }
        return parent + "|" + command;
    }

    @Override
    public OssProcess param(String name, String value) {
        command.add(name + "_" + value);
        return this;
    }

    @Override
    public OssProcess and(String command) {
        return new OssProcessImpl(command).setParent(this);
    }

    @Override
    public OssProcess and(OssProcessModel model) {
        return new OssProcessImpl(model).setParent(this);
    }
}
