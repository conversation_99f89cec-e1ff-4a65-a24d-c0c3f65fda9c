package com.daddylab.msaiagent.common.oss;

import com.aliyun.credentials.utils.AuthConstant;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.sts20150401.Client;
import com.aliyun.teaopenapi.models.Config;
import com.daddylab.msaiagent.common.oss.models.OssBucket;
import lombok.Data;
import lombok.SneakyThrows;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * <AUTHOR>
 * @since 2023/10/11
 */
@Data
@ConfigurationProperties(prefix = "oss")
@RefreshScope
public class OssConfig {
  private String endpoint;
  private String accessKeyId;
  private String accessKeySecret;
  private String publicBucket;
  private String privateBucket;
  private String publicUrl;
  private String privateUrl;
  private String prefixDir;
  private int defaultSignExpireSeconds = 3600;

  public OSS getOssClient() {
    return new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
  }

  private long stsTokenExpireSeconds = 1800L;
  private String stsRegion = "cn-hangzhou";
  private String stsEndpoint = "sts.cn-hangzhou.aliyuncs.com";
  private String stsRoleArn;
  private String stsAccessKeyId;
  private String stsAccessKeySecret;

  @SneakyThrows
  public Client getStsClient() {
    // 当您在初始化凭据客户端不传入任何参数时，Credentials工具会使用默认凭据链方式初始化客户端。
    // 详情请参考：https://help.aliyun.com/zh/sdk/developer-reference/manage-access-credentials
    Config config = new Config();
    // Endpoint 请参考 https://api.aliyun.com/product/Sts
    config.setEndpoint(stsEndpoint);
    config.setAccessKeyId(stsAccessKeyId);
    config.setAccessKeySecret(stsAccessKeySecret);
    config.setType(AuthConstant.ACCESS_KEY);
    com.aliyun.credentials.Client credentials = new com.aliyun.credentials.Client();
    config.setCredential(credentials);
    return new Client(config);
  }

  public String getBucketName(OssBucket bucket) {
    switch (bucket) {
      case PRIVATE:
        return privateBucket;
      case PUBLIC:
        return publicBucket;
      default:
        throw new IllegalArgumentException("无效的 OSS Bucket");
    }
  }
}
