package com.daddylab.msaiagent.common.oss.models;

import com.aliyun.oss.HttpMethod;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/9/20
 */
@Data
@Accessors(chain = true)
public class OssPresignUrlRequest {
  @ApiModelProperty(value = "请求方法", required = true, notes = "只支持 GET,PUT")
  private HttpMethod method = HttpMethod.GET;

  @ApiModelProperty(value = "资源URL", notes = "请求资源的完整URL，传了URL可以不传bucket和path")
  private String url;

  @ApiModelProperty(value = "bucket名称", required = true)
  private String bucket;

  @ApiModelProperty(value = "资源路径", required = true)
  private String path;

  @ApiModelProperty(value = "资源类型", notes = "上传文件时必填")
  private String contentType;

  @ApiModelProperty(value = "过期时间（秒）", notes = "不传默认为10分钟过期")
  private Integer expireSeconds;

  @ApiModelProperty(value = "附件名称", notes = "下载文件名称")
  private String attachmentFilename;
}
