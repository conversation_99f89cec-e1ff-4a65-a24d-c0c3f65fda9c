package com.daddylab.msaiagent.common.oss;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.PatternPool;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.*;
import com.daddylab.msaiagent.common.base.exception.AiAgentErrorCodeEnum;
import com.daddylab.msaiagent.common.base.exception.AiAgentException;
import com.daddylab.msaiagent.common.oss.models.OssPresignUrlRequest;
import com.daddylab.msaiagent.common.oss.models.OssProcess;
import com.daddylab.msaiagent.common.oss.models.SaveAs;
import com.daddylab.msaiagent.common.oss.models.VideoSnapshots;
import com.google.common.util.concurrent.RateLimiter;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.input.ObservableInputStream;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @since 2023/10/11
 */
@Slf4j
public class OssGatewayImpl implements OssGateway {
  private final OssConfig ossConfig;
  private int timeout = 30000;

  public OssGatewayImpl(OssConfig ossConfig) {
    this.ossConfig = ossConfig;
  }

  @Override
  public OssConfig getOssConfig() {
    return ossConfig;
  }

  @Override
  public String put(String path, InputStream input) {
    return put(ossConfig.getPrivateBucket(), path, input);
  }

  @Override
  public String put(String bucket, String path, InputStream input) {
    final OSS ossClient = ossConfig.getOssClient();
    final String objectPath = normalizePath(path, true);
    ossClient.putObject(bucket, objectPath, input);
    final String objectURL = getObjectURL(bucket, objectPath);
    log.info("[OSS] 上传OSS成功:{}", objectURL);
    return objectURL;
  }

  @Override
  public String transferURL(String bucket, String path, String inputURL) {
    try (final HttpResponse response =
        HttpUtil.createGet(inputURL, true).timeout(timeout).executeAsync()) {
      if (!response.isOk()) {
        throw new IOException(
            "open http connection error, response status:" + response.getStatus());
      }
      try (final InputStream inputStream = response.bodyStream()) {
        final ObservableInputStream observableInputStream = new ObservableInputStream(inputStream);
        final RateLimiter rateLimiter = RateLimiter.create(1f);
        observableInputStream.add(
            new ObservableInputStream.Observer() {
              int total = 0;

              @Override
              public void data(byte[] buffer, int offset, int length) throws IOException {
                total += length;
                if (rateLimiter.tryAcquire()) {
                  log.debug("[OSS] transferURL read {} bytes, {} totals", length, total);
                }
              }
            });
        return put(bucket, path, observableInputStream);
      }
    } catch (Exception e) {
      throw new OssGatewayException("[OSS] transferURL error", e);
    }
  }

  @Override
  public String putPublic(String path, InputStream input) {
    return put(ossConfig.getPublicBucket(), path, input);
  }

  private String getObjectURL(String bucket, String path) {
    return URLUtil.normalize(
        String.format("https://%s.oss-cn-hangzhou.aliyuncs.com/%s", bucket, path), false, true);
  }

  @Override
  public ObjectMetadata metadata(String bucket, String path) {
    return ossConfig.getOssClient().getObjectMetadata(bucket, normalizePath(path, false));
  }

  @Override
  public Map<String, String> imageInfo(String bucket, String path) {
    final GetObjectRequest getObjectRequest =
        new GetObjectRequest(bucket, normalizePath(path, false));
    getObjectRequest.setProcess("image/info");
    final OSSObject ossObject = ossConfig.getOssClient().getObject(getObjectRequest);
    final String content = IoUtil.read(ossObject.getObjectContent(), StandardCharsets.UTF_8);
    final JSONObject contentJsonObject = JSONUtil.parseObj(content);
    final HashMap<String, String> imageInfo = new HashMap<>();
    for (Entry<String, Object> entry : contentJsonObject.entrySet()) {
      if (entry.getValue() instanceof JSONObject) {
        final JSONObject value = (JSONObject) entry.getValue();
        imageInfo.put(entry.getKey(), (String) value.get("value"));
      }
    }
    return imageInfo;
  }

  @Override
  public Map<String, String> imageInfo(String ossUrl) {
    final String[] urlItems = parseObjectUrl(ossUrl);
    return imageInfo(urlItems[0], urlItems[1]);
  }

  @Override
  public void delete(String bucket, String path) {
    ossConfig.getOssClient().deleteObject(bucket, normalizePath(path, false));
  }

  protected String[] parseObjectUrl(String url) {
    url = URLUtil.decode(url);
    String regex = null;
    if (url.contains("?")) {
      regex = "https?://([^.]+)\\.oss-cn-hangzhou\\.aliyuncs\\.com(/[^?]+).*";
    } else {
      regex = "https?://([^.]+)\\.oss-cn-hangzhou\\.aliyuncs\\.com(/.+)";
    }
    final Pattern pattern = PatternPool.get(regex);
    if (!ReUtil.isMatch(pattern, url)) {
      throw AiAgentException.throwException(AiAgentErrorCodeEnum.OSS_OBJECT_ERROR);
    }
    final List<String> allGroups = ReUtil.getAllGroups(pattern, url);
    return new String[] {allGroups.get(1), allGroups.get(2)};
  }

  @Override
  public String generatePresignedUrl(String url) {
    final String[] object = parseObjectUrl(url);
    return generatePresignedUrl(object[0], object[1]);
  }

  @Override
  public String generatePresignedUrl(String url, int expireSeconds) {
    final String[] object = parseObjectUrl(url);
    return generatePresignedUrl(object[0], object[1], expireSeconds);
  }

  @Override
  public String generatePresignedUrl(String bucket, String path) {
    return generatePresignedUrl(
        bucket, normalizePath(path, false), ossConfig.getDefaultSignExpireSeconds());
  }

  @Override
  public String generatePresignedUrl(String bucket, String path, int expireSeconds) {
    return generatePresignedUrl(
        new OssPresignUrlRequest().setBucket(bucket).setPath(path).setExpireSeconds(expireSeconds));
  }

  @Override
  public String generatePresignedUrl(OssPresignUrlRequest request) {
    final GeneratePresignedUrlRequest generatePresignedUrlRequest;
    if (request.getUrl() != null) {
      final String[] urlItems = parseObjectUrl(request.getUrl());
      generatePresignedUrlRequest =
          new GeneratePresignedUrlRequest(
              urlItems[0], normalizePath(urlItems[1], false), request.getMethod());
    } else {
      generatePresignedUrlRequest =
          new GeneratePresignedUrlRequest(
              request.getBucket(), normalizePath(request.getPath(), false), request.getMethod());
    }
    generatePresignedUrlRequest.setContentType(request.getContentType());
    final ResponseHeaderOverrides responseHeaders = new ResponseHeaderOverrides();
    if (StrUtil.isNotBlank(request.getAttachmentFilename())) {
      final String contentDisposition =
          String.format("attachment; filename=\"%s\"", request.getAttachmentFilename());
      responseHeaders.setContentDisposition(contentDisposition);
    }
    generatePresignedUrlRequest.setResponseHeaders(responseHeaders);
    final Integer expireSeconds =
        Optional.ofNullable(request.getExpireSeconds())
            .orElseGet(ossConfig::getDefaultSignExpireSeconds);
    generatePresignedUrlRequest.setExpiration(getExpireDate(expireSeconds));
    return ossConfig.getOssClient().generatePresignedUrl(generatePresignedUrlRequest).toString();
  }

  @AllArgsConstructor(staticName = "of")
  public static class OSSSignUrlWrapper {
    private URL url;

    public String decode() {
      if (url == null) {
        return StrUtil.EMPTY;
      }
      String path = url.getPath();
      String decodePath = URLUtil.decode(path, StandardCharsets.UTF_8);
      if (!PatternPool.get(ReUtil.RE_CHINESE).matcher(decodePath).find()) {
        return url.toString();
      }
      String query = url.getQuery() != null ? "?" + url.getQuery() : "";
      String ref = url.getRef() != null ? "#" + url.getRef() : "";
      return url.getProtocol() + "://" + url.getAuthority() + decodePath + query + ref;
    }
  }

  @Override
  public void videoSnapshots(String bucket, String path, String saveAs) {
    videoSnapshots(
        bucket, path, VideoSnapshots.builder().build(), SaveAs.builder().o(saveAs).build());
  }

  @Override
  public void videoSnapshots(
      String bucket, String path, VideoSnapshots videoSnapshots, SaveAs saveAs) {
    final OssProcess process = OssProcess.command(videoSnapshots).and(saveAs);
    final String key = normalizePath(path, false);
    final AsyncProcessObjectRequest asyncProcessObjectRequest =
        new AsyncProcessObjectRequest(bucket, key, process.toString());
    ossConfig.getOssClient().asyncProcessObject(asyncProcessObjectRequest);
  }

  @NonNull
  private static DateTime getExpireDate(int expireSeconds) {
    return DateUtil.date(LocalDateTime.now().plusSeconds(expireSeconds));
  }

  @Override
  public String calculatePostSignature(Map<String, String> policyConditions) {
    final OSS ossClient = ossConfig.getOssClient();
    final PolicyConditions cons = new PolicyConditions();
    for (Entry<String, String> entry : policyConditions.entrySet()) {
      cons.addConditionItem(entry.getKey(), entry.getValue());
    }
    final String postPolicy =
        ossClient.generatePostPolicy(getExpireDate(ossConfig.getDefaultSignExpireSeconds()), cons);
    return ossClient.calculatePostSignature(postPolicy);
  }

  @Override
  public String normalizePath(String path, boolean prefix) {
    if (path.startsWith("http")) {
      path = URLUtil.getPath(path);
    }
    path = StrUtil.removePrefix(path, "/");
    if (prefix) {
      final String prefixDir = StrUtil.removePrefix(ossConfig.getPrefixDir(), "/");
      if (!path.startsWith(prefixDir)) {
        path = prefixDir + "/" + path;
      }
    }
    path = removeRedundantSlash(path);
    path = StrUtil.removePrefix(path, "/");
    path = URLUtil.decode(path);
    return path;
  }

  @Override
  public String toHttpUrl(String ossUrl) {
    if (ossUrl == null) {
      return null;
    }
    final HashMap<String, String> urlMap = new HashMap<>();
    urlMap.put("oss://daddyoss-private/", ossConfig.getPrivateUrl() + "/");
    urlMap.put("oss://daddyoss/", ossConfig.getPublicUrl() + "/");
    for (Entry<String, String> entry : urlMap.entrySet()) {
      if (ossUrl.startsWith(entry.getKey())) {
        return ossUrl.replaceFirst(entry.getKey(), entry.getValue());
      }
    }
    return ossUrl;
  }

  @NonNull
  private static String removeRedundantSlash(String objectPath) {
    return objectPath.replaceAll("/+", "/");
  }
}
