package com.daddylab.msaiagent.common.markdown;

import java.util.Locale;

/**
 * <AUTHOR>
 * @since 2025/4/11
 */
public class MarkdownUtils {

  /**
   * 将对象转换为可读字符串(markdown格式)
   *
   * @param obj 对象
   * @return 可读字符串
   */
  public static String toMarkdown(Object obj) {
    return ObjectPrinter.INSTANCE.print(obj, Locale.getDefault());
  }

  /**
   * 将对象转换为可读字符串(markdown格式)
   *
   * @param obj 对象
   * @param title 标题
   * @return 可读字符串
   */
  public static String toMarkdown(Object obj, String title) {
    return ObjectPrinter.INSTANCE.print(obj, title);
  }
}
