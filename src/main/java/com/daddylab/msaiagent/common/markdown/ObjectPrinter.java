package com.daddylab.msaiagent.common.markdown;

import cn.hutool.core.bean.BeanDesc;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.PropDesc;
import cn.hutool.core.util.StrUtil;
import com.daddylab.msaiagent.common.bailian.tools.BaiLianIgnore;
import com.daddylab.msaiagent.common.base.enums.IEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.format.Printer;

import javax.annotation.Nullable;
import java.util.Arrays;
import java.util.Collection;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;

/**
 * <AUTHOR>
 * @since 2025/4/11
 */
@Slf4j
public class ObjectPrinter implements Printer<Object> {

  static final ObjectPrinter INSTANCE = new ObjectPrinter();
  public static final String NULL_STR = "无";
  public static final String EMPTY_STR = "空";

  @Override
  public @NotNull String print(@Nullable Object object, @Nullable Locale locale) {
    if (object == null) {
      return NULL_STR;
    }
    return print(object, 0);
  }

  public String print(Object object, String title) {
    return "## " + title + "\n\n" + print(object, 0);
  }

  private String print(Object obj, int level) {
    if (obj == null) {
      return NULL_STR;
    }
    if (obj instanceof IEnum) {
      return "**" + ((IEnum<?>) obj).getDesc() + "**";
    }
    if (obj instanceof String) {
      return toStringValue(obj);
    }
    if (obj instanceof Number || obj instanceof Character) {
      return toStringValue(obj);
    }
    if (obj instanceof Boolean) {
      return ((Boolean) obj) ? "**是**" : "**否**";
    }
    if (obj instanceof Collection) {
      return handleCollection((Collection<?>) obj, level);
    }
    if (obj instanceof Map) {
      return handleMap((Map<?, ?>) obj, level);
    }
    if (obj.getClass().isArray()) {
      return handleCollection(Arrays.asList((Object[]) obj), level);
    }
    return processObject(obj, level);
  }

  private static @NotNull String toStringValue(Object obj) {
    final String str = obj.toString();
    if (StrUtil.isBlank(str)) {
      return NULL_STR;
    }
    return str;
  }

  private String processObject(Object obj, int level) {
    Class<?> clazz = obj.getClass();
    StringBuilder sb = new StringBuilder();
    if (level == 0) {
      sb.append("### ").append(getClassName(clazz)).append("\n\n");
    }
    final BeanDesc beanDesc = BeanUtil.getBeanDesc(clazz);
    for (PropDesc prop : beanDesc.getProps()) {
      final PropDescWrapper propDescWrapper = new PropDescWrapper(prop);
      if (!prop.isReadable(true)) {
        continue;
      }
      try {
        Object value = prop.getValue(obj);
        String valueStr = print(value, level + 1);
        String fieldName = getFieldName(propDescWrapper);
        if (fieldName == null) {
          continue;
        }
        if (valueStr == null) {
          continue;
        }
        final RichText richText = propDescWrapper.getAnnotation(RichText.class);
        sb.append(getIndent(level + 1)).append("- **").append(fieldName).append("**:");
        if (valueStr.contains("\n") || richText != null) {
          sb.append("\n").append(valueStr).append("\n\n");
        } else {
          sb.append(" ").append(valueStr).append("\n");
        }
      } catch (Exception e) {
        log.error("[MarkdownUtils] processObject error", e);
      }
    }
    return sb.toString();
  }

  private String handleCollection(Collection<?> collection, int level) {
    if (collection.isEmpty()) {
      return EMPTY_STR;
    }
    StringBuilder sb = new StringBuilder();
    for (Object item : collection) {
      String itemStr = print(item, level + 1);
      if (itemStr.contains("\n")) {
        sb.append(getIndent(level + 1)).append("- ");
        String[] lines = itemStr.split("\n");
        for (int i = 0; i < lines.length; i++) {
          if (i == 0) {
            sb.append(lines[i].trim());
          } else if (!lines[i].trim().isEmpty()) {
            sb.append("\n").append(getIndent(level + 2)).append(lines[i].trim());
          }
        }
        sb.append("\n");
      } else {
        sb.append(getIndent(level + 1)).append("- ").append(itemStr).append("\n");
      }
    }
    return sb.toString();
  }

  private String handleMap(Map<?, ?> map, int level) {
    if (map.isEmpty()) {
      return EMPTY_STR;
    }
    StringBuilder sb = new StringBuilder();
    for (Entry<?, ?> entry : map.entrySet()) {
      Object value = entry.getValue();
      String valueStr = print(value, level + 1);
      sb.append(getIndent(level + 1)).append("- **").append(entry.getKey()).append("**:");
      if (isComplexType(value) || valueStr.contains("\n")) {
        sb.append("\n");
        String[] lines = valueStr.split("\n");
        for (String line : lines) {
          sb.append(getIndent(level + 2)).append(line.trim()).append("\n");
        }
      } else {
        sb.append(" ").append(valueStr).append("\n");
      }
    }
    return sb.toString();
  }

  private String getFieldName(PropDescWrapper propDesc) {
    final BaiLianIgnore baiLianIgnore = propDesc.getAnnotation(BaiLianIgnore.class);
    if (baiLianIgnore != null) {
      return null;
    }
    ApiModelProperty annotation = propDesc.getAnnotation(ApiModelProperty.class);
    if (annotation != null && !annotation.value().isEmpty()) {
      return annotation.value();
    }
    // 将lowerCamel转换为更易读的格式（如userName → User Name）
    String name = propDesc.getName();
    return name.replaceAll("([A-Z])", " $1").trim().substring(0, 1).toUpperCase()
        + name.substring(1);
  }

  private String getClassName(Class<?> clazz) {
    final ApiModel annotation = clazz.getAnnotation(ApiModel.class);
    if (annotation != null && !annotation.value().isEmpty()) {
      return annotation.value();
    }
    // 将lowerCamel转换为更易读的格式（如userName → User Name）
    String name = clazz.getName();
    return name.replaceAll("([A-Z])", " $1").trim().substring(0, 1).toUpperCase()
        + name.substring(1);
  }

  private String getIndent(int level) {
    StringBuilder sb = new StringBuilder();
    for (int i = 0; i < Math.max(0, level); i++) {
      sb.append("  ");
    }
    return sb.toString();
  }

  private boolean isComplexType(Object obj) {
    if (obj == null) {
      return false;
    }
    return obj instanceof Collection
        || obj instanceof Map
        || obj.getClass().isArray()
        || !(obj instanceof String
            || obj instanceof Number
            || obj instanceof Boolean
            || obj instanceof Character
            || obj instanceof IEnum);
  }
}
