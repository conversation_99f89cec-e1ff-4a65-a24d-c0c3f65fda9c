package com.daddylab.msaiagent.common.markdown;

import cn.hutool.core.bean.PropDesc;
import cn.hutool.core.util.StrUtil;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @since 2025/3/19
 */
public class PropDescWrapper extends PropDesc {

  public PropDescWrapper(PropDesc propDesc) {
    super(propDesc.getField(), propDesc.getGetter(), propDesc.getSetter());
  }

  public PropDescWrapper(Field field, Method getter, Method setter) {
    super(field, getter, setter);
  }

  public String getName() {
    if (this.getGetter() != null) {
      return StrUtil.getGeneralField(this.getGetter().getName());
    }
    if (this.getField() != null) {
      return this.getRawFieldName();
    }
    return null;
  }

  public <T extends Annotation> T getAnnotation(Class<T> annotationClass) {
    if (this.getGetter() != null) {
      final T annotation = this.getGetter().getAnnotation(annotationClass);
      if (annotation != null) {
        return annotation;
      }
    }
    final Field field = this.getField();
    if (field != null) {
      final T annotation = field.getAnnotation(annotationClass);
      if (annotation != null) {
        return annotation;
      }
    }
    return null;
  }
}
