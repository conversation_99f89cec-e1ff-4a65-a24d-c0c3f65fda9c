package com.daddylab.msaiagent.common.feign;

import com.daddylab.msaiagent.common.feign.config.YingDaoConfiguration;
import com.daddylab.msaiagent.common.feign.domain.query.YingDaoStartQuery;
import com.daddylab.msaiagent.common.feign.domain.query.YingDaoTaskResultQuery;
import com.daddylab.msaiagent.common.feign.domain.vo.YingDaoTaskQueryVo;
import com.daddylab.msaiagent.common.feign.domain.vo.YingDaoTaskStartVo;
import com.daddylab.msaiagent.common.feign.response.YingDaoResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 影刀开放API
 */
@FeignClient(name = "${service.provider.yingdao.serveName}", url = "${service.provider.yingdao.url}", configuration = {YingDaoConfiguration.class})
public interface YingDaoFeignClient {

    /**
     * 启动任务
     *
     * @param yingDaoStartQuery YingDaoStartQuery
     * @return com.daddylab.workbench.feign.response.YingDaoResponse<com.daddylab.workbench.feign.domain.vo.YingDaoTaskStartVo>
     * @date 2023/11/28 17:34
     * <AUTHOR>
     */
    @PostMapping("/oapi/dispatch/v2/task/start")
    YingDaoResponse<YingDaoTaskStartVo> start(@RequestBody YingDaoStartQuery yingDaoStartQuery);

    /**
     * 查询影刀执行结果
     *
     * @param yingDaoTaskResultQuery com.daddylab.msaiagent.common.feign.domain.query.YingDaoTaskResultQuery
     * @return com.daddylab.msaiagent.common.feign.response.YingDaoResponse<com.daddylab.msaiagent.common.feign.domain.vo.YingDaoTaskQueryVo>
     * <AUTHOR>
     * @date 2025/4/29 16:49
     */
    @PostMapping("/oapi/dispatch/v2/task/query")
    YingDaoResponse<YingDaoTaskQueryVo> queryResult(@RequestBody YingDaoTaskResultQuery yingDaoTaskResultQuery);
}
