package com.daddylab.msaiagent.common.feign.domain.query;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName YingDaoStartForm.java
 * @Description 描述类的作用
 * @date 2023-11-28 17:31
 */
@AllArgsConstructor(staticName = "of")
@NoArgsConstructor
@Data
public class YingDaoTaskResultQuery {
    @JsonProperty("taskUuid")
    private String taskUuid;

}
