package com.daddylab.msaiagent.common.feign.config;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.daddylab.msaiagent.common.config.property.YingDaoProperty;
import com.daddylab.msaiagent.common.feign.domain.vo.YingDaoAccessTokenVO;
import com.daddylab.msaiagent.common.feign.response.YingDaoResponse;
import com.daddylab.msaiagent.common.utils.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import feign.RequestInterceptor;
import feign.codec.Decoder;
import feign.codec.Encoder;
import feign.form.spring.SpringFormEncoder;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.ResponseEntityDecoder;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

/**
 * 影刀配置
 *
 * @date 2023/11/28 17:05
 * <AUTHOR>
 */
@Configuration
public class YingDaoConfiguration {

    @Autowired
    private ObjectFactory<HttpMessageConverters> messageConverters;
    @Autowired
    private YingDaoProperty yingDaoProperty;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    public ObjectFactory<HttpMessageConverters> feignHttpMessageConverter() {
        final HttpMessageConverters httpMessageConverters = new HttpMessageConverters(new JavaMappingJackson2HttpMessageConverter());
        return () -> httpMessageConverters;
    }

    public static class JavaMappingJackson2HttpMessageConverter extends MappingJackson2HttpMessageConverter {
        JavaMappingJackson2HttpMessageConverter() {
            List<MediaType> mediaTypes = new ArrayList<>();
//            mediaTypes.add(MediaType.valueOf(MediaType.TEXT_HTML_VALUE + ";charset=UTF-8"));
            mediaTypes.add(MediaType.valueOf(MediaType.ALL_VALUE + ";charset=UTF-8"));
            setSupportedMediaTypes(mediaTypes);
        }
    }
    @Bean
    public Encoder feignFormEncoder() {
        return new SpringFormEncoder(new SpringEncoder(messageConverters));
    }

    @Bean
    public Decoder feignDecoder() {
        return new ResponseEntityDecoder(new SpringDecoder(feignHttpMessageConverter()));
    }

    @Bean("yingdaoRequestInterceptor")
    public RequestInterceptor requestInterceptor() {
        return template -> {
            //获取accessToken
            String tokenKey = "yingdao:token";
            String accessToken = null;
            if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(tokenKey))) {
                accessToken = stringRedisTemplate.opsForValue().get(tokenKey);
            } else {
                String url = String.format("https://api.yingdao.com/oapi/token/v2/token/create?accessKeyId=%s&accessKeySecret=%s", yingDaoProperty.getAccessKeyId(), yingDaoProperty.getAccessKeySecret());
                HttpResponse response = HttpUtil.createPost(url).execute();
                if (!response.isOk()) {
                    return;
                }
                YingDaoResponse<YingDaoAccessTokenVO> ydResponse = JsonUtil.parseObject(response.body(), new TypeReference<YingDaoResponse<YingDaoAccessTokenVO>>() {
                });
                Assert.isTrue(ydResponse != null && ydResponse.getCode() == 200, "获取影刀token失败");
                accessToken = ydResponse.getData().getAccessToken();
                stringRedisTemplate.opsForValue().set(tokenKey, accessToken, ydResponse.getData().getExpiresIn(), TimeUnit.SECONDS);
            }
            if (StrUtil.isEmpty(accessToken)) {
                return;
            }
            template.header("Authorization", String.format("Bearer %s", accessToken));
        };
    }
}
