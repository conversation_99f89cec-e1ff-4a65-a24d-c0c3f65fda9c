package com.daddylab.msaiagent.common.feign.domain.query;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.checkerframework.common.value.qual.ArrayLen;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName YingDaoStartForm.java
 * @Description 描述类的作用
 * @date 2023-11-28 17:31
 */
@AllArgsConstructor(staticName = "of")
@NoArgsConstructor
@Data
public class YingDaoStartQuery {
    /**
     * scheduleUuid
     */
    @JsonProperty("scheduleUuid")
    private String scheduleUuid;
    /**
     * scheduleRelaParams
     */
    @JsonProperty("scheduleRelaParams")
    private List<ScheduleRelaParams> scheduleRelaParams;

    /**
     * ScheduleRelaParams
     */
    @NoArgsConstructor
    @Data
    public static class ScheduleRelaParams {
        /**
         * robotUuid
         */
        @JsonProperty("robotUuid")
        private String robotUuid;
        /**
         * params
         */
        @JsonProperty("params")
        private List<Params> params;

        /**
         * Params
         */
        @AllArgsConstructor(staticName = "of")
        @NoArgsConstructor
        @Data
        public static class Params {
            /**
             * name
             */
            @JsonProperty("name")
            private String name;
            /**
             * value
             */
            @JsonProperty("value")
            private String value;
            /**
             * type str-string int float bool file
             */
            @JsonProperty("type")
            private String type;
            public static Params ofString(String name, String value) {
                Params params = new Params();
                params.setName(name);
                params.setValue(value);
                params.setType("str");
                return params;
            }

            public static Params ofInt(String name, String value) {
                Params params = new Params();
                params.setName(name);
                params.setValue(value);
                params.setType("int");
                return params;
            }
        }
    }


    /**
     * 无参
     *
     * @param scheduleUuid String
     * @return com.daddylab.workbench.feign.domain.query.YingDaoStartQuery
     * @date 2023/11/28 17:33
     * <AUTHOR>
     */
    public static YingDaoStartQuery ofNoParams(String scheduleUuid) {
        YingDaoStartQuery yingDaoStartQuery = new YingDaoStartQuery();
        yingDaoStartQuery.setScheduleUuid(scheduleUuid);
        yingDaoStartQuery.setScheduleRelaParams(Collections.emptyList());
        return yingDaoStartQuery;
    }

}
