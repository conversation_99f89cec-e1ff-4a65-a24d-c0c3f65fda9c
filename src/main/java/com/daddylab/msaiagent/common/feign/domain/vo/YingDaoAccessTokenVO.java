package com.daddylab.msaiagent.common.feign.domain.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @ClassName YingDaoAccessTokenVO.java
 * @Description 描述类的作用
 * @date 2023-11-28 17:12
 */
@NoArgsConstructor
@Data
public class YingDaoAccessTokenVO {

    /**
     * accessToken
     */
    @JsonProperty("accessToken")
    private String accessToken;
    /**
     * expiresIn
     */
    @JsonProperty("expiresIn")
    private Integer expiresIn;
}
