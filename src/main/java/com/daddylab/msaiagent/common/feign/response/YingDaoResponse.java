package com.daddylab.msaiagent.common.feign.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @ClassName YingDaoResponse.java
 * @Description 描述类的作用
 * @date 2023-11-28 17:11
 */
@NoArgsConstructor
@Data
public class YingDaoResponse<T> {

    /**
     * data
     */
    @JsonProperty("data")
    private T data;
    /**
     * code
     */
    @JsonProperty("code")
    private Integer code;
    /**
     * success
     */
    @JsonProperty("success")
    private Boolean success;
    /**
     * requestId
     */
    @JsonProperty("requestId")
    private String requestId;
}
