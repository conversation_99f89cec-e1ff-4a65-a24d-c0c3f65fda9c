package com.daddylab.msaiagent.common.feign.domain.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 *
 * @className YingDaoTaskQueryVo
 * <AUTHOR>
 * @date 2025/4/29 16:47
 * @description: TODO 
 */
@NoArgsConstructor
@Data
public class YingDaoTaskQueryVo {

    @JsonProperty("taskUuid")
    private String taskUuid;
    @JsonProperty("taskName")
    private String taskName;
    // waiting 等待调度
    //running 任务运行中
    //finish
    //任务运行结束
    //stopping
    //任务正在停止
    //stopped
    //已结束
    //error
    //异常
    @JsonProperty("status")
    private String status;
    @JsonProperty("statusName")
    private String statusName;
    @JsonProperty("startTime")
    private String startTime;
    @JsonProperty("endTime")
    private String endTime;
    @JsonProperty("jobDataList")
    private List<JobDataList> jobDataList;

    @NoArgsConstructor
    @Data
    public static class JobDataList {
        @JsonProperty("jobUuid")
        private String jobUuid;
        @JsonProperty("status")
        private String status;
        @JsonProperty("statusName")
        private String statusName;
        @JsonProperty("robotUuid")
        private String robotUuid;
        @JsonProperty("robotName")
        private String robotName;
        @JsonProperty("startTime")
        private String startTime;
        @JsonProperty("endTime")
        private String endTime;
        @JsonProperty("remark")
        private String remark;
        @JsonProperty("robotParams")
        private RobotParams robotParams;
        @JsonProperty("robotClientUuid")
        private String robotClientUuid;
        @JsonProperty("robotClientName")
        private String robotClientName;
        @JsonProperty("screenshotUrl")
        private String screenshotUrl;
        @JsonProperty("createTime")
        private String createTime;

        @NoArgsConstructor
        @Data
        public static class RobotParams {
            @JsonProperty("inputs")
            private List<Inputs> inputs;
            @JsonProperty("outputs")
            private List<Outputs> outputs;

            @NoArgsConstructor
            @Data
            public static class Inputs {
                @JsonProperty("name")
                private String name;
                @JsonProperty("value")
                private String value;
                @JsonProperty("type")
                private String type;
            }

            @NoArgsConstructor
            @Data
            public static class Outputs {
                @JsonProperty("name")
                private String name;
                @JsonProperty("value")
                private String value;
                @JsonProperty("type")
                private String type;
            }
        }
    }

    public Boolean isFinal() {
        return isError() || isSuccess() || isSuccess();
    }

    public Boolean isSuccess() {
        return "finish".equals(status);
    }
    public Boolean isError() {
        return "error".equals(status);
    }
    public Boolean isStop() {
        return "stopped".equals(status);
    }
}
