package com.daddylab.msaiagent.common.bailian.impl.support;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ReflectUtil;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.Client;
import com.daddylab.msaiagent.common.base.exception.AiAgentErrorCodeEnum;
import com.daddylab.msaiagent.common.base.exception.AiAgentException;
import com.daddylab.msaiagent.common.utils.JsonUtil;
import com.google.api.client.util.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cglib.proxy.MethodInterceptor;
import org.springframework.cglib.proxy.MethodProxy;
import org.springframework.retry.RetryException;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * @className BaiLianClientInterceptor
 * <AUTHOR>
 * @date 2025/3/6 10:14
 * @description: TODO
 */
@Slf4j
public class BaiLianClientInterceptor implements MethodInterceptor {
  private final Set<String> superMethodNameFilterSets = new HashSet<>();
  private final Set<String> clientMethodNameFilterSets =
          Set.of(
                  "deleteFile",
                  "listIndices",
                  "retrieve",
                  "syncCategoryToIndex",
                  "describeFile",
                  "deleteIndexDocument",
                  "listChunks",
                  "syncFileIdToIndex",
                  "submitIndexAddDocumentsJob",
                  "submitIndexAddDocumentsJobWithOptions", "getIndexJobStatus");

  public BaiLianClientInterceptor() {
    Method[] publicMethods = ReflectUtil.getPublicMethods(Client.class);
    Arrays.stream(publicMethods).map(Method::getName).forEach(superMethodNameFilterSets::add);
  }

  @Override
  public Object intercept(Object obj, Method method, Object[] args, MethodProxy methodProxy)
      throws Throwable {
    if (!clientMethodNameFilterSets.contains(method.getName())) {
      return methodProxy.invokeSuper(obj, args);
    }
    Object result = null;
    try {
      result = methodProxy.invokeSuper(obj, args);
      log.info(
          "[调用百炼接口] 执行完成. methodName={}, args={}, result={}",
          method.getName(),
              JsonUtil.toJSONString(args),
          JsonUtil.toJSONString(result));
      BaseClientResponse baseClientResponse =
          BeanUtil.copyProperties(result, BaseClientResponse.class);
      if (baseClientResponse.getStatusCode() != 200) {
        throw AiAgentException.throwException(
            AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, baseClientResponse.getBody().getMessage());
      }
      BaseClientBody baseClientBody = baseClientResponse.getBody();
      if (!"200".equals(baseClientBody.getStatus())) {
        throw AiAgentException.throwException(
            AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, baseClientBody.getMessage());
      }
    } catch (AiAgentException AiAgentException) {
      throw AiAgentException;
    } catch (TeaException tea) {
      if ("Throttling.Api".equals(tea.getCode())
          && tea.getMessage().contains("Request was denied due to user flow control")) {
        throw new RetryException("need to retry");
      }
      throw AiAgentException.throwException(AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, tea.getMessage());
    } catch (Exception e) {
      log.error("[初始化失败] methodName={}, msg={}", method.getName(), e.getMessage());
      throw AiAgentException.throwException(AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, e.getMessage());
    }
    return result;
  }

  @Data
  public static class BaseClientResponse {
    private java.util.Map<String, String> headers;
    private Integer statusCode;
    private BaseClientBody body;
  }

  @Data
  public static class BaseClientBody {
    private String code;
    private String message;
    private String requestId;
    private String status;
    private Boolean success;
  }

  public boolean filterMethod(String methodName) {
    return !clientMethodNameFilterSets.contains(methodName);
    //        return superMethodNameFilterSets.contains(methodName) ||
    // clientMethodNameFilterSets.contains(methodName);
  }
}
