package com.daddylab.msaiagent.common.bailian.domain.vo;

import lombok.Data;

/**
 *
 * @className BaiLianIndexChunksVO
 * <AUTHOR>
 * @date 2025/3/13 16:22
 * @description: TODO 
 */
@Data
public class BaiLianIndexDocVO {

    /**
     * 索引ID
     */
    private String indexId;

    /**
     * 非结构化数据指向类目ID，结构化数据代表数据表ID
     */
    private String sourceId;

    /**
     * 知识库文档ID
     */
    private String fileId;

    /**
     * 文档名称
     */
    private String name;

    /**
     * 文档大小，单位字节 Byte。
     */
    private Integer size;

    /**
     * 文档格式类型。可能值为： pdf、docx、doc、txt、md、pptx、ppt、png、jpg、jpeg、bmp、gif、EXCEL。
     */
    private String documentType;

    /**
     * 文档导入状态。可能值为： INSERT_ERROR：文档导入失败。 RUNNING：文档导入中。 DELETED：文档已删除。 FINISH：文档导入成功。
     */
    private String status;

    /**
     * 文档导入错误状态码。
     */
    private String code;

    /**
     * 文档导入错误信息。
     */
    private String message;
}
