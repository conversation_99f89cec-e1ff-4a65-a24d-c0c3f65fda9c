package com.daddylab.msaiagent.common.bailian.domain.dto;

import com.google.common.collect.Lists;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 * @className RetrieveQuery
 * <AUTHOR>
 * @date 2025/3/6 11:45
 * @description: TODO 
 */
@Data
public class RetrieveQuery {
    private String indexId;
    private String keyword;
    private Integer size;


    // 是否启用rerank
    private Boolean enableReRank;
    private List<RankModel> rankModel;
    private BigDecimal reRankMinScore;
    private Integer reRankTopN;

    public static RetrieveQuery ofQuery(String indexId, String keyword, Integer size) {
        RetrieveQuery retrieveQuery = new RetrieveQuery();
        retrieveQuery.setIndexId(indexId);
        retrieveQuery.setKeyword(keyword);
        retrieveQuery.setSize(size);
        retrieveQuery.setEnableReRank(true);
        retrieveQuery.setRankModel(Lists.newArrayList());
        retrieveQuery.setReRankMinScore(new BigDecimal("0.2"));
        retrieveQuery.setReRankTopN(size);
        return retrieveQuery;
    }

    @Data
    public static class RankModel {
        // 模型名称
        private String modelName;
    }
}
