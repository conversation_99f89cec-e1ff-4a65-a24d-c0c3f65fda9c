package com.daddylab.msaiagent.common.bailian;

import com.aliyun.bailian20231229.models.GetIndexJobStatusResponseBody.GetIndexJobStatusResponseBodyData;
import com.aliyun.bailian20231229.models.ListFileResponseBody.ListFileResponseBodyDataFileList;
import com.aliyun.bailian20231229.models.ListMemoryNodesResponseBody;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.daddylab.msaiagent.common.bailian.domain.dto.RetrieveQuery;
import com.daddylab.msaiagent.common.bailian.domain.enums.ModuleEnum;
import com.daddylab.msaiagent.common.bailian.domain.vo.*;

import java.util.Collection;
import java.util.List;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @className BaiLianService
 * @date 2025/2/28 14:57
 * @description: TODO
 */
public interface BaiLianService {

  /**
   * 获取百炼的类目列表
   *
   * @return java.util.List<com.daddylab.tech.common.bailian.domain.vo.BaiLianCategoryVO>
   * <AUTHOR>
   * @date 2025/3/3 10:50
   */
  List<BaiLianCategoryVO> getCategoryList(String pid);

  BaiLianCategoryVO addCategory(String pid, String name);

  String getCategoryId(ModuleEnum moduleEnum);

  FileInfoVO getFileInfo(String fileId);

  /**
   * 上传oss文件到百炼
   *
   * @param moduleEnum com.daddylab.tech.common.bailian.domain.enums.ModuleEnum
   * @param ossUrl java.lang.String oss的链接
   * @param filename java.lang.String 文件的名称(为空则取ossUrl的文件名)
   * @return java.lang.String 文件的fileId
   * <AUTHOR>
   * @date 2025/3/3 10:49
   */
  UploadFileResult uploadOssFile(ModuleEnum moduleEnum, String ossUrl, String filename);

  UploadFileResult uploadOssFile(ModuleEnum moduleEnum, String ossUrl, String filename, List<String> tags);

  /**
   * 上传本地文件到百炼
   *
   * @param moduleEnum com.daddylab.tech.common.bailian.domain.enums.ModuleEnum
   * @param fileBytes byte
   * @param fileName java.lang.String
   * @return java.lang.String 文件的fileId
   * <AUTHOR>
   * @date 2025/3/3 10:49
   */
  UploadFileResult uploadFile(ModuleEnum moduleEnum, byte[] fileBytes, String fileName);

  UploadFileResult uploadFile(ModuleEnum moduleEnum, byte[] fileBytes, String fileName, List<String> tags);

  /**
   * 上传文件到百炼的类目中
   *
   * @param baiLianCategoryId java.lang.String 百炼类目ID
   * @param fileBytes byte
   * @param fileName java.lang.String
   * @param tags java.util.List<java.lang.String>
   * @return java.lang.String
   * <AUTHOR>
   * @date 2025/4/11 17:30
   */
  UploadFileResult uploadFile(String baiLianCategoryId, byte[] fileBytes, String fileName, List<String> tags);

  void deleteFile(String fileId);

  void updateFileTags(String fileId, List<String> tags);

  /**
   * 获取索引列表
   */
  List<BaiLianIndexVO> getIndexList(String indexName);

  /**
   * 检索知识索引
   */
  List<RetrieveVO> retrieve(RetrieveQuery retrieveQuery);

  List<BaiLianIndexDocVO> indexDocList(String indexId);

  /**
   * 分页获取索引文档列表
   * 
   * @param indexId
   * @param consumer
   * @param pageSize
   */
  void foreachIndexDocList(String indexId, Consumer<IPage<BaiLianIndexDocVO>> consumer, int pageSize);

  void syncCategoryToIndex(List<String> categoryIds, String indexId) throws Exception;

  String syncFileIdToIndex(List<String> fileIds, String indexId);

  /**
   * 删除知识库中的文档(索引)
   *
   * @param indexId java.lang.String
   * @param docIds java.util.List<java.lang.String> 等价于fieldIds
   * <AUTHOR>
   * @date 2025/3/10 10:46
   */
  void deleteIndexDoc(String indexId, List<String> docIds);

//  /**
//   * 单轮对话
//   *
//   * @param appId 百炼-我的应用-应用ID
//   * @param apiKey 百炼-我的应用-发布渠道-API Key
//   * @param prompt 提示词
//   * @return output.getText() 回答
//   */
//  ApplicationOutput chat(String appId, String apiKey, String prompt);

  List<BaiLianIndexChunksVO> indexChunks(String indexId, String fileId);

  void foreachFiles(
      String categoryId,
      Consumer<List<ListFileResponseBodyDataFileList>> consumer,
      int pageSize,
      int pageLimit);

  String createMemory(String user);

  List<ListMemoryNodesResponseBody.ListMemoryNodesResponseBodyMemoryNodes> getMemoryNodeList(
      String memoryId);

  void deleteMemory(Collection<String> memoryIds);

  void showMemory();

  /**
   * 创建索引
   * @param name 索引名称
   * @return 知识库ID，又称IndexId
   */
  String createIndex(String name);
  /**
   * 删除知识库索引
   * @param indexId 知识库ID
   * @return
   */
  Boolean deleteIndex(String indexId);
  /**
   * 提交索引创建任务
   */
  Boolean SubmitIndexJob(String indexId);

  void getIndexJobStatus(
      String indexId,
      String jobId,
      int pageSize,
      Consumer<GetIndexJobStatusResponseBodyData> consumer);
}
