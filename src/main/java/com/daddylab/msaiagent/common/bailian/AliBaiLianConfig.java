package com.daddylab.msaiagent.common.bailian;

import com.aliyun.bailian20231229.Client;
import com.aliyun.teaopenapi.models.Config;
import com.daddylab.msaiagent.common.bailian.impl.support.BaiLianClientInterceptor;
import com.google.common.collect.Lists;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cglib.proxy.Enhancer;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @className AliBaiLianConfig
 * <AUTHOR>
 * @date 2025/2/28 14:44
 * @description: TODO
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "bailian")
@RefreshScope
public class AliBaiLianConfig {
  private String accessKeyId;
  private String accessKeySecret;
  private String apiKey;
  private String endpoint;
  private String workspaceId;
  private String parentCategoryId;
  private List<BaiLianIndexConfig> indexConfigs;

  /** 用户长期记忆数量限制 */
  private String userMemoryLimitSize = "30";

  private String appId;
  //     应用调用API key
  private String appApiKey;
  //      应用名称
  private String appName;

  /** 不做自动清理的文件类型（默认用户上传的文件不做清理） */
  private List<Integer> notRemoveFileTypes = Lists.newArrayList(15);



  @Data
  public static class BaiLianIndexConfig {

    /** 索引ID */
    private String indexId;

    /** 索引关联文件分类ID */
    private List<String> categoryIds;

    /** 每次提交的文档数量限制 */
    private int docLimitPerSubmit = 1000;

    /** 是否禁用自动索引 */
    private boolean autoIndexDisabled;

    /** 是否禁止同步索引下文档到本地数据库 */
    private boolean syncIndexDocDisabled;
  }

  @Bean
  public Client baiLianClient() {
    Config config =
        new Config()
            .setAccessKeyId(accessKeyId)
            .setAccessKeySecret(accessKeySecret)
            .setEndpoint(endpoint);
    Enhancer enhancer = new Enhancer();
    enhancer.setSuperclass(Client.class);
    enhancer.setCallback(new BaiLianClientInterceptor());
    return (Client) enhancer.create(new Class[] {Config.class}, new Object[] {config});
  }
}
