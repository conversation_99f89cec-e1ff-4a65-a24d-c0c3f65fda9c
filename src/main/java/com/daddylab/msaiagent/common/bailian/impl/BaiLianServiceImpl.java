package com.daddylab.msaiagent.common.bailian.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.aliyun.bailian20231229.Client;
import com.aliyun.bailian20231229.models.*;
import com.aliyun.bailian20231229.models.GetIndexJobStatusResponseBody.GetIndexJobStatusResponseBodyData;
import com.aliyun.bailian20231229.models.ListFileResponseBody.ListFileResponseBodyData;
import com.aliyun.bailian20231229.models.ListFileResponseBody.ListFileResponseBodyDataFileList;
import com.aliyun.bailian20231229.models.ListIndexDocumentsResponseBody.ListIndexDocumentsResponseBodyDataDocuments;
import com.aliyun.tea.TeaException;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.daddylab.msaiagent.common.bailian.AliBaiLianConfig;
import com.daddylab.msaiagent.common.bailian.BaiLianService;
import com.daddylab.msaiagent.common.bailian.domain.dto.RetrieveQuery;
import com.daddylab.msaiagent.common.bailian.domain.enums.ModuleEnum;
import com.daddylab.msaiagent.common.bailian.domain.vo.*;
import com.daddylab.msaiagent.common.base.exception.AiAgentErrorCodeEnum;
import com.daddylab.msaiagent.common.base.exception.AiAgentException;
import com.daddylab.msaiagent.common.oss.OssGateway;
import com.daddylab.msaiagent.common.utils.JsonUtil;
import com.daddylab.msaiagent.common.utils.PageUtils;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.Lists;
import com.google.gson.internal.LinkedTreeMap;
import kong.unirest.HttpResponse;
import kong.unirest.Unirest;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.groovy.util.Maps;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.RetryException;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.retry.support.RetryTemplateBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.io.ByteArrayInputStream;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @className BailianServiceImpl
 * @date 2025/2/28 14:57
 * @description: TODO
 */
@Service
@Slf4j
public class BaiLianServiceImpl implements BaiLianService {

  public static final String BAILIAN_OSS_DIR = "/bailian/";
  @Autowired private Client client;
  @Autowired private AliBaiLianConfig aliBaiLianConfig;
  @Autowired private OssGateway ossGateway;

  @Override
  public List<BaiLianCategoryVO> getCategoryList(String pid) {
    try {
      ListCategoryRequest listCategoryRequest = new ListCategoryRequest();
      listCategoryRequest.setCategoryType("UNSTRUCTURED");
      listCategoryRequest.setMaxResults(200);
      listCategoryRequest.setParentCategoryId(pid);
      ListCategoryResponse listCategoryResponse =
          client.listCategory(aliBaiLianConfig.getWorkspaceId(), listCategoryRequest);
      if (listCategoryResponse.statusCode != 200) {
        throw AiAgentException.throwException(
            AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, listCategoryResponse.getBody().getMessage());
      }
      ListCategoryResponseBody responseBody = listCategoryResponse.getBody();
      if (!responseBody.getSuccess()) {
        throw AiAgentException.throwException(
            AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, responseBody.getMessage());
      }
      return listCategoryResponse.getBody().getData().getCategoryList().stream()
          .map(
              category -> {
                BaiLianCategoryVO baiLianCategoryVO = new BaiLianCategoryVO();
                baiLianCategoryVO.setId(category.getCategoryId());
                baiLianCategoryVO.setPid(category.getParentCategoryId());
                baiLianCategoryVO.setName(category.getCategoryName());
                return baiLianCategoryVO;
              })
          .collect(Collectors.toList());
    } catch (AiAgentException tech) {
      throw tech;
    } catch (Exception e) {
      log.error("[调用接口失败] msg={}", e.getMessage());
      throw AiAgentException.throwException(AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, e.getMessage());
    }
  }

  private Cache<String, List<BaiLianCategoryVO>> categoryListCache =
      Caffeine.newBuilder().expireAfterWrite(Duration.ofMinutes(5)).maximumSize(5).build();

  private List<BaiLianCategoryVO> getCategoryListCache(String pid) {
    return categoryListCache.get(pid, this::getCategoryList);
  }

  @Override
  public BaiLianCategoryVO addCategory(String pid, String name) {
    try {
      AddCategoryRequest addCategoryRequest = new AddCategoryRequest();
      addCategoryRequest.setCategoryName(name);
      addCategoryRequest.setCategoryType("UNSTRUCTURED");
      addCategoryRequest.setParentCategoryId(pid);
      AddCategoryResponse addCategoryResponse =
          client.addCategory(aliBaiLianConfig.getWorkspaceId(), addCategoryRequest);
      if (addCategoryResponse.statusCode != 200) {
        throw AiAgentException.throwException(
            AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, addCategoryResponse.getBody().getMessage());
      }
      AddCategoryResponseBody responseBody = addCategoryResponse.getBody();
      if (!responseBody.getSuccess()) {
        throw AiAgentException.throwException(
            AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, responseBody.getMessage());
      }
      BaiLianCategoryVO baiLianCategoryVO = new BaiLianCategoryVO();
      baiLianCategoryVO.setId(responseBody.getData().getCategoryId());
      baiLianCategoryVO.setPid(pid);
      baiLianCategoryVO.setName(responseBody.getData().getCategoryName());
      return baiLianCategoryVO;
    } catch (AiAgentException tech) {
      throw tech;
    } catch (Exception e) {
      log.error("[调用接口失败] msg={}", e.getMessage());
      throw AiAgentException.throwException(AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, e.getMessage());
    } finally {
      categoryListCache.invalidateAll();
    }
  }

  @Override
  public String getCategoryId(ModuleEnum moduleEnum) {
    String moduleName = moduleEnum.getValue();
    String pid = aliBaiLianConfig.getParentCategoryId();
    Map<String, String> categoryMap =
            getCategoryListCache(pid).stream()
                    .collect(Collectors.toMap(BaiLianCategoryVO::getName, BaiLianCategoryVO::getId));
    if (categoryMap.containsKey(moduleName)) {
      return categoryMap.get(moduleName);
    }
    return addCategory(pid, moduleName).getId();
  }

  @Override
  public FileInfoVO getFileInfo(String fileId) {
    try {
      DescribeFileResponse describeFileResponse =
          client.describeFile(aliBaiLianConfig.getWorkspaceId(), fileId);
      DescribeFileResponseBody.DescribeFileResponseBodyData data =
          describeFileResponse.getBody().getData();
      FileInfoVO fIleInfoVO = new FileInfoVO();
      fIleInfoVO.setFileId(data.getFileId());
      fIleInfoVO.setFileName(data.getFileName());
      fIleInfoVO.setStatus(data.getStatus());
      return fIleInfoVO;
    } catch (RetryException retryException) {
      throw retryException;
    } catch (Exception e) {
      log.error("[获取详情失败] msg={}", e.getMessage());
    }
    return null;
  }

  @Override
  public UploadFileResult uploadOssFile(
      ModuleEnum moduleEnum, String ossUrl, String filename, List<String> tags) {
    try {
      String signUrl = ossGateway.generatePresignedUrl(ossUrl);
      HttpResponse<byte[]> downloadResponse = Unirest.get(signUrl).asBytes();
      if (downloadResponse.getStatus() != 200) {
        throw AiAgentException.throwException(AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, "文件下载失败");
      }
      byte[] fileBytes = downloadResponse.getBody();
      String finalFilename = StringUtils.getFilename(ossUrl);
      if (StrUtil.isNotEmpty(filename)) {
        finalFilename = filename;
      }
      return uploadFileWithRetry(moduleEnum, fileBytes, finalFilename, tags, false);
    } catch (AiAgentException AiAgentException) {
      throw AiAgentException;
    } catch (Exception e) {
      log.error("[调用接口失败] msg={}", e.getMessage());
      throw AiAgentException.throwException(AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, e.getMessage());
    }
  }

  @Override
  public UploadFileResult uploadOssFile(ModuleEnum moduleEnum, String ossUrl, String filename) {
    return uploadOssFile(moduleEnum, ossUrl, filename, Collections.emptyList());
  }

  private UploadFileResult uploadFileToBaiLian(
      ModuleEnum moduleEnum, byte[] fileBytes, String fileName, List<String> tags) {
      String categoryId = getCategoryId(moduleEnum);
      return uploadFile(categoryId, fileBytes, fileName, tags);
  }

  @Override
  public UploadFileResult uploadFile(String baiLianCategoryId, byte[] fileBytes, String fileName, List<String> tags) {
    try {
      String md5Hex = DigestUtil.md5Hex(fileBytes);
      ApplyFileUploadLeaseRequest applyFileUploadLeaseRequest = new ApplyFileUploadLeaseRequest();
      applyFileUploadLeaseRequest.setFileName(fileName);
      applyFileUploadLeaseRequest.setMd5(md5Hex);
      applyFileUploadLeaseRequest.setSizeInBytes(String.valueOf(fileBytes.length));
      ApplyFileUploadLeaseResponse applyFileUploadLeaseResponse =
              client.applyFileUploadLease(
                      baiLianCategoryId, aliBaiLianConfig.getWorkspaceId(), applyFileUploadLeaseRequest);
      if (applyFileUploadLeaseResponse.statusCode != 200) {
        throw AiAgentException.throwException(
                AiAgentErrorCodeEnum.THIRD_SERVER_ERROR,
                applyFileUploadLeaseResponse.getBody().getMessage());
      }
      ApplyFileUploadLeaseResponseBody responseBody = applyFileUploadLeaseResponse.getBody();
      if (!responseBody.getSuccess()) {
        throw AiAgentException.throwException(
                AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, responseBody.getMessage());
      }
      String signUrl = responseBody.getData().getParam().getUrl();
      String leaseId = responseBody.getData().getFileUploadLeaseId();
      Map<String, String> headers =
              (Map<String, String>) responseBody.getData().getParam().getHeaders();
      log.info(
              "[文件上传到百炼] 获取预签名成功 baiLianCategoryId={}, fileName={}， signUrl={}",
              baiLianCategoryId,
              fileName,
              signUrl);
      // 上传文件到百炼
      HttpResponse<String> httpResponse =
              Unirest.put(signUrl).body(fileBytes).headers(headers).asString();
      log.info(
              "[文件上传到百炼] 文件上传到临时空间成功  categoryId={}, fileName={}， res={}",
              baiLianCategoryId,
              fileName,
              httpResponse.getBody());
      AddFileRequest addFileRequest = new AddFileRequest();
      addFileRequest.setCategoryId(baiLianCategoryId);
      addFileRequest.setCategoryType("UNSTRUCTURED");
      addFileRequest.setLeaseId(leaseId);
      addFileRequest.setParser("DASHSCOPE_DOCMIND");
      addFileRequest.setTags(tags);
      AddFileResponse addFileResponse =
              client.addFile(aliBaiLianConfig.getWorkspaceId(), addFileRequest);
      if (addFileResponse.statusCode != 200) {
        throw AiAgentException.throwException(
                AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, addFileResponse.getBody().getMessage());
      }
      AddFileResponseBody addFileResponseBody = addFileResponse.getBody();
      if (!"true".equals(addFileResponseBody.getSuccess())) {
        throw AiAgentException.throwException(
                AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, addFileResponseBody.getMessage());
      }
      log.info(
              "[文件上传到百炼] 文件添加成功categoryId={}, fileName={}",
              baiLianCategoryId,
              fileName);
      final UploadFileResult uploadFileResult = new UploadFileResult();
      uploadFileResult.setCategoryId(baiLianCategoryId);
      uploadFileResult.setFileId(addFileResponseBody.getData().getFileId());
      return uploadFileResult;
    } catch (AiAgentException AiAgentException) {
      throw AiAgentException;
    } catch (TeaException tea) {
      if ("Throttling.Api".equals(tea.getCode())
              && tea.getMessage().contains("Request was denied due to user flow control")) {
        throw new RetryException("need to retry");
      }
      throw AiAgentException.throwException(AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, tea.getMessage());
    } catch (Exception e) {
      log.error("[调用接口失败] msg={}", e.getMessage());
      throw AiAgentException.throwException(AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, e.getMessage());
    }
  }

  @Override
  public UploadFileResult uploadFile(ModuleEnum moduleEnum, byte[] fileBytes, String fileName) {
    return uploadFile(moduleEnum, fileBytes, fileName, Collections.emptyList());
  }

  @Override
  public UploadFileResult uploadFile(
      ModuleEnum moduleEnum, byte[] fileBytes, String fileName, List<String> tags) {
    return uploadFileWithRetry(moduleEnum, fileBytes, fileName, tags, true);
  }

  private UploadFileResult uploadFileWithRetry(
      ModuleEnum moduleEnum,
      byte[] fileBytes,
      String fileName,
      List<String> tags,
      boolean uploadOss) {
    final RetryTemplate retryTemplate = getRetryTemplate();
    return retryTemplate.execute(
        retryContext -> {
          log.info(
              "[文件上传到百炼] 当前第{}次上传。moduleEnum={}, fileName={}, tags={}, ",
              retryContext.getRetryCount(),
              moduleEnum,
              fileName,
              tags);
          final UploadFileResult uploadFileResult =
              uploadFileToBaiLian(moduleEnum, fileBytes, fileName, tags);
          if (uploadOss) {
            final String ossUrl =
                ossGateway.put(BAILIAN_OSS_DIR + fileName, new ByteArrayInputStream(fileBytes));
            uploadFileResult.setOssUrl(ossUrl);
          }
          return uploadFileResult;
        });
  }

  private RetryTemplate getRetryTemplate() {
    return new RetryTemplateBuilder()
        .retryOn(RetryException.class)
        .fixedBackoff(1000)
        .maxAttempts(3)
        .build();
  }

  @Override
  @SneakyThrows
  public void deleteFile(String fileId) {
    try {
      client.deleteFile(fileId, aliBaiLianConfig.getWorkspaceId());
    } catch (Exception e) {
      if (e.getMessage().contains("Cant find out file for your file_id parameter.")) {
        return;
      }
      throw e;
    }
  }

  @Override
  public void updateFileTags(String fileId, List<String> tags) {
    if (CollUtil.isEmpty(tags)) {
      return;
    }
    try {
      UpdateFileTagRequest updateFileTagRequest = new UpdateFileTagRequest();
      updateFileTagRequest.setTags(
          tags.stream().filter(StrUtil::isNotBlank).collect(Collectors.toList()));
      client.updateFileTag(aliBaiLianConfig.getWorkspaceId(), fileId, updateFileTagRequest);
    } catch (Exception e) {
      log.info("[更新文件标签失败] fileId={}, tags={}, msg={}", fileId, tags, e.getMessage());
    }
  }

  @Override
  public List<BaiLianIndexVO> getIndexList(String indexName)  {
    ListIndicesRequest listIndicesRequest = new ListIndicesRequest();
    listIndicesRequest.setIndexName(indexName);
    listIndicesRequest.setPageNumber("1");
    listIndicesRequest.setPageSize("100");
    try {
      ListIndicesResponse listIndicesResponse =
          client.listIndices(aliBaiLianConfig.getWorkspaceId(), listIndicesRequest);

      return listIndicesResponse.getBody().getData().getIndices().stream()
          .map(
              index -> {
                BaiLianIndexVO baiLianIndexVO = new BaiLianIndexVO();
                baiLianIndexVO.setId(index.getId());
                baiLianIndexVO.setName(index.getName());
                baiLianIndexVO.setDesc(index.getDescription());
                return baiLianIndexVO;
              })
          .collect(Collectors.toList());
    } catch (Exception exception) {
      if (exception instanceof RetryException) {
        throw (RetryException) exception;
      }
      if (exception instanceof AiAgentException) {
        throw (AiAgentException) exception;
      }
      throw AiAgentException.throwException(
              AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, exception.getMessage());
    }
  }

  @Override
  public List<RetrieveVO> retrieve(RetrieveQuery retrieveQuery) {
    RetrieveRequest retrieveRequest = new RetrieveRequest();
    retrieveRequest.setIndexId(retrieveQuery.getIndexId());
    retrieveRequest.setQuery(retrieveQuery.getKeyword());
    retrieveRequest.setEnableReranking(retrieveQuery.getEnableReRank());
    retrieveRequest.setRerankTopN(retrieveQuery.getSize());
    retrieveRequest.setRerankMinScore(retrieveQuery.getReRankMinScore().floatValue());
    retrieveRequest.setSaveRetrieverHistory(false);
    retrieveRequest.setDenseSimilarityTopK(retrieveQuery.getSize());
    retrieveRequest.setSparseSimilarityTopK(retrieveQuery.getSize());
    retrieveRequest.setSearchFilters(
        Lists.newArrayList(Maps.of("is_displayed_chunk_content", "true")));
    try {
      RetrieveResponse retrieveResponse =
          client.retrieve(aliBaiLianConfig.getWorkspaceId(), retrieveRequest);
      return retrieveResponse.getBody().getData().getNodes().stream()
          .map(
              node -> {
                LinkedTreeMap<String, Object> metadataMap =
                    (LinkedTreeMap<String, Object>) node.getMetadata();
                RetrieveVO retrieveVO = new RetrieveVO();
                retrieveVO.setId(String.valueOf(metadataMap.get("_id")));
                retrieveVO.setTitle(String.valueOf(metadataMap.get("title")));
                retrieveVO.setContent(String.valueOf(metadataMap.get("content")));
                retrieveVO.setDocId(String.valueOf(metadataMap.get("doc_id")));
                retrieveVO.setDocName(String.valueOf(metadataMap.get("doc_name")));
                retrieveVO.setText(node.getText());
                retrieveVO.setScore(node.getScore());
                return retrieveVO;
              })
          .collect(Collectors.toList());
    } catch (Exception exception) {
      if (exception instanceof RetryException) {
        throw (RetryException) exception;
      }
      if (exception instanceof AiAgentException) {
        throw (AiAgentException) exception;
      }
      throw AiAgentException.throwException(
          AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, exception.getMessage());
    }
  }

  @Override
  public void syncCategoryToIndex(List<String> categoryIds, String indexId) throws Exception {
    SubmitIndexAddDocumentsJobRequest submitIndexAddDocumentsJobRequest =
        new SubmitIndexAddDocumentsJobRequest();
    submitIndexAddDocumentsJobRequest.setCategoryIds(categoryIds);
    submitIndexAddDocumentsJobRequest.setDocumentIds(Lists.newArrayList());
    submitIndexAddDocumentsJobRequest.setIndexId(indexId);
    submitIndexAddDocumentsJobRequest.setSourceType("DATA_CENTER_CATEGORY");
    client.submitIndexAddDocumentsJob(
        aliBaiLianConfig.getWorkspaceId(), submitIndexAddDocumentsJobRequest);
  }

  @Override
  public String syncFileIdToIndex(List<String> fileIds, String indexId) {
    SubmitIndexAddDocumentsJobRequest submitIndexAddDocumentsJobRequest =
        new SubmitIndexAddDocumentsJobRequest();
    submitIndexAddDocumentsJobRequest.setCategoryIds(Collections.emptyList());
    submitIndexAddDocumentsJobRequest.setDocumentIds(fileIds);
    submitIndexAddDocumentsJobRequest.setIndexId(indexId);
    submitIndexAddDocumentsJobRequest.setSourceType("DATA_CENTER_FILE");
    log.debug("[百炼] 同步文件到索引 fileIds={}, indexId={}", fileIds, indexId);
    try {
      final SubmitIndexAddDocumentsJobResponse submitIndexAddDocumentsJobResponse =
          client.submitIndexAddDocumentsJob(
              aliBaiLianConfig.getWorkspaceId(), submitIndexAddDocumentsJobRequest);
      final SubmitIndexAddDocumentsJobResponseBody body =
          submitIndexAddDocumentsJobResponse.getBody();
      if (!"200".equals(body.getStatus())) {
        throw AiAgentException.construct(AiAgentErrorCodeEnum.THIRD_SERVER_ERROR)
            .setMsg("同步文件到索引失败 code:{}, msg:{}, requestId:{}")
            .withArgs(body.getCode(), body.getMessage(), body.getRequestId())
            .build();
      }
      return body.getData().getId();
    } catch (Exception e) {
      if (e instanceof RetryException) {
        throw (RetryException) e;
      }
      if (e instanceof AiAgentException) {
        throw (AiAgentException) e;
      }
      throw AiAgentException.construct(AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, e).build();
    }
  }

  @Override
  public void deleteIndexDoc(String indexId, List<String> docIds) {
    try {
      log.info("[百炼] 删除索引文档 indexId={}, docIds={}", indexId, docIds);
      DeleteIndexDocumentRequest deleteIndexDocumentRequest = new DeleteIndexDocumentRequest();
      deleteIndexDocumentRequest.setDocumentIds(docIds);
      deleteIndexDocumentRequest.setIndexId(indexId);
      client.deleteIndexDocument(aliBaiLianConfig.getWorkspaceId(), deleteIndexDocumentRequest);
    } catch (Exception exception) {
      if (exception instanceof RetryException) {
        throw (RetryException) exception;
      }
      if (exception instanceof AiAgentException) {
        throw (AiAgentException) exception;
      }
      throw AiAgentException.throwException(
          AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, exception.getMessage());
    }
  }

//  @Override
//  public ApplicationOutput chat(String appId, String apiKey, String prompt) {
//    ApplicationParam param =
//        ApplicationParam.builder().apiKey(apiKey).appId(appId).prompt(prompt).build();
//    Application application = new Application();
//    try {
//      ApplicationResult result = application.call(param);
//      return result.getOutput();
//    } catch (Exception AiAgentException) {
//      throw AiAgentException.throwException(
//          AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, AiAgentException.getMessage());
//    }
//  }

  @Override
  public List<BaiLianIndexChunksVO> indexChunks(String indexId, String fileId) {
    return PageUtils.aggregationPages(
        page -> {
          Integer pageSize = 100;
          ListChunksRequest listChunksRequest = new ListChunksRequest();
          listChunksRequest.setFields(Lists.newArrayList());
          listChunksRequest.setFileId(fileId);
          listChunksRequest.setIndexId(indexId);
          listChunksRequest.setPageNum(page.intValue());
          listChunksRequest.setPageSize(pageSize);
          try {
            ListChunksResponse listChunksResponse =
                client.listChunks(aliBaiLianConfig.getWorkspaceId(), listChunksRequest);
            ListChunksResponseBody.ListChunksResponseBodyData data =
                listChunksResponse.getBody().getData();
            if (data.getTotal() == 0) {
              return PageUtils.PageResponse.of(0L, new ArrayList<BaiLianIndexChunksVO>(), pageSize);
            }
            List<BaiLianIndexChunksVO> chunksVOS =
                data.getNodes().stream()
                    .map(
                        node -> {
                          LinkedTreeMap<String, Object> metadataMap =
                              (LinkedTreeMap<String, Object>) node.getMetadata();
                          BaiLianIndexChunksVO baiLianIndexChunksVO = new BaiLianIndexChunksVO();
                          baiLianIndexChunksVO.setTitle(String.valueOf(metadataMap.get("title")));
                          baiLianIndexChunksVO.setChunkId(
                              Long.parseLong((String) metadataMap.get("_chunk_id")));
                          baiLianIndexChunksVO.setContent(
                              String.valueOf(metadataMap.get("content")));
                          return baiLianIndexChunksVO;
                        })
                    .collect(Collectors.toList());
            return PageUtils.PageResponse.of(data.getTotal(), chunksVOS, pageSize);
          } catch (Exception e) {
            if (e instanceof RetryException) {
              throw (RetryException) e;
            }
            if (e instanceof AiAgentException) {
              throw (AiAgentException) e;
            }
            throw AiAgentException.throwException(
                AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, e.getMessage());
          }
        });
  }

  @Override
  public void foreachFiles(
      String categoryId,
      Consumer<List<ListFileResponseBodyDataFileList>> consumer,
      int pageSize,
      int pageLimit) {
    String nextToken = null;
    int index = 0;
    while (true) {
      index++;
      try {
        final ListFileRequest request = new ListFileRequest();
        request.setCategoryId(categoryId);
        request.setMaxResults(pageSize);
        request.setNextToken(nextToken);
        final ListFileResponse listFileResponse =
            client.listFile(aliBaiLianConfig.getWorkspaceId(), request);
        final ListFileResponseBody body = listFileResponse.getBody();
        final ListFileResponseBodyData data = body.getData();
        final List<ListFileResponseBodyDataFileList> fileList = data.getFileList();
        consumer.accept(fileList);
        nextToken = data.getNextToken();
        if (StrUtil.isBlank(nextToken)) {
          break;
        }
        if (index >= pageLimit) {
          break;
        }
      } catch (Exception e) {
        log.error("[百炼-遍历文件]查询异常:{}", e.getMessage(), e);
      }
    }
  }

  @Override
  public List<BaiLianIndexDocVO> indexDocList(String indexId) {
    return PageUtils.aggregationPages(page -> requestIndexDocuments(indexId, page, 100));
  }

  private PageUtils.PageResponse<BaiLianIndexDocVO> requestIndexDocuments(
      String indexId, Long pageIndex, Integer pageSize) {
    Assert.isTrue(pageSize <= 100, "请求百炼知识库文档分页SIZE不能大于100");
    ListIndexDocumentsRequest listIndexDocumentsRequest = new ListIndexDocumentsRequest();
    listIndexDocumentsRequest.setIndexId(indexId);
    listIndexDocumentsRequest.setPageNumber(pageIndex.intValue());
    listIndexDocumentsRequest.setPageSize(pageSize);
    try {
      ListIndexDocumentsResponse listIndexDocumentsResponse =
          client.listIndexDocuments(aliBaiLianConfig.getWorkspaceId(), listIndexDocumentsRequest);
      ListIndexDocumentsResponseBody.ListIndexDocumentsResponseBodyData data =
          listIndexDocumentsResponse.getBody().getData();
      if (data.getTotalCount() == 0) {
        return PageUtils.PageResponse.of(0L, new ArrayList<>(), pageSize);
      }
      List<BaiLianIndexDocVO> indexDocVOS =
          data.getDocuments().stream()
              .map(doc -> toBaiLianIndexDocVO(indexId, doc))
              .collect(Collectors.toList());
      return PageUtils.PageResponse.of(data.getTotalCount(), indexDocVOS, pageSize);
    } catch (Exception e) {
      if (e instanceof RetryException) {
        throw (RetryException) e;
      }
      if (e instanceof AiAgentException) {
        throw (AiAgentException) e;
      }
      log.error("[百炼-查询索引文档]查询异常:{}", e.getMessage(), e);
      throw AiAgentException.throwException(
          AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, e.getMessage());
    }
  }

  private static @NotNull BaiLianIndexDocVO toBaiLianIndexDocVO(
      String indexId, ListIndexDocumentsResponseBodyDataDocuments doc) {
    BaiLianIndexDocVO baiLianIndexDocVO = new BaiLianIndexDocVO();
    baiLianIndexDocVO.setFileId(doc.getId());
    baiLianIndexDocVO.setName(doc.getName());
    baiLianIndexDocVO.setSize(doc.getSize());
    baiLianIndexDocVO.setDocumentType(doc.getDocumentType());
    baiLianIndexDocVO.setStatus(doc.getStatus());
    baiLianIndexDocVO.setCode(doc.getCode());
    baiLianIndexDocVO.setMessage(doc.getMessage());
    baiLianIndexDocVO.setIndexId(indexId);
    baiLianIndexDocVO.setSourceId(doc.getSourceId());
    return baiLianIndexDocVO;
  }

  @Override
  public void foreachIndexDocList(
      String indexId, Consumer<IPage<BaiLianIndexDocVO>> consumer, int pageSize) {
    int pageIndex = 0;
    while (true) {
      pageIndex++;
      PageUtils.PageResponse<BaiLianIndexDocVO> pageResponse =
          requestIndexDocuments(indexId, (long) pageIndex, pageSize);
      if (CollUtil.isEmpty(pageResponse.getData())) {
        break;
      }
      final Integer responsePageSize = pageResponse.getPageSize();
      final Page<BaiLianIndexDocVO> pageDTO =
          PageDTO.of(pageIndex, responsePageSize, pageResponse.getTotal());
      pageDTO.setRecords(pageResponse.getData());
      consumer.accept(pageDTO);
      if (pageIndex >= PageUtil.totalPage(pageResponse.getTotal(), responsePageSize)) {
        break;
      }
    }
  }

  @Override
  public String createMemory(String user) {
    final String description =
        StrUtil.format(
            "profile[{}]-appNam[{}]-user[{}]",
                SpringUtil.getActiveProfile(),
            aliBaiLianConfig.getAppName(),
            user);
    CreateMemoryRequest request = new CreateMemoryRequest();
    request.setDescription(description);
    CreateMemoryResponse memory = null;
    try {
      memory = client.createMemory(aliBaiLianConfig.getWorkspaceId(), request);
    } catch (Exception e) {
      log.error("创建长期记忆体调用异常.res:{}", JsonUtil.toJSONString(memory), e);
      throw AiAgentException.throwException(AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, "创建长期记忆体调用异常");
    }
    if (memory.statusCode != 200) {
      log.error("创建长期记忆体响应.res:{}", JsonUtil.toJSONString(memory));
      throw AiAgentException.throwException(AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, "创建长期记忆体响应");
    }
    return memory.getBody().getMemoryId();
  }

  @Override
  public List<ListMemoryNodesResponseBody.ListMemoryNodesResponseBodyMemoryNodes> getMemoryNodeList(
      String memoryId) {

    ListMemoryNodesRequest request = new ListMemoryNodesRequest();
    // 取值 <= 50
    request.setMaxResults(50);
    ListMemoryNodesResponse listMemoryNodesResponse = null;
    try {
      listMemoryNodesResponse =
          client.listMemoryNodes(aliBaiLianConfig.getWorkspaceId(), memoryId, request);
    } catch (Exception e) {
      log.error(
          "拉取记忆片段异常.mId:{},res:{}", memoryId, JsonUtil.toJSONString(listMemoryNodesResponse), e);
      throw AiAgentException.throwException(AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, "拉取记忆片段异常");
    }
    boolean b1 = listMemoryNodesResponse.getStatusCode() == 200;
    boolean b2 = CollUtil.isNotEmpty(listMemoryNodesResponse.getBody().getMemoryNodes());
    List<ListMemoryNodesResponseBody.ListMemoryNodesResponseBodyMemoryNodes> resList =
        new LinkedList<>(listMemoryNodesResponse.getBody().getMemoryNodes());

    while (b1 && b2) {
      // 当前云产品API请求速率暂未透出。先睡0.5秒
      try {
        TimeUnit.MILLISECONDS.sleep(500);
      } catch (InterruptedException e) {
        // ignore
      }

      final String nextToken = listMemoryNodesResponse.getBody().getNextToken();
      request.setNextToken(nextToken);
      try {
        listMemoryNodesResponse =
            client.listMemoryNodes(aliBaiLianConfig.getWorkspaceId(), memoryId, request);
      } catch (Exception e) {
        log.error(
            "拉取记忆片段异常.mId:{},nextToken:{},res:{}",
            memoryId,
            nextToken,
            JsonUtil.toJSONString(listMemoryNodesResponse),
            e);
        throw AiAgentException.throwException(AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, "拉取记忆片段异常");
      }
      b1 = listMemoryNodesResponse.getStatusCode() == 200;
      b2 = CollUtil.isNotEmpty(listMemoryNodesResponse.getBody().getMemoryNodes());
      resList.addAll(listMemoryNodesResponse.getBody().getMemoryNodes());
    }

    return resList;
  }

  @Override
  public void deleteMemory(Collection<String> memoryIds) {
    if (CollUtil.isEmpty(memoryIds)) return;

    for (String memoryId : memoryIds) {
      try {
        final DeleteMemoryResponse deleteMemoryResponse =
            client.deleteMemory(aliBaiLianConfig.getWorkspaceId(), memoryId);
        Assert.state(
            deleteMemoryResponse.getStatusCode() == 200,
            "删除用户长期记忆响应异常." + JsonUtil.toJSONString(deleteMemoryResponse));
      } catch (Exception e) {
        log.error("删除用户长期记忆异常,mId:{}", memoryId, e);
        throw AiAgentException.throwException(AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, "删除用户长期记忆异常");
      }
    }
  }

  @Override
  public void showMemory() {
    ListMemoriesRequest request = new ListMemoriesRequest();
    request.setMaxResults(50);
    //    request.setNextToken();
    final ListMemoriesResponse listMemoriesResponse;
    try {
      listMemoriesResponse = client.listMemories(aliBaiLianConfig.getWorkspaceId(), request);
      log.info("listMemoriesResponse:{}", JsonUtil.toJSONString(listMemoriesResponse));
    } catch (Exception e) {
      log.error("拉取长期记忆体异常", e);
      throw AiAgentException.throwException(AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, "拉取长期记忆体异常");
    }
  }

  @Override
  public String createIndex(String name) {
    List<BaiLianIndexVO> indexList = getIndexList(name);
    if (!indexList.isEmpty()) {
      return CollUtil.getFirst(indexList).getId();
    }
    String indexId = "";
    CreateIndexRequest request = new CreateIndexRequest();
    request.setName(name);
    request.setStructureType("unstructured"); // 非结构化
    request.setSourceType("DATA_CENTER_CATEGORY"); // 数据中心分类
    request.setCategoryIds(
        Arrays.asList("cate_a1809c73a4304a46b6ed47396de875b7_10175531")); // 知识库的类目ID列表，对应默认类目
    request.setSinkType("BUILT_IN"); // 内置的向量数据库
    try {
      CreateIndexResponse response = client.createIndex(aliBaiLianConfig.getWorkspaceId(), request);
      indexId = response.getBody().getData().getId();
    } catch (Exception e) {
      log.error("创建知识库索引失败", e);
      throw AiAgentException.throwException(AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, "创建知识库索引失败");
    }
    return indexId;
  }

  @Override
  public Boolean deleteIndex(String indexId) {
    DeleteIndexRequest request = new DeleteIndexRequest();
    request.setIndexId(indexId);
    try {
      DeleteIndexResponse response = client.deleteIndex(aliBaiLianConfig.getWorkspaceId(), request);
      return response.getBody().getSuccess();
    } catch (Exception e) {
      log.error("删除知识库索引失败", e);
      throw AiAgentException.throwException(AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, "删除知识库索引失败");
    }
  }

  @Override
  public Boolean SubmitIndexJob(String indexId) {
    SubmitIndexJobRequest request = new SubmitIndexJobRequest();
    request.setIndexId(indexId);
    try{
      SubmitIndexJobResponse response = client.submitIndexJob(aliBaiLianConfig.getWorkspaceId(),request);
      return response.getBody().getSuccess();
    }catch (Exception e){
      log.error("提交索引创建失败", e);
      throw AiAgentException.throwException(AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, "提交索引创建失败");
    }
  }

  @Override
  public void getIndexJobStatus(
      String indexId,
      String jobId,
      int pageSize,
      Consumer<GetIndexJobStatusResponseBodyData> consumer) {
    int pageNumber = 1;
    for (; ; ) {
      final GetIndexJobStatusRequest getIndexJobStatusRequest = new GetIndexJobStatusRequest();
      getIndexJobStatusRequest.setIndexId(indexId);
      getIndexJobStatusRequest.setJobId(jobId);
      getIndexJobStatusRequest.setPageNumber(pageNumber);
      getIndexJobStatusRequest.setPageSize(pageSize);
      try {
        final GetIndexJobStatusResponse indexJobStatus =
            client.getIndexJobStatus(aliBaiLianConfig.getWorkspaceId(), getIndexJobStatusRequest);
        final GetIndexJobStatusResponseBodyData data = indexJobStatus.getBody().getData();
        if (data == null || data.getDocuments() == null || data.getDocuments().isEmpty()) {
          break;
        }
        consumer.accept(data);
        pageNumber++;
      } catch (Exception e) {
        log.error("获取索引任务状态失败", e);
        throw AiAgentException.throwException(AiAgentErrorCodeEnum.THIRD_SERVER_ERROR, "获取索引任务状态失败");
      }
    }
  }
}
