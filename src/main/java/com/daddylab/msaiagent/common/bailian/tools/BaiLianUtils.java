package com.daddylab.msaiagent.common.bailian.tools;


import com.daddylab.msaiagent.common.markdown.MarkdownUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class BaiLianUtils {

  /**
   * 将对象转换为可读字符串(markdown格式)
   *
   * @param obj 对象
   * @return 可读字符串
   */
  public static String toStringReadability(Object obj) {
    return MarkdownUtils.toMarkdown(obj);
  }
  /**
   * 将对象转换为可读字符串(markdown格式)
   *
   * @param obj 对象
   * @param title 标题
   * @return 可读字符串
   */
  public static String toStringReadability(Object obj, String title) {
    return MarkdownUtils.toMarkdown(obj, title);
  }

}
