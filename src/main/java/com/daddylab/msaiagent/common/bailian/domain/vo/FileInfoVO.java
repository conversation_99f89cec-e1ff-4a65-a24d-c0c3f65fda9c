package com.daddylab.msaiagent.common.bailian.domain.vo;

import lombok.Data;

/**
 *
 * @className RetrieveVO
 * <AUTHOR>
 * @date 2025/3/6 13:42
 * @description: TODO 
 */
@Data
public class FileInfoVO {
    private String fileId;
    private String fileName;
    /**
     *  INIT: 待解析。
     * PARSING: 解析中。
     * PARSE_SUCCESS：解析完成。
     * PARSE_FAILED：解析失败。
     * SAFE_CHECKING: 安全检测中。
     * SAFE_CHECK_FAILED: 安全检测失败。
     * INDEX_BUILDING：索引构建中。
     * INDEX_BUILD_SUCCESS：索引构建成功。
     * INDEX_BUILDING_FAILED：索引构建失败。
     * INDEX_DELETED：文件索引已删除。
     */
    private String status;
    public Boolean isParseSuccess() {
        return "PARSE_SUCCESS".equals(status);
    }

    public Boolean isParseFailed() {
        return "PARSE_FAILED".equals(status);
    }

}
