package com.daddylab.msaiagent.common.log;

import cn.hutool.core.util.StrUtil;

import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2024/8/21
 */
public class ValueSummarySerializer implements Function<Object, String> {

    private final int charLimit;
    private final Function<Object, String> valueSerializer;

    public ValueSummarySerializer(int charLimit, Function<Object, String> valueSerializer) {
        this.charLimit = charLimit;
        this.valueSerializer = valueSerializer;
    }

    @Override
    public String apply(Object o) {
        String valueStr = valueSerializer.apply(o);
        if (valueStr == null) {
            return StrUtil.EMPTY;
        }
        final int valueLen = valueStr.length();
        if (valueLen <= charLimit) {
            return valueStr;
        }
        final int truncateLeft = charLimit / 2;
        final int truncateRight = charLimit - truncateLeft;
        return StrUtil.subWithLength(valueStr, 0, truncateLeft)
            + "(⋯"
            + (valueLen - charLimit)
            + ")"
            + StrUtil.subWithLength(valueStr, -1, truncateRight);
    }
}
