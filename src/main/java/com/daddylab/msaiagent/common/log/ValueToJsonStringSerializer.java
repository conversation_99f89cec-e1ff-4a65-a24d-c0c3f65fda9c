package com.daddylab.msaiagent.common.log;

import cn.hutool.core.io.FastStringWriter;
import com.daddylab.msaiagent.common.utils.JacksonUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.util.JsonGeneratorDelegate;
import lombok.SneakyThrows;

import java.io.IOException;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2024/9/5
 */
public class ValueToJsonStringSerializer implements Function<Object, String> {
  private final ValueSummarySerializer valueSummarySerializer;

  public ValueToJsonStringSerializer(int charLimit) {
    this.valueSummarySerializer = new ValueSummarySerializer(charLimit, Object::toString);
  }

  @Override
  @SneakyThrows
  public String apply(Object o) {
    final FastStringWriter writer = new FastStringWriter();
    final JsonGenerator jsonGenerator = JacksonUtil.mapper().createGenerator(writer);
    JacksonUtil.mapper()
        .writeValue(new GeneratorDelegate(jsonGenerator, valueSummarySerializer), o);
    return writer.toString();
  }

  static class GeneratorDelegate extends JsonGeneratorDelegate {
    private final ValueSummarySerializer valueSummarySerializer;

    public GeneratorDelegate(JsonGenerator d, ValueSummarySerializer valueSummarySerializer) {
      super(d);
      this.valueSummarySerializer = valueSummarySerializer;
    }

    @Override
    public void writeString(String text) throws IOException {
      super.writeString(this.valueSummarySerializer.apply(text));
    }
  }
}
