package com.daddylab.msaiagent.common.log;

import lombok.Value;
import org.jetbrains.annotations.NotNull;

import java.util.Collection;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2024/8/21
 */
@Value
public class LogVarImpl implements LogVar {

    Object value;
    String name;
    Function<Object, String> valueSerializer;
    Function<Collection<?>, String> collectionSerializer;

    @Override
    public boolean isNamed() {
        return name != null && !name.isEmpty();
    }

    private String serialize(Object value) {
        if (value instanceof Collection) {
            return collectionSerializer.apply((Collection<?>) value);
        }
        return valueSerializer.apply(value);
    }

    @Override
    public String toString() {
        if (!name.isEmpty()) {
            return name + "=" + serialize(value);
        } else {
            return serialize(value);
        }
    }

    public static LogVarBuilder builder(Object value) {
        return new LogVarBuilder(value);
    }

    public static final class LogVarBuilder implements LogVar {

        public static final int CHAR_LIMIT = 256;
        public static final int COLLECTION_LIMIT = 8;
        private Object value;
        private String name = "";
        private Function<Object, String> valueSerializer =
            wrapSummarySerializer(ValueToStringSerializer.INSTANCE, CHAR_LIMIT);
        private Function<Collection<?>, String> collectionSerializer =
            new CollectionSummarySerializer(COLLECTION_LIMIT, valueSerializer);

        private LogVarBuilder(Object value) {
            this.value = value;
        }

        public LogVarBuilder value(Object value) {
            this.value = value;
            return this;
        }

        public LogVarBuilder name(String name) {
            this.name = name;
            return this;
        }

        private LogVarBuilder valueSerializer(
            Function<Object, String> valueSerializer, int collectionLimit) {
            this.valueSerializer = valueSerializer;
            this.collectionSerializer =
                new CollectionSummarySerializer(collectionLimit, this.valueSerializer);
            return this;
        }

        @NotNull
        private ValueSummarySerializer wrapSummarySerializer(
            Function<Object, String> valueSerializer, int charLimit) {
            return new ValueSummarySerializer(charLimit, valueSerializer);
        }

        public LogVarBuilder valueSerializer(Function<Object, String> valueSerializer) {
            return valueSerializer(
                wrapSummarySerializer(valueSerializer, CHAR_LIMIT), COLLECTION_LIMIT);
        }

        public LogVarBuilder limit(int limit) {
            return valueSerializer(wrapSummarySerializer(ValueToStringSerializer.INSTANCE, limit),
                COLLECTION_LIMIT);
        }

        public LogVarBuilder json() {
            return valueSerializer(new ValueToJsonStringSerializer(CHAR_LIMIT), COLLECTION_LIMIT);
        }

        public LogVarBuilder json(int charLimit) {
            return valueSerializer(new ValueToJsonStringSerializer(charLimit), COLLECTION_LIMIT);
        }

        public LogVarBuilder collectionSerializer(
            Function<Collection<?>, String> collectionSerializer) {
            this.collectionSerializer = collectionSerializer;
            return this;
        }

        public LogVarBuilder collectionSerializer(int limit) {
            this.collectionSerializer = new CollectionSummarySerializer(limit,
                this.valueSerializer);
            return this;
        }

        private LogVarImpl instance;

        public LogVarImpl build() {
            if (instance != null) {
                return instance;
            }
            this.instance = new LogVarImpl(value, name, valueSerializer, collectionSerializer);
            return instance;
        }

        @Override
        public Object getValue() {
            return build().getValue();
        }

        @Override
        public String getName() {
            return build().getName();
        }

        @Override
        public boolean isNamed() {
            return build().isNamed();
        }

        @Override
        public String toString() {
            return build().toString();
        }
    }
}
