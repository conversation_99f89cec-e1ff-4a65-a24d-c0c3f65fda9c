package com.daddylab.msaiagent.common.log;

import java.util.Arrays;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2024/8/21
 */
public class ValueToStringSerializer implements Function<Object, String> {
  public static final ValueToStringSerializer INSTANCE = new ValueToStringSerializer();

  @Override
  public String apply(Object element) {
    if (element instanceof Object[]) return Arrays.toString((Object[]) element);
    else if (element instanceof byte[]) return Arrays.toString((byte[]) element);
    else if (element instanceof short[]) return Arrays.toString((short[]) element);
    else if (element instanceof int[]) return Arrays.toString((int[]) element);
    else if (element instanceof long[]) return Arrays.toString((long[]) element);
    else if (element instanceof char[]) return Arrays.toString((char[]) element);
    else if (element instanceof float[]) return Arrays.toString((float[]) element);
    else if (element instanceof double[]) return Arrays.toString((double[]) element);
    else if (element != null) return element.toString();
    return null;
  }
}
