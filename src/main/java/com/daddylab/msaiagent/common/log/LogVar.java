package com.daddylab.msaiagent.common.log;

/**
 * <AUTHOR>
 * @since 2024/8/21
 */
public interface LogVar {
  Object getValue();

  String getName();

  boolean isNamed();

  @Override
  String toString();

  static LogVarImpl.LogVarBuilder unnamed(Object value) {
    return LogVarImpl.builder(value);
  }

  static LogVarImpl.LogVarBuilder named(String name, Object value) {
    return LogVarImpl.builder(value).name(name);
  }
}
