package com.daddylab.msaiagent.common.utils;

import cn.hutool.core.util.PageUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 *
 * @className PageUtils
 * <AUTHOR>
 * @date 2025/3/13 17:16
 * @description: TODO 
 */
public class PageUtils {

    /**
     * 获取所有页的数据
     *
     * @param pageResponseHandler java.util.function.Function<java.lang.Long
     * @return java.util.List<T>
     * <AUTHOR>
     * @date 2025/3/13 17:29
     */
    public static <T> List<T> aggregationPages(Function<Long, PageResponse<T>> pageResponseHandler) {
        long totalPage = 0;
        long page = 1;
        boolean isFirst = true;
        List<T> dataList = new ArrayList<>();
        do {
            PageResponse<T> pageResponse = pageResponseHandler.apply(page);
            if (isFirst) {
                Long total = pageResponse.getTotal();
                Integer pageSize = pageResponse.getPageSize();
                totalPage = PageUtil.totalPage(total, pageSize);
                isFirst = false;
            }
            dataList.addAll(pageResponse.getData());
            page++;
        } while (page <= totalPage);
        return dataList;
    }

    public static <T> void batchCallback(Function<Long, PageResponse<T>> pageResponseHandler, Consumer<List<T>> consumer) {
        long totalPage = 0;
        long page = 1;
        boolean isFirst = true;
        do {
            PageResponse<T> pageResponse = pageResponseHandler.apply(page);
            if (isFirst) {
                Long total = pageResponse.getTotal();
                Integer pageSize = pageResponse.getPageSize();
                totalPage = PageUtil.totalPage(total, pageSize);
                isFirst = false;
            }
            consumer.accept(pageResponse.getData());
            page++;
        } while (page <= totalPage);
    }



    @NoArgsConstructor
    @AllArgsConstructor(staticName = "of")
    @Data
    public static class PageResponse<T> {
        private Long total;
        private List<T> data;
        private Integer pageSize;
    }
}
