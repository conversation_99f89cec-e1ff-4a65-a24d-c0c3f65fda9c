package com.daddylab.msaiagent.common.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.codec.TypedJsonJacksonCodec;
import org.springframework.util.Assert;

import java.lang.reflect.Type;
import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

@Slf4j
public class JacksonUtil {
  private static final JsonMapper MAPPER;

  static {
    final JsonMapper.Builder mapperBuild = JsonMapper.builder();

    // 注册JSR310（LocalDateTime）
    mapperBuild.addModule(new JavaTimeModule());

    // 定义不要将日期对象写为时间戳
    mapperBuild.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);

    // 忽略未定义字段
    mapperBuild.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    // 如果是空对象的时候,不抛异常
    mapperBuild.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);

    // json转bean时忽略大小写
    mapperBuild.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true);

    // 空字符串允许解析为NULL
    mapperBuild.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);

    // 序列化时忽略值为null的属性
    mapperBuild.serializationInclusion(JsonInclude.Include.NON_NULL);

    MAPPER = mapperBuild.build();
  }

  @SneakyThrows
  public static String toJSONString(Object value) {
    if (value == null) return null;
    return MAPPER.writeValueAsString(value);
  }

  @SneakyThrows
  public static byte[] toJSONBytes(Object value) {
    if (value == null) return null;
    return MAPPER.writeValueAsBytes(value);
  }

  @SneakyThrows
  public static String toJSONPrettyString(Object value) {
    if (value == null) return null;
    return MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(value);
  }

  @SneakyThrows
  public static <T> T parseObject(String value, Type type) {
    if (value == null) return null;
    return MAPPER.readValue(value, MAPPER.getTypeFactory().constructType(type));
  }

  @SneakyThrows
  public static ArrayNode parseArray(String value) {
    if (value == null) return null;
    final JsonNode jsonNode = MAPPER.readTree(value);
    Assert.isTrue(jsonNode.isArray(), "jsonNode is not array");
    return (ArrayNode) jsonNode;
  }

  @SneakyThrows
  public static <T> T parseObject(byte[] value, Type type) {
    if (value == null) return null;
    return MAPPER.readValue(value, MAPPER.getTypeFactory().constructType(type));
  }

  @SneakyThrows
  public static <T> T parseObject(byte[] value, Class<T> type) {
    if (value == null) return null;
    return MAPPER.readValue(value, type);
  }

  @SneakyThrows
  public static <T> T parseObject(String value, Class<T> clazz) {
    if (value == null) return null;
    return MAPPER.readValue(value, clazz);
  }

  public static <T> T parseObjectToObject(Object o, Class<T> tClass){
    if (o == null) return null;
    return MAPPER.convertValue(o, tClass);
  }

  @SneakyThrows
  public static <T> List<T> parseObjectList(String value, Class<T> clazz) {
    if (value == null) return null;
    JavaType javaType = MAPPER.getTypeFactory().constructParametricType(List.class, clazz);
    return MAPPER.readValue(value, javaType);
  }

  @SneakyThrows
  public static <T> List<T> parseObjectList(byte[] value, Class<T> clazz) {
    if (value == null) return null;
    JavaType javaType = MAPPER.getTypeFactory().constructParametricType(List.class, clazz);
    return MAPPER.readValue(value, javaType);
  }

  @SneakyThrows
  public static List<ObjectNode> parseObjectList(String value) {
    if (value == null) return null;
    JsonNode jsonNode = MAPPER.readTree(value);
    Assert.isTrue(jsonNode.isArray(), "jsonNode is not array");
    ArrayList<ObjectNode> objectNodes = new ArrayList<>();
    for (JsonNode node : jsonNode) {
      objectNodes.add((ObjectNode) node);
    }
    return objectNodes;
  }

  @SneakyThrows
  public static <T> T parseObject(String value, TypeReference<T> typeReference) {
    if (value == null) return null;
    return MAPPER.readValue(value, typeReference);
  }

  @SneakyThrows
  public static Map<String, Object> parseMap(String value) {
    if (value == null) return null;
    return MAPPER.readValue(value, new TypeReference<HashMap<String, Object>>() {});
  }

  @SneakyThrows
  public static JsonNode parseObject(String value) {
    if (value == null) return null;
    return MAPPER.readTree(value);
  }

  @SneakyThrows
  public static JsonNode parseObject(byte[] data) {
    if (data.length == 0) return null;
    return MAPPER.readTree(data);
  }

  public static JsonNode mapRecursive(JsonNode node, Function<JsonNode, JsonNode> mappingFunction) {
    final JsonNodeFactory instance = JsonNodeFactory.instance;
    if (node.isObject()) {
      final ObjectNode jsonNodes = instance.objectNode();
      final Iterator<Entry<String, JsonNode>> fields = node.fields();
      while (fields.hasNext()) {
        final Entry<String, JsonNode> subNode = fields.next();
        jsonNodes.set(subNode.getKey(), mapRecursive(subNode.getValue(), mappingFunction));
      }
      return jsonNodes;
    } else if (node.isArray()) {
      final ArrayNode jsonNodes = instance.arrayNode();
      for (JsonNode subNode : node) {
        jsonNodes.add(mapRecursive(subNode, mappingFunction));
      }
      return jsonNodes;
    } else {
      return mappingFunction.apply(node);
    }
  }

  public static JsonMapper mapper() {
    return MAPPER;
  }

  public static <T> TypedJsonJacksonCodec typedJsonJacksonCodec(Class<T> clazz) {
    return new TypedJsonJacksonCodec(clazz, mapper());
  }

  public static <T> TypedJsonJacksonCodec typedJsonJacksonCodec(TypeReference<T> reference) {
    return new TypedJsonJacksonCodec(reference, mapper());
  }
}
