package com.daddylab.msaiagent.common.utils;

import com.lowagie.text.pdf.BaseFont;
import org.xhtmlrenderer.pdf.ITextRenderer;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

public class PdfUtil
{
    public static String cleanHtml(String htmlContent) {
        Document doc = Jsoup.parse(htmlContent);
        doc.outputSettings().syntax(Document.OutputSettings.Syntax.xml); // 强制转为 XML 格式
        doc.select("script").remove(); // 清理不安全标签
        return doc.html();
    }

    public static byte[] generatePdfFromHtml(String html) {
        String xhtmlContent = cleanHtml(html);
        xhtmlContent = "<html><head><meta charset=\"UTF-8\" /><style>body { font-family:SimSun; }</style></head><body>" + xhtmlContent + "</body></html>";        String baseUrl = PdfUtil.class.getClassLoader().getResource("fonts/").toString();
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            ITextRenderer renderer = new ITextRenderer();
            // 配置基础路径（用于加载 CSS/图片）
            renderer.setDocumentFromString(xhtmlContent, baseUrl);
            // 字体支持（中文字体）
            String fontPath = PdfUtil.class.getClassLoader().getResource("fonts/simsun.ttf").getFile();
            renderer.getFontResolver().addFont(
                    fontPath,
                    BaseFont.IDENTITY_H,
                    BaseFont.NOT_EMBEDDED
            );
            renderer.layout();
            renderer.createPDF(outputStream);
            return outputStream.toByteArray();
        } catch (Exception e) {
            throw new RuntimeException("PDF 生成失败", e);
        }
    }


    public static void main(String[] args) {
        // 测试生成 PDF到本地
        byte[] pdfBytes = generatePdfFromHtml("<h2>测试PDF生成</h2><p>这是一个测试内容。</p><br/><img src=\"https://res.ess.tencent.cn/cdn/tsign-developer-center/assets/images/模版发起流程图-b824f57f1388d961a173bca4edfe265f.png\" />");
        try (java.io.FileOutputStream fos = new java.io.FileOutputStream("test.pdf")) {
            fos.write(pdfBytes);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
