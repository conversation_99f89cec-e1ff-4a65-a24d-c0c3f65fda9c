package com.daddylab.msaiagent.common.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StopWatch;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/10/18
 */
@Slf4j
public class DownloadUtils {
  public static void download(RestTemplate restTemplate, String downloadUrl, String outputPath)
      throws IOException {
    final StopWatch stopWatch = new StopWatch();
    stopWatch.start();
    log.debug("[文件下载] 下载开始，文件URL：{}，下载到：{}", downloadUrl, outputPath);
    final ResponseEntity<Resource> videoResource =
        restTemplate.getForEntity(downloadUrl, Resource.class);
    FileUtil.mkParentDirs(outputPath);
    try (final InputStream inputStream =
            Objects.requireNonNull(videoResource.getBody()).getInputStream();
        final OutputStream fileOutputStream =
            Files.newOutputStream(
                Paths.get(outputPath), StandardOpenOption.CREATE, StandardOpenOption.WRITE); ) {
      IoUtil.copy(inputStream, fileOutputStream);
      stopWatch.stop();
      log.debug(
          "[文件下载] 下载完成，文件URL：{}，下载到：{}，耗时：{} ms",
          downloadUrl,
          outputPath,
          stopWatch.getTotalTimeMillis());
    }
  }
}
