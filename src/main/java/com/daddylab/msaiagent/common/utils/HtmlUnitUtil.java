package com.daddylab.msaiagent.common.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.gargoylesoftware.htmlunit.*;
import com.gargoylesoftware.htmlunit.html.HtmlPage;
import com.gargoylesoftware.htmlunit.util.Cookie;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.URI;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Class  HtmlUtils
 *
 * @Date 2022/5/19上午10:16
 * <AUTHOR>
 */
@Slf4j
public class HtmlUnitUtil {
    /**
     * cookie的分隔符
     */
    public static String COOKIE_DELIMITER = "; ";
    public static String COOKIE_EQ_DELIMITER = "=";

    public static String COOKIE_HEADER = "Cookie";

    /**
     * 提取html的url
     *
     * @param url
     * @return
     */
    public static Set<String> parseHtmlFileUrl(String url) {

        return Collections.emptySet();
    }

    /**
     * 爬取html页面，并执行页面js脚本
     *
     * @param httpUrl 网页地址
     * @return html
     */
    public static HtmlPage crawlingHtml(String httpUrl) {
        try (WebClient webClient = new WebClient(BrowserVersion.CHROME)) {
            CookieManager cookieManager = webClient.getCookieManager();
            cookieManager.setCookiesEnabled(true);
            return setting(httpUrl, webClient, cookieManager, Maps.newHashMap());
        } catch (Throwable e) {
            log.info("[抓取页面数据异常]", e);
        }
        return null;
    }

    /**
     * 爬取html页面，并执行页面js脚本
     *
     * @param httpUrl 网页地址
     * @return html
     */
    public static HtmlPage crawlingHtml(String httpUrl, Map<String, String> headers) {
        try (WebClient webClient = new WebClient(BrowserVersion.CHROME)) {
            CookieManager cookieManager = webClient.getCookieManager();
            cookieManager.setCookiesEnabled(true);
            return setting(httpUrl, webClient, cookieManager, headers);
        } catch (Throwable e) {
            log.info("[抓取页面数据异常]", e);
        }
        return null;
    }

    /**
     * 爬取html页面，并执行页面js脚本
     *
     * @param httpUrl 网页地址
     * @return html
     */
    public static String crawlingHtml(String httpUrl, String cookies) {
        return crawlingHtml(httpUrl, cookies, Maps.newHashMap());
    }



    public static String crawlingRedBookHtml(String detailUrl, String cookies) {
        try (WebClient webClient = new WebClient(BrowserVersion.CHROME)) {
            CookieManager cookieManager = webClient.getCookieManager();
            cookieManager.setCookiesEnabled(true);

            List<String> blacklist = Arrays.asList("vendor-dynamic", "library-vue", "vendor");

            webClient.setWebConnection(new HttpWebConnection(webClient) {
                @Override
                public WebResponse getResponse(WebRequest request) throws IOException {
                    String url = request.getUrl().toString();
                    for (String pattern : blacklist) {
                        if (url.matches(".*" + pattern + ".*")) {
                            return createEmptyResponse(request);
                        }
                    }
                    return super.getResponse(request);
                }

                private WebResponse createEmptyResponse(WebRequest request) {
                    return new WebResponse(
                            new WebResponseData("".getBytes(), 200, "", Collections.emptyList()),
                            request,
                            200
                    );
                }
            });



            if (StrUtil.isNotEmpty(cookies)) {
                URL url = URLUtil.url(detailUrl);
                for (String cookie: cookies.split(COOKIE_DELIMITER)) {
                    //key=value
                    String[] split = cookie.split("=");
                    if (split.length < 2) {
                        continue;
                    }
                    cookieManager.addCookie(new Cookie(url.getHost(), split[0], split[1]));
                }
            }
            HtmlPage htmlPage = setting(detailUrl, webClient, cookieManager, Maps.newHashMap());
            return htmlPage.asXml();
        } catch (Throwable e) {
            log.info("[抓取页面数据异常]", e);
        }
        return null;
    }

    /**
     * 爬取html页面，并执行页面js脚本
     *
     * @param httpUrl 网页地址
     * @return html
     */
    public static String crawlingHtml(String httpUrl, String cookies, Map<String, String> headers) {
        try (WebClient webClient = new WebClient(BrowserVersion.CHROME)) {
            CookieManager cookieManager = webClient.getCookieManager();
            cookieManager.setCookiesEnabled(true);
            if (StrUtil.isNotEmpty(cookies)) {
                URL url = URLUtil.url(httpUrl);
                for (String cookie: cookies.split(COOKIE_DELIMITER)) {
                    //key=value
                    String[] split = cookie.split("=");
                    if (split.length < 2) {
                        continue;
                    }
                    cookieManager.addCookie(new Cookie(url.getHost(), split[0], split[1]));
                }
            }
            HtmlPage htmlPage = setting(httpUrl, webClient, cookieManager, headers);
            return htmlPage.asXml();
        } catch (Throwable e) {
            log.info("[抓取页面数据异常]", e);
        }
        return null;
    }

    /**
     * 设置webClient
     *
     * @param httpUrl
     * @param webClient
     * @param cookieManager
     * @param headers
     * @return
     * @throws IOException
     */
    private static HtmlPage setting(String httpUrl, WebClient webClient, CookieManager cookieManager, Map<String, String> headers) throws IOException {
        webClient.setCookieManager(cookieManager);
        webClient.getOptions().setPopupBlockerEnabled(false);
        webClient.getOptions().setCssEnabled(false);
        webClient.getOptions().setDownloadImages(false);
        webClient.getOptions().setThrowExceptionOnScriptError(false);
        webClient.getOptions().setPrintContentOnFailingStatusCode(false);
        webClient.getOptions().setRedirectEnabled(true);
        webClient.getOptions().setJavaScriptEnabled(true);
        webClient.getOptions().setTimeout(10000);
        webClient.setAjaxController(new NicelyResynchronizingAjaxController());
        webClient.waitForBackgroundJavaScript(60000);
        headers.forEach(webClient::addRequestHeader);
        HtmlPage htmlPage = webClient.getPage(httpUrl);
        htmlPage.executeJavaScript("Object.defineProperties(navigator, {webdriver:{get:()=>undefined}});", "webdriver", 1);
        return htmlPage;
    }


    /**
     * 组装cookie
     *
     * @param domain java.lang.String
     * @param cookieStr java.lang.String
     * @return org.htmlunit.CookieManager
     * <AUTHOR>
     * @date 2024/10/8 14:07
     */
    static List<Cookie> convertCookies(String domain, String cookieStr) {
        return Arrays.stream(cookieStr.split(COOKIE_DELIMITER)).map(String::trim).map(value -> {
            String[] split = value.split(COOKIE_EQ_DELIMITER);
            return new Cookie(domain, split[0], split[1]);
        }).collect(Collectors.toList());
    }


    /**
     * 获取cookieManager
     *
     * @param domain java.lang.String
     * @param cookieStr java.lang.String
     * @return org.htmlunit.CookieManager
     * <AUTHOR>
     * @date 2024/10/8 14:13
     */
    public static CookieManager getCookieManager(String domain, String cookieStr) {
        CookieManager cookieManager = new CookieManager();
        cookieManager.setCookiesEnabled(true);
        convertCookies(domain, cookieStr).forEach(cookieManager::addCookie);
        return cookieManager;
    }

    public static String convertCookie(Set<Cookie> cookies) {
        return cookies.stream().map(cookie -> {
            return cookie.getName() + COOKIE_EQ_DELIMITER + cookie.getValue();
        }).distinct().collect(Collectors.joining(COOKIE_DELIMITER));
    }

    public static String parserUrlDomain(String url) {
        try {
            URI uri = new URI(url);
            return uri.getHost();
        } catch (Exception e) {
            log.error("[解析域名]失败. url:{}", url);
        }
        return StrUtil.EMPTY;
    }

    /**
     * 重置cookie
     *
     * @param webClient com.gargoylesoftware.htmlunit.WebClient
     * @param cookie java.lang.String
     * @param url java.lang.String
     * <AUTHOR>
     * @date 2024/10/23 10:48
     */
    public static void resetCookie(WebClient webClient, String cookie, String url) {
        String domain = parserUrlDomain(url);
        CookieManager cookieManager = getCookieManager(domain, cookie);
        webClient.setCookieManager(cookieManager);
    }
}
