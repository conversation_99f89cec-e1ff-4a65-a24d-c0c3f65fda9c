package com.daddylab.msaiagent.db.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/2/15
 */
@Configuration
@MapperScan(
        value = {"com.daddylab.msaiagent.db.aiAgent.mapper"},
        lazyInitialization = "true")
@EnableTransactionManagement
@Slf4j
public class MybatisConfiguration {

  @Bean
  public CommonFieldHandler commonFieldHandler() {
    return new CommonFieldHandler();
  }
}
