package com.daddylab.msaiagent.db.config;

import cn.hutool.core.date.DateUtil;
import org.apache.ibatis.reflection.MetaObject;

import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/14 10:50 上午
 * @description
 */
public class CommonFieldHandler extends BaseMetaObjectHandler {

  final Supplier<Long> currentSecondsSupplier = DateUtil::currentSeconds;
  final Supplier<Long> userIdSupplier = () -> 0L;
  final Supplier<Integer> currentSecondsIntSupplier = () -> (int) DateUtil.currentSeconds();
  final Supplier<Integer> userIdIntSupplier = () -> 0;

  @Override
  public void insertFill(MetaObject metaObject) {
    this.strictInsertFill(metaObject, "createdAt", currentSecondsSupplier, Long.class);
    this.strictInsertFill(metaObject, "createdUid", userIdSupplier, Long.class);
    this.strictInsertFill(metaObject, "updatedAt", currentSecondsSupplier, Long.class);
    this.strictInsertFill(metaObject, "updatedUid", userIdSupplier, Long.class);
    this.strictInsertFill(metaObject, "createdAt", currentSecondsIntSupplier, Integer.class);
    this.strictInsertFill(metaObject, "createdUid", userIdIntSupplier, Integer.class);
    this.strictInsertFill(metaObject, "updatedAt", userIdIntSupplier, Integer.class);
    this.strictInsertFill(metaObject, "updatedUid", currentSecondsIntSupplier, Integer.class);
  }

  @Override
  public void updateFill(MetaObject metaObject) {
    forceUpdateFill(metaObject, "deletedAt", currentSecondsSupplier, Long.class);
    forceUpdateFill(metaObject, "updatedAt", currentSecondsSupplier, Long.class);
    forceUpdateFill(metaObject, "updatedUid", userIdSupplier, Long.class);
    this.strictInsertFill(metaObject, "updatedAt", currentSecondsIntSupplier, Integer.class);
    this.strictInsertFill(metaObject, "updatedUid", userIdIntSupplier, Integer.class);
  }
}
