package com.daddylab.msaiagent.db.base;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(
    of = {"id"},
    callSuper = false)
public class Entity<T extends Model<?>> extends Model<T> implements Serializable {
  private static final long serialVersionUID = 1L;

  /** id */
  @TableId(value = "id", type = IdType.AUTO)
  protected Long id;

  /** 创建时间 */
  @TableField(value = "created_at", fill = FieldFill.INSERT)
  protected Long createdAt;

  /** 创建人 */
  @TableField(value = "created_uid", fill = FieldFill.INSERT)
  protected Long createdUid;

  /** 更新时间 */
  @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
  protected Long updatedAt;

  /** 更新人 */
  @TableField(value = "updated_uid", fill = FieldFill.INSERT_UPDATE)
  protected Long updatedUid;

  /** 删除时间 */
  @TableField(
      insertStrategy = FieldStrategy.NEVER,
      updateStrategy = FieldStrategy.NOT_NULL,
      fill = FieldFill.UPDATE)
  private Long deletedAt;

  /** 是否已删除 */
  @TableLogic(value = "0", delval = "`id`")
  private Long isDel;
}
