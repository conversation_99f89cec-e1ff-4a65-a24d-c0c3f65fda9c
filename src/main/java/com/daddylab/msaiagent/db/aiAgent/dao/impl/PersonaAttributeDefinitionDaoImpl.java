package com.daddylab.msaiagent.db.aiAgent.dao.impl;

import com.daddylab.msaiagent.db.aiAgent.entity.PersonaAttributeDefinition;
import com.daddylab.msaiagent.db.aiAgent.mapper.PersonaAttributeDefinitionMapper;
import com.daddylab.msaiagent.db.aiAgent.dao.PersonaAttributeDefinitionDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 虚拟人属性定义表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Service
public class PersonaAttributeDefinitionDaoImpl extends ServiceImpl<PersonaAttributeDefinitionMapper, PersonaAttributeDefinition> implements PersonaAttributeDefinitionDao {
  @Override
  public PersonaAttributeDefinitionMapper getBaseMapper() {
    return super.getBaseMapper();
  }
}
