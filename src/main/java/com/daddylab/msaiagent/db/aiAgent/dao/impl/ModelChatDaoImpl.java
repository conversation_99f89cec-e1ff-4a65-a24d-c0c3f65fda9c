package com.daddylab.msaiagent.db.aiAgent.dao.impl;

import com.daddylab.msaiagent.db.aiAgent.entity.ModelChat;
import com.daddylab.msaiagent.db.aiAgent.mapper.ModelChatMapper;
import com.daddylab.msaiagent.db.aiAgent.dao.ModelChatDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 模型对话记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Service
public class ModelChatDaoImpl extends ServiceImpl<ModelChatMapper, ModelChat> implements ModelChatDao {
  @Override
  public ModelChatMapper getBaseMapper() {
    return super.getBaseMapper();
  }
}
