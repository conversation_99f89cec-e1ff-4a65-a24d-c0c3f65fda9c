package com.daddylab.msaiagent.db.aiAgent.dao.impl;

import com.daddylab.msaiagent.db.aiAgent.entity.RedbookHotspot;
import com.daddylab.msaiagent.db.aiAgent.mapper.RedbookHotspotMapper;
import com.daddylab.msaiagent.db.aiAgent.dao.RedbookHotspotDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;


/**
 * <p>
 * 小红书热点 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Service
public class RedbookHotspotDaoImpl extends ServiceImpl<RedbookHotspotMapper, RedbookHotspot> implements RedbookHotspotDao {
  @Override
  public RedbookHotspotMapper getBaseMapper() {
    return super.getBaseMapper();
  }

  @Override
  public List<RedbookHotspot> getRank(int rank, int days) {
    //计算当前时间，近days的开始时间戳和结束时间戳
    LocalDateTime now = LocalDateTime.now();
    LocalDateTime endOfDay = now.withHour(23).withMinute(59).withSecond(59);
    long endTime = endOfDay.toInstant(ZoneOffset.ofHours(8)).toEpochMilli()/1000;
    LocalDateTime startOfDay = now.minusDays(days).withHour(0).withMinute(0).withSecond(0);
    long startTime = startOfDay.toInstant(ZoneOffset.ofHours(8)).toEpochMilli()/1000;
    // 如果是周榜（rank为2或3），计算周范围
    if (rank == 2 || rank == 3) {
      LocalDateTime weekStart = startOfDay.with(java.time.DayOfWeek.MONDAY).withHour(0).withMinute(0).withSecond(0);
      LocalDateTime weekEnd = weekStart.plusDays(6).withHour(23).withMinute(59).withSecond(59);
      startTime = weekStart.toInstant(ZoneOffset.ofHours(8)).toEpochMilli() / 1000;
      endTime = weekEnd.toInstant(ZoneOffset.ofHours(8)).toEpochMilli() / 1000;
    }
    //筛选数据
    List<RedbookHotspot> redbookHotspots = this.lambdaQuery()
            .eq(RedbookHotspot::getRank, rank)
            .ge(RedbookHotspot::getStartTime, startTime)  // start_time >= 起始时间
            .le(RedbookHotspot::getEndTime, endTime) // end_time <= 结束时间
            .orderByDesc(RedbookHotspot::getStartTime)
            .list();
    return redbookHotspots;
  }


}
