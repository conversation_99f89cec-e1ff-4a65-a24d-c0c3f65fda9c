package com.daddylab.msaiagent.db.aiAgent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.daddylab.msaiagent.db.base.Entity;
import java.io.Serializable;

import com.daddylab.msaiagent.db.base.PureEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;

/**
 * <p>
 * 虚拟人详情（应该村mongodb暂时存json）
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
@TableName("persona_detail")
public class PersonaDetail extends PureEntity<PersonaDetail> {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    @TableField("persona_id")
    private Long personaId;

    /**
     * 用户uuid
     */
    @TableField("persona_uuid")
    private String personaUuid;

    /**
     * 虚拟人详情
     */
    @TableField("content")
    private String content;

  @TableField("job_id")
  private Long jobId;

  // ------
  /** 生成异常信息 */
  @TableField(value = "error_content", exist = false)
  private String errorContent;

  /** ai生成提示信息 */
  @TableField(value = "update_notes", exist = false)
  private String updateNotes;

  /** ai生成追问信息 */
  @TableField(value = "ai_questions", exist = false)
  private String aiQuestions;

  /** 对话id */
  @TableField(value = "chat_id", exist = false)
  private Long chatId;
}
