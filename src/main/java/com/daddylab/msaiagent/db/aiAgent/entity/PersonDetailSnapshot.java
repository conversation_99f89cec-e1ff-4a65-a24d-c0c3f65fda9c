package com.daddylab.msaiagent.db.aiAgent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.daddylab.msaiagent.db.base.Entity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;

/**
 * 虚拟人详情快照
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
@TableName("person_detail_snapshot")
public class PersonDetailSnapshot extends Entity<PersonDetailSnapshot> {

  private static final long serialVersionUID = 1L;

  /** 用户id */
  @TableField("person_id")
  private Long personId;

  /** 用户uuid */
  @TableField("person_uuid")
  private String personUuid;

  /** 虚拟人详情 */
  @TableField("content")
  private String content;

  /** 更新理由 */
  @TableField("update_reason")
  private String updateReason;

  /** 属性分析* */
  @TableField("attribute_analysis")
  private String attributeAnalysis;


}
