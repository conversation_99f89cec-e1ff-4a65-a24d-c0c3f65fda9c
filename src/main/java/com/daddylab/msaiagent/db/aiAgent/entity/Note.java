package com.daddylab.msaiagent.db.aiAgent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.daddylab.msaiagent.db.base.Entity;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;

/**
 * <p>
 * 笔记
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
@TableName("note")
public class Note extends Entity<Note> {

    private static final long serialVersionUID = 1L;

    /**
     * 虚拟人物ID
     */
    @TableField("virtual_person_id")
    private Long virtualPersonId;

    /**
     * 场景ID
     */
    @TableField("scene_id")
    private Long sceneId;

    /**
     * 标题使用的提示词
     */
    @TableField("title_prompt_id")
    private Long titlePromptId;

    /**
     * 内容使用的提示词
     */
    @TableField("content_prompt_id")
    private Long contentPromptId;

    /**
     * 标签使用的提示词
     */
    @TableField("tag_prompt_id")
    private Long tagPromptId;

    /**
     * 标题使用的提示词内容
     */
    @TableField("title_prompt_content")
    private String titlePromptContent;

    /**
     * 标题使用的提示词内容
     */
    @TableField("content_prompt_content")
    private String contentPromptContent;

    /**
     * 标签使用的提示词内容
     */
    @TableField("tag_prompt_content")
    private String tagPromptContent;

    /**
     * 生成的标题
     */
    @TableField("title")
    private String title;

    /**
     * 生成的内容
     */
    @TableField("content")
    private String content;

    /**
     * 生成的标签
     */
    @TableField("tag")
    private String tag;

    /**
     * 笔记状态 0-待生成 1-生成中 2-生成完成
     */
    @TableField("status")
    private Integer status;

    /**
     * 使用的模型
     */
    @TableField("model")
    private String model;

    /**
     * 最终的要发表的笔记
     */
    @TableField("final_content")
    private String finalContent;
}
