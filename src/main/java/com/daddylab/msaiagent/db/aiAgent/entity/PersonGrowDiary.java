package com.daddylab.msaiagent.db.aiAgent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.daddylab.msaiagent.db.base.Entity;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;

/**
 * <p>
 * 人物成长日记
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
@TableName("person_grow_diary")
public class PersonGrowDiary extends Entity<PersonGrowDiary> {

    private static final long serialVersionUID = 1L;

    /**
     * 人物ID，唯一键
     */
    @TableField("person_uuid")
    private String personUuId;


    /**
     * 日记内容
     */
    @TableField("diary_content")
    private String diaryContent;
}
