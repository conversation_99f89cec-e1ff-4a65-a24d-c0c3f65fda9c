package com.daddylab.msaiagent.db.aiAgent.dao.impl;

import com.daddylab.msaiagent.db.aiAgent.entity.PersonaAttributeCategoryLvl1;
import com.daddylab.msaiagent.db.aiAgent.mapper.PersonaAttributeCategoryLvl1Mapper;
import com.daddylab.msaiagent.db.aiAgent.dao.PersonaAttributeCategoryLvl1Dao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 虚拟人一级属性分类表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Service
public class PersonaAttributeCategoryLvl1DaoImpl extends ServiceImpl<PersonaAttributeCategoryLvl1Mapper, PersonaAttributeCategoryLvl1> implements PersonaAttributeCategoryLvl1Dao {
  @Override
  public PersonaAttributeCategoryLvl1Mapper getBaseMapper() {
    return super.getBaseMapper();
  }
}
