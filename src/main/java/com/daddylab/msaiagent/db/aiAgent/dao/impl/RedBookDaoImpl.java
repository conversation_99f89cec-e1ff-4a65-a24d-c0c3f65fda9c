package com.daddylab.msaiagent.db.aiAgent.dao.impl;

import com.daddylab.msaiagent.db.aiAgent.entity.RedBook;
import com.daddylab.msaiagent.db.aiAgent.mapper.RedBookMapper;
import com.daddylab.msaiagent.db.aiAgent.dao.RedBookDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 小红书笔记 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Service
public class RedBookDaoImpl extends ServiceImpl<RedBookMapper, RedBook> implements RedBookDao {
  @Override
  public RedBookMapper getBaseMapper() {
    return super.getBaseMapper();
  }
}
