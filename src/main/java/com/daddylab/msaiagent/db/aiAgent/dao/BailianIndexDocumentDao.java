package com.daddylab.msaiagent.db.aiAgent.dao;

import com.daddylab.msaiagent.db.aiAgent.entity.BailianIndexDocument;
import com.baomidou.mybatisplus.extension.service.IService;
import com.daddylab.msaiagent.db.aiAgent.enums.BaiLianIndexDocumentStatusEnum;
import com.daddylab.msaiagent.db.aiAgent.mapper.BailianIndexDocumentMapper;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 百炼文件索引下的文档列表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
public interface BailianIndexDocumentDao extends IService<BailianIndexDocument> {
  @Override
  BailianIndexDocumentMapper getBaseMapper();

  List<String> status(Collection<String> fileIds, BaiLianIndexDocumentStatusEnum status);

  boolean updateStatus(
          List<BailianIndexDocument> indexDocuments,
          BaiLianIndexDocumentStatusEnum baiLianIndexDocStatusEnum);

  boolean updateStatusByIds(
          List<Long> indexDocLocalIds, BaiLianIndexDocumentStatusEnum baiLianIndexDocStatusEnum);

  List<BailianIndexDocument> listByCategoryId(String indexId, String categoryId);
}
