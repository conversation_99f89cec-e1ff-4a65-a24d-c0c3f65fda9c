package com.daddylab.msaiagent.db.aiAgent.enums;

import com.daddylab.msaiagent.common.base.enums.IIntegerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @className BaiLianFileTypeEnum
 * @date 2025/3/4 13:34
 * @description: TODO
 */
@AllArgsConstructor
@Getter
public enum BaiLianFileRemoteStatusEnum implements IIntegerEnum {

  // 模块类型 1-待上传 2-已上传 3-已删除
  WAIT(1, "待上传"),
  SUCCESS(2, "已上传"),
  DELETE(3, "已删除"),
  NOT_EXISTS(4, "远程不存在"),
  ;

  private final Integer value;
  private final String desc;
}
