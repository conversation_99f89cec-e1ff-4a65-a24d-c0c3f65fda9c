package com.daddylab.msaiagent.db.aiAgent.dao.impl;

import com.daddylab.msaiagent.db.aiAgent.entity.AigcJob;
import com.daddylab.msaiagent.db.aiAgent.mapper.AigcJobMapper;
import com.daddylab.msaiagent.db.aiAgent.dao.AigcJobDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;


/**
 * <p>
 * aigc的异步任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Service
public class AigcJobDaoImpl extends ServiceImpl<AigcJobMapper, AigcJob> implements AigcJobDao {
  @Override
  public AigcJobMapper getBaseMapper() {
    return super.getBaseMapper();
  }
}
