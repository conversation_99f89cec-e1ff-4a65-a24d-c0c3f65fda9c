package com.daddylab.msaiagent.db.aiAgent.dao.impl;

import com.daddylab.msaiagent.db.aiAgent.entity.VirtualPerson;
import com.daddylab.msaiagent.db.aiAgent.mapper.VirtualPersonMapper;
import com.daddylab.msaiagent.db.aiAgent.dao.VirtualPersonDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 虚拟人物 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
@Service
public class VirtualPersonDaoImpl extends ServiceImpl<VirtualPersonMapper, VirtualPerson> implements VirtualPersonDao {
  @Override
  public VirtualPersonMapper getBaseMapper() {
    return super.getBaseMapper();
  }
}
