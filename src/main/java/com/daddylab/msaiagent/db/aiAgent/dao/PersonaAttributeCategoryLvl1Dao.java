package com.daddylab.msaiagent.db.aiAgent.dao;

import com.daddylab.msaiagent.db.aiAgent.entity.PersonaAttributeCategoryLvl1;
import com.baomidou.mybatisplus.extension.service.IService;
import com.daddylab.msaiagent.db.aiAgent.mapper.PersonaAttributeCategoryLvl1Mapper;

/**
 * <p>
 * 虚拟人一级属性分类表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
public interface PersonaAttributeCategoryLvl1Dao extends IService<PersonaAttributeCategoryLvl1> {
  @Override
  PersonaAttributeCategoryLvl1Mapper getBaseMapper();
}
