package com.daddylab.msaiagent.db.aiAgent.dao;

import com.daddylab.msaiagent.db.aiAgent.entity.PersonaDetail;
import com.baomidou.mybatisplus.extension.service.IService;
import com.daddylab.msaiagent.db.aiAgent.mapper.PersonaDetailMapper;

/**
 * <p>
 * 虚拟人详情（应该村mongodb暂时存json） 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
public interface PersonaDetailDao extends IService<PersonaDetail> {
  @Override
  PersonaDetailMapper getBaseMapper();
}
