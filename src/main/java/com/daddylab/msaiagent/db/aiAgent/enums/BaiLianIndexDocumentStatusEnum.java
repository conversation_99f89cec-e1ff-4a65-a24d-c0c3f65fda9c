package com.daddylab.msaiagent.db.aiAgent.enums;

import com.daddylab.msaiagent.common.base.enums.INameValuedEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文档导入状态。可能值为： INSERT_ERROR：文档导入失败。 RUNNING：文档导入中。 DELETED：文档已删除。 FINISH：文档导入成功。
 *
 * <AUTHOR>
 * @since 2025/4/13
 */
@Getter
@AllArgsConstructor
public enum BaiLianIndexDocumentStatusEnum implements INameValuedEnum {
  INSERT_ERROR("文档导入失败"),
  RUNNING("文档导入中"),
  DELETED("文档已删除"),
  FINISH("文档导入成功"),
  INSERT_ERROR_RETRY("文档导入失败重试中（自定义状态）"),
  INSERT_ERROR_FINAL("文档导入失败放弃重试"),
  ;

  private final String desc;
}
