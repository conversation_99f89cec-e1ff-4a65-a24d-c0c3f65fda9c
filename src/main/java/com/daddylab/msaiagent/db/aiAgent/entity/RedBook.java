package com.daddylab.msaiagent.db.aiAgent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.daddylab.msaiagent.db.aiAgent.enums.BooleanEnum;
import com.daddylab.msaiagent.db.base.Entity;
import java.io.Serializable;
import java.util.List;

import com.daddylab.msaiagent.db.handler.ListStringJacksonHandler;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;

/**
 * <p>
 * 小红书笔记
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
@TableName(value = "red_book", autoResultMap = true)
public class RedBook extends Entity<RedBook> {

    private static final long serialVersionUID = 1L;

    /**
     * 账号
     */
    @TableField("account_id")
    private String accountId;
    /**
     * 账号加密ID
     */
    @TableField("account_encrypt_id")
    private String accountEncryptId;

    /**
     * 账号名称
     */
    @TableField("account_name")
    private String accountName;

    /**
     * 笔记ID
     */
    @TableField("note_id")
    private String noteId;
    /**
     * 标题
     */
    @TableField("title")
    private String title;

    /**
     * 笔记内容
     */
    @TableField("content")
    private String content;

    /**
     * 笔记类型 1-图文 2-视频
     */
    @TableField("type")
    private Integer type;

    /**
     * 笔记种类 1-账号全部笔记 2-关键词热点数据
     */
    @TableField("cate_type")
    private Integer cateType;
    /**
     * 详情链接
     */
    @TableField("url")
    private String url;

    /**
     * 标签
     */
    @TableField(value = "tags", typeHandler = ListStringJacksonHandler.class)
    private List<String> tags;

    /**
     * 图片json,ps:["1.png", "2.jpg"]
     */
    @TableField(value = "images", typeHandler = ListStringJacksonHandler.class)
    private List<String> images;

    /**
     * 视频链接json,ps:["1.mp4", "2.mp4"]
     */
    @TableField(value = "videos", typeHandler = ListStringJacksonHandler.class)
    private List<String> videos;

    /**
     * 是否解析
     */
    @TableField("is_parser")
    private BooleanEnum isParser;

    /**
     * 原内容
     */
    @TableField("source_data")
    private String sourceData;
    /**
     * 点赞数量
     */
    @TableField("upvote_num")
    private String upvoteNum;

    /**
     * 收藏数量
     */
    @TableField("collect_num")
    private String collectNum;

    /**
     * 评论数量
     */
    @TableField("discuss_num")
    private String discussNum;
    /**
     * 最后更新时间
     */
    @TableField("last_update_time")
    private Long lastUpdateTime;
}
