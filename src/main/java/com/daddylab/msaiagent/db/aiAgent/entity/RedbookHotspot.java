package com.daddylab.msaiagent.db.aiAgent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.daddylab.msaiagent.db.base.Entity;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;

/**
 * <p>
 * 小红书热点
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
@TableName("redbook_hotspot")
public class RedbookHotspot extends Entity<RedbookHotspot> {

    private static final long serialVersionUID = 1L;

    /**
     * 榜单类型，1行业热度榜 2热词飙升榜 3热词总量榜 4新榜热点指数
     */
    @TableField("rank")
    private Integer rank;

    /**
     * 榜单周期,0日榜 1周榜 2月榜
     */
    @TableField("gap")
    private Integer gap;

    /**
     * 榜单起始时间戳
     */
    @TableField("start_time")
    private Long startTime;

    /**
     * 榜单结束时间戳
     */
    @TableField("end_time")
    private Long endTime;

    /**
     * 分类
     */
    @TableField("cate")
    private String cate;

    /**
     * 热词
     */
    @TableField("hotword")
    private String hotword;

    /**
     * 热度指数
     */
    @TableField("hot_val")
    private Long hotVal;

    /**
     * 关联笔记数
     */
    @TableField("note_val")
    private Long noteVal;

    /**
     * 关联笔记互动量
     */
    @TableField("interact_val")
    private Long interactVal;

    /**
     * 新增笔记数
     */
    @TableField("note_new_val")
    private Long noteNewVal;

    /**
     * 关联分类
     */
    @TableField("tag_rate")
    private String tagRate;

    /**
     * 热点属性,0无 1搜索飙升
     */
    @TableField("hot_attr")
    private Integer hotAttr;
}
