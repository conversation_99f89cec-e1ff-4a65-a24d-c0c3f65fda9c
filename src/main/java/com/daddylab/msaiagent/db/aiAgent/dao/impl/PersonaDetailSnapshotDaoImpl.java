package com.daddylab.msaiagent.db.aiAgent.dao.impl;

import com.daddylab.msaiagent.db.aiAgent.entity.PersonDetailSnapshot;
import com.daddylab.msaiagent.db.aiAgent.mapper.PersonDetailSnapshotMapper;
import com.daddylab.msaiagent.db.aiAgent.dao.PersonDetailSnapshotDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 虚拟人详情快照 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Service
public class PersonaDetailSnapshotDaoImpl extends ServiceImpl<PersonDetailSnapshotMapper, PersonDetailSnapshot> implements PersonDetailSnapshotDao {
  @Override
  public PersonDetailSnapshotMapper getBaseMapper() {
    return super.getBaseMapper();
  }
}
