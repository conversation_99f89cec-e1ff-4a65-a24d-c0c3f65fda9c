package com.daddylab.msaiagent.db.aiAgent.dao.impl;

import com.daddylab.msaiagent.db.aiAgent.entity.ActivityMaterial;
import com.daddylab.msaiagent.db.aiAgent.mapper.ActivityMaterialMapper;
import com.daddylab.msaiagent.db.aiAgent.dao.ActivityMaterialDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 活动信息素材表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Service
public class ActivityMaterialDaoImpl extends ServiceImpl<ActivityMaterialMapper, ActivityMaterial> implements ActivityMaterialDao {
  @Override
  public ActivityMaterialMapper getBaseMapper() {
    return super.getBaseMapper();
  }
}
