package com.daddylab.msaiagent.db.aiAgent.dao;

import com.daddylab.msaiagent.db.aiAgent.entity.PersonaAttributeDefinition;
import com.baomidou.mybatisplus.extension.service.IService;
import com.daddylab.msaiagent.db.aiAgent.mapper.PersonaAttributeDefinitionMapper;

/**
 * <p>
 * 虚拟人属性定义表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
public interface PersonaAttributeDefinitionDao extends IService<PersonaAttributeDefinition> {
  @Override
  PersonaAttributeDefinitionMapper getBaseMapper();
}
