package com.daddylab.msaiagent.db.aiAgent.dao.impl;

import com.daddylab.msaiagent.db.aiAgent.entity.ProductMaterial;
import com.daddylab.msaiagent.db.aiAgent.mapper.ProductMaterialMapper;
import com.daddylab.msaiagent.db.aiAgent.dao.ProductMaterialDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 商品素材表 (强化人群-痛点-卖点关联) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Service
public class ProductMaterialDaoImpl extends ServiceImpl<ProductMaterialMapper, ProductMaterial> implements ProductMaterialDao {
  @Override
  public ProductMaterialMapper getBaseMapper() {
    return super.getBaseMapper();
  }
}
