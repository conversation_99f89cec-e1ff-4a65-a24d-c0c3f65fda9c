package com.daddylab.msaiagent.db.aiAgent.dao.impl;

import cn.hutool.core.date.DateUtil;
import com.daddylab.msaiagent.db.aiAgent.entity.BailianFile;
import com.daddylab.msaiagent.db.aiAgent.enums.BaiLianFileTypeEnum;
import com.daddylab.msaiagent.db.aiAgent.mapper.BailianFileMapper;
import com.daddylab.msaiagent.db.aiAgent.dao.BailianFileDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.daddylab.msaiagent.db.base.Entity;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <p>
 * 百炼文件处理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Service
public class BailianFileDaoImpl extends ServiceImpl<BailianFileMapper, BailianFile> implements BailianFileDao {
  @Override
  public BailianFileMapper getBaseMapper() {
    return super.getBaseMapper();
  }
  @Override
  public List<BailianFile> listByTypeId(BaiLianFileTypeEnum type, Long id) {
    return lambdaQuery().eq(BailianFile::getType, type).eq(BailianFile::getTypeId, id).list();
  }

  @Override
  public List<BailianFile> listByTypeIds(BaiLianFileTypeEnum type, List<Long> typeIds) {
    return lambdaQuery().eq(BailianFile::getType, type).in(BailianFile::getTypeId, typeIds).list();
  }

  @Override
  public List<BailianFile> listByFileId(String fileId) {
    return lambdaQuery().eq(BailianFile::getFileId, fileId).list();
  }

  @Override
  public boolean updateRefCount(String fileId, long count) {
    return lambdaUpdate()
            .eq(BailianFile::getFileId, fileId)
            .set(BailianFile::getRefCount, count)
            .set(Entity::getUpdatedAt, DateUtil.currentSeconds())
            .update();
  }

  @Override
  public boolean updateRefCount(List<String> fileIds, long count) {
    return lambdaUpdate()
            .in(BailianFile::getFileId, fileIds)
            .set(BailianFile::getRefCount, count)
            .set(Entity::getUpdatedAt, DateUtil.currentSeconds())
            .update();
  }
}
