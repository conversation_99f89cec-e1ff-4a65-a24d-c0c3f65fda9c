package com.daddylab.msaiagent.db.aiAgent.dao;

import com.daddylab.msaiagent.db.aiAgent.entity.PersonGrowDiary;
import com.baomidou.mybatisplus.extension.service.IService;
import com.daddylab.msaiagent.db.aiAgent.mapper.PersonGrowDiaryMapper;

import java.util.List;

/**
 * <p>
 * 人物成长日记 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
public interface PersonGrowDiaryDao extends IService<PersonGrowDiary> {
    @Override
    PersonGrowDiaryMapper getBaseMapper();

    List<PersonGrowDiary> selectLatestDiary(String personUuId, Integer limit);
}
