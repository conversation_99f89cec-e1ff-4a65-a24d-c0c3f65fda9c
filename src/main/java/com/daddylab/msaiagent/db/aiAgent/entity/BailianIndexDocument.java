package com.daddylab.msaiagent.db.aiAgent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.daddylab.msaiagent.db.base.Entity;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;

/**
 * <p>
 * 百炼文件索引下的文档列表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
@TableName("bailian_index_document")
public class BailianIndexDocument extends Entity<BailianIndexDocument> {

    private static final long serialVersionUID = 1L;

    /**
     * 索引ID
     */
    @TableField("index_id")
    private String indexId;

    /**
     * 非结构化数据指向类目ID，结构化数据代表数据表ID
     */
    @TableField("source_id")
    private String sourceId;

    /**
     * 知识库文档ID
     */
    @TableField("file_id")
    private String fileId;

    /**
     * 文档名称
     */
    @TableField("name")
    private String name;

    /**
     * 文档大小，单位字节 Byte。
     */
    @TableField("size")
    private Integer size;

    /**
     * 文档格式类型。可能值为： pdf、docx、doc、txt、md、pptx、ppt、png、jpg、jpeg、bmp、gif、EXCEL。
     */
    @TableField("document_type")
    private String documentType;

    /**
     * 文档导入状态。可能值为： INSERT_ERROR：文档导入失败。 RUNNING：文档导入中。 DELETED：文档已删除。 FINISH：文档导入成功。
     */
    @TableField("status")
    private String status;

    /**
     * 文档导入错误状态码。
     */
    @TableField("code")
    private String code;

    /**
     * 文档导入错误信息。
     */
    @TableField("message")
    private String message;
}
