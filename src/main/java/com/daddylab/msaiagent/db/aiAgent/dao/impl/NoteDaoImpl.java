package com.daddylab.msaiagent.db.aiAgent.dao.impl;

import com.daddylab.msaiagent.db.aiAgent.entity.Note;
import com.daddylab.msaiagent.db.aiAgent.mapper.NoteMapper;
import com.daddylab.msaiagent.db.aiAgent.dao.NoteDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 笔记 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
@Service
public class NoteDaoImpl extends ServiceImpl<NoteMapper, Note> implements NoteDao {
  @Override
  public NoteMapper getBaseMapper() {
    return super.getBaseMapper();
  }
}
