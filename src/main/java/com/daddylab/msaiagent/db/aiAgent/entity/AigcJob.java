package com.daddylab.msaiagent.db.aiAgent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.daddylab.msaiagent.db.base.Entity;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;

/**
 * <p>
 * aigc的异步任务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
@TableName("aigc_job")
public class AigcJob extends Entity<AigcJob> {

    private static final long serialVersionUID = 1L;

    /**
     * 任务类型
     */
    @TableField("job_type")
    private String jobType;

    /**
     * 任务主体id
     */
    @TableField("biz_id")
    private Long bizId;

    /**
     * 对话id
     */
    @TableField("model_chat_id")
    private Long modelChatId;

    /**
     * 异常信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 场景状态 0-待开始 1-生成中 2-解析中 3-处理完成 -1异常
     */
    @TableField("status")
    private Integer status;

    /**
     * 请求的表单
     */
    @TableField("job_data")
    private String jobData;

    /**
     * ai生成提示信息
     */
    @TableField("update_notes")
    private String updateNotes;

    /**
     * ai生成追问信息
     */
    @TableField("ai_questions")
    private String aiQuestions;

    /**
     * ai其他补充的字段
     */
    @TableField("extra_data")
    private String extraData;
}
