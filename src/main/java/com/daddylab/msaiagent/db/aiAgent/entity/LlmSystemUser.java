package com.daddylab.msaiagent.db.aiAgent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.daddylab.msaiagent.db.base.Entity;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;

/**
 * <p>
 * 大模型系统用户定义
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
@TableName("llm_system_user")
public class LlmSystemUser extends Entity<LlmSystemUser> {

    private static final long serialVersionUID = 1L;

    /**
     * 业务编码
     */
    @TableField("biz_key")
    private String bizKey;

    /**
     * 系统用户定义
     */
    @TableField("system_msg")
    private String systemMsg;
}
