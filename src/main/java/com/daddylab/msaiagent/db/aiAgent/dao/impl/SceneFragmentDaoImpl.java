package com.daddylab.msaiagent.db.aiAgent.dao.impl;

import com.daddylab.msaiagent.db.aiAgent.entity.SceneFragment;
import com.daddylab.msaiagent.db.aiAgent.mapper.SceneFragmentMapper;
import com.daddylab.msaiagent.db.aiAgent.dao.SceneFragmentDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 场景片段库表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Service
public class SceneFragmentDaoImpl extends ServiceImpl<SceneFragmentMapper, SceneFragment> implements SceneFragmentDao {
  @Override
  public SceneFragmentMapper getBaseMapper() {
    return super.getBaseMapper();
  }
}
