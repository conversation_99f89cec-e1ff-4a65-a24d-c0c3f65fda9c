package com.daddylab.msaiagent.db.aiAgent.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.daddylab.msaiagent.db.aiAgent.entity.BailianIndexDocument;
import com.daddylab.msaiagent.db.aiAgent.enums.BaiLianIndexDocumentStatusEnum;
import com.daddylab.msaiagent.db.aiAgent.mapper.BailianIndexDocumentMapper;
import com.daddylab.msaiagent.db.aiAgent.dao.BailianIndexDocumentDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.daddylab.msaiagent.db.base.Entity;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <p>
 * 百炼文件索引下的文档列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Service
public class BailianIndexDocumentDaoImpl extends ServiceImpl<BailianIndexDocumentMapper, BailianIndexDocument> implements BailianIndexDocumentDao {
  @Override
  public BailianIndexDocumentMapper getBaseMapper() {
    return super.getBaseMapper();
  }

  @Override
  public List<String> status(Collection<String> fileIds, BaiLianIndexDocumentStatusEnum status) {
    if (CollUtil.isEmpty(fileIds)) return Collections.emptyList();
    if (Objects.isNull(status)) return Collections.emptyList();

    return this.lambdaQuery()
            .in(BailianIndexDocument::getFileId, fileIds)
            .eq(BailianIndexDocument::getStatus, status.name())
            .list()
            .stream()
            .map(BailianIndexDocument::getFileId)
            .distinct()
            .collect(Collectors.toList());
  }

  @Override
  public boolean updateStatus(
          List<BailianIndexDocument> indexDocuments, BaiLianIndexDocumentStatusEnum status) {
    final List<Long> indexDocLocalIds =
            indexDocuments.stream().map(Entity::getId).collect(Collectors.toList());
    return updateStatusByIds(indexDocLocalIds, status);
  }

  @Override
  public boolean updateStatusByIds(List<Long> indexDocLocalIds, BaiLianIndexDocumentStatusEnum status) {
    return lambdaUpdate()
            .set(BailianIndexDocument::getStatus, status)
            .set(Entity::getUpdatedAt, DateUtil.currentSeconds())
            .in(Entity::getId, indexDocLocalIds)
            .update();
  }

  @Override
  public List<BailianIndexDocument> listByCategoryId(String indexId, String categoryId) {
    return getBaseMapper().listByCategoryId(indexId, categoryId);
  }
}
