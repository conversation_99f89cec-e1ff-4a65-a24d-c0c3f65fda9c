package com.daddylab.msaiagent.db.aiAgent.dao.impl;

import com.daddylab.msaiagent.db.aiAgent.entity.KnowledgeBaseMaterial;
import com.daddylab.msaiagent.db.aiAgent.mapper.KnowledgeBaseMaterialMapper;
import com.daddylab.msaiagent.db.aiAgent.dao.KnowledgeBaseMaterialDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 知识库素材表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Service
public class KnowledgeBaseMaterialDaoImpl extends ServiceImpl<KnowledgeBaseMaterialMapper, KnowledgeBaseMaterial> implements KnowledgeBaseMaterialDao {
  @Override
  public KnowledgeBaseMaterialMapper getBaseMapper() {
    return super.getBaseMapper();
  }

  @Override
  public KnowledgeBaseMaterial getByUuid(String uuid) {
    return this.lambdaQuery()
            .eq(KnowledgeBaseMaterial::getMaterialUuid, uuid)
            .one();
  }
}
