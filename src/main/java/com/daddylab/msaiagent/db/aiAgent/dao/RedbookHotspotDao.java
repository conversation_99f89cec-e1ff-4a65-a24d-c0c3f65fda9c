package com.daddylab.msaiagent.db.aiAgent.dao;

import com.daddylab.msaiagent.db.aiAgent.entity.RedbookHotspot;
import com.baomidou.mybatisplus.extension.service.IService;
import com.daddylab.msaiagent.db.aiAgent.mapper.RedbookHotspotMapper;

import java.util.List;

/**
 * <p>
 * 小红书热点 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
public interface RedbookHotspotDao extends IService<RedbookHotspot> {
    @Override
    RedbookHotspotMapper getBaseMapper();

    List<RedbookHotspot> getRank(int rank, int days);

}
