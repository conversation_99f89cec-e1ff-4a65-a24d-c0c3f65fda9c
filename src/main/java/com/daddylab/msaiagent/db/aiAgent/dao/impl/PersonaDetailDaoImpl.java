package com.daddylab.msaiagent.db.aiAgent.dao.impl;

import com.daddylab.msaiagent.db.aiAgent.entity.PersonaDetail;
import com.daddylab.msaiagent.db.aiAgent.mapper.PersonaDetailMapper;
import com.daddylab.msaiagent.db.aiAgent.dao.PersonaDetailDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 虚拟人详情（应该村mongodb暂时存json） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Service
public class PersonaDetailDaoImpl extends ServiceImpl<PersonaDetailMapper, PersonaDetail> implements PersonaDetailDao {
  @Override
  public PersonaDetailMapper getBaseMapper() {
    return super.getBaseMapper();
  }
}
