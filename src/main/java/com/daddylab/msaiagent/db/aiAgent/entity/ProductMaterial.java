package com.daddylab.msaiagent.db.aiAgent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.daddylab.msaiagent.db.base.PureEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;

/**
 * <p>
 * 商品素材表 (强化人群-痛点-卖点关联)
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
@TableName("product_material")
@Schema(title = "商品素材表", description = "商品素材表 (强化人群-痛点-卖点关联)")
public class ProductMaterial extends PureEntity<ProductMaterial> {

    private static final long serialVersionUID = 1L;

    /**
     * 素材全局唯一UUID
     */
    @Schema(title = "素材全局唯一UUID")
    @TableField("asset_uuid")
    private String assetUuid;

    /**
     * 素材简要描述 (运营用)
     */
    @Schema(title = "素材简要描述", description = "运营用")
    @TableField("description")
    private String description;

    /**
     * 商品官方名称/常用名
     */
    @Schema(title = "商品官方名称/常用名")
    @TableField("product_name")
    private String productName;

    /**
     * 商品分类标签 (JSON数组字符串)
     */
    @Schema(title = "商品分类标签", description = "JSON数组字符串")
    @TableField("product_category_tags_json")
    private String productCategoryTagsJson;

    /**
     * 核心卖点总列表 (JSON数组字符串, 作为所有卖点的集合)
     */
    @TableField("core_selling_points_json")
    @Schema(title = "核心卖点总列表", description = "JSON数组字符串, 作为所有卖点的集合")
    private String coreSellingPointsJson;

    /**
     * 目标人群细分-痛点-解决方案关联 (JSON数组对象, 结构详见下方注释)
     */
    @TableField("targeted_segments_pain_points_solutions_json")
    @Schema(title = "目标人群细分-痛点-解决方案关联", description = "JSON数组对象, 结构详见下方注释")
    private String targetedSegmentsPainPointsSolutionsJson;

    /**
     * 商品总体目标受众概括描述 (可选, 作为补充)
     */
    @TableField("target_audience_general_description")
    @Schema(title = "商品总体目标受众概括描述", description = "可选, 作为补充")
    private String targetAudienceGeneralDescription;

    /**
     * 典型使用场景示例 (JSON数组字符串)
     */
    @TableField("usage_scenario_examples_json")
    @Schema(title = "典型使用场景示例", description = "JSON数组字符串")
    private String usageScenarioExamplesJson;

    /**
     * 用户体验相关的关键词/标签 (JSON数组字符串)
     */
    @TableField("user_experience_keywords_json")
    @Schema(title = "用户体验相关的关键词/标签", description = "JSON数组字符串")
    private String userExperienceKeywordsJson;

    /**
     * 简要使用方法或技巧
     */
    @TableField("instructions_for_use_brief")
    @Schema(title = "简要使用方法或技巧")
    private String instructionsForUseBrief;

    /**
     * 产品规格参数 (JSON对象)
     */
    @TableField("product_specifications_json")
    @Schema(title = "产品规格参数", description = "JSON对象")
    private String productSpecificationsJson;

    /**
     * 外观描述关键词 (JSON数组字符串)
     */
    @TableField("appearance_description_keywords_json")
    @Schema(title = "外观描述关键词", description = "JSON数组字符串")
    private String appearanceDescriptionKeywordsJson;

    /**
     * 行动召唤文案建议 (JSON数组字符串)
     */
    @TableField("call_to_action_suggestions_json")
    @Schema(title = "行动召唤文案建议", description = "JSON数组字符串")
    private String callToActionSuggestionsJson;

    /**
     * 可用于内容创作的故事性切入点 (JSON数组字符串)
     */
    @TableField("storytelling_angles_json")
    @Schema(title = "可用于内容创作的故事性切入点", description = "JSON数组字符串")
    private String storytellingAnglesJson;

    /**
     * 与竞品对比的差异化优势点 (JSON数组对象)
     */
    @TableField("comparison_points_vs_competitors_json")
    @Schema(title = "与竞品对比的差异化优势点", description = "JSON数组对象")
    private String comparisonPointsVsCompetitorsJson;

    /**
     * 建议搭配的社交媒体话题标签 (JSON数组字符串)
     */
    @TableField("associated_hashtags_suggestion_json")
    @Schema(title = "建议搭配的社交媒体话题标签", description = "JSON数组字符串")
    private String associatedHashtagsSuggestionJson;

    /**
     * 针对此产品推荐的内容调性/风格 (JSON数组字符串)
     */
    @TableField("content_tone_style_suggestions_json")
    @Schema(title = "针对此产品推荐的内容调性/风格", description = "JSON数组字符串")
    private String contentToneStyleSuggestionsJson;

    /**
     * 给AI内容创作的特别注意事项或灵感提示
     */
    @TableField("notes_for_ai_content_creation")
    @Schema(title = "给AI内容创作的特别注意事项或灵感提示")
    private String notesForAiContentCreation;

    /**
     * 通用自定义标签 (JSON数组字符串, 运营用)
     */
    @TableField("tags_json")
    @Schema(title = "通用自定义标签", description = "JSON数组字符串, 运营用")
    private String tagsJson;

    /**
     * 是否启用: 1-是, 0-否
     */
    @TableField("is_enabled")
    @Schema(title = "是否启用: 1-是, 0-否")
    private Integer isEnabled;

    /**
     * 当前ai生成任务id
     */
    @TableField(value = "job_id", exist = false)
    @Schema(title = "当前ai对话id", hidden = true)
    private Long jobId;
}
