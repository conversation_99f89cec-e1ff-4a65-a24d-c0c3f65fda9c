package com.daddylab.msaiagent.db.aiAgent.enums;

import com.daddylab.msaiagent.common.bailian.domain.enums.ModuleEnum;
import com.daddylab.msaiagent.common.base.enums.IIntegerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @className BaiLianFileTypeEnum
 * @date 2025/3/4 13:34
 * @description: TODO
 */
@AllArgsConstructor
@Getter
public enum BaiLianFileTypeEnum implements IIntegerEnum {

  // 文件类型 1-稿定模版 2-图片素材
  GD_TEMPLATE(1, "稿定模版", ModuleEnum.GD_TEMPLATE),
  IMAGE_MATERIAL(2, "图片素材", ModuleEnum.IMAGE_MATERIAL),
  KNOWLEDGE_BASE_MATERIAL(3, "知识库素材", ModuleEnum.KNOWLEDGE_BASE_MATERIAL),
  ACTIVITY_MATERIAL(4, "活动信息素材", ModuleEnum.ACTIVITY_MATERIAL),
  ;
  private final Integer value;
  private final String desc;
  private final ModuleEnum moduleEnum;
}
