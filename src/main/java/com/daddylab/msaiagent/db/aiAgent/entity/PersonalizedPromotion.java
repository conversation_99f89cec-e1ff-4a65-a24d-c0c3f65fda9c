package com.daddylab.msaiagent.db.aiAgent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.daddylab.msaiagent.db.base.Entity;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;

/**
 * <p>
 * 人物个性化提升
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
@TableName("personalized_promotion")
public class PersonalizedPromotion extends Entity<PersonalizedPromotion> {

    private static final long serialVersionUID = 1L;

    /**
     * 人物ID，唯一键
     */
    @TableField("person_uuid")
    private String personUuid;

    /**
     * 输出内容
     */
    @TableField("diary_content")
    private String diaryContent;
}
