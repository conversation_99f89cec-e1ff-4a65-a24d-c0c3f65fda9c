package com.daddylab.msaiagent.db.aiAgent.dao.impl;

import com.daddylab.msaiagent.db.aiAgent.entity.PersonaCore;
import com.daddylab.msaiagent.db.aiAgent.mapper.PersonaCoreMapper;
import com.daddylab.msaiagent.db.aiAgent.dao.PersonaCoreDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 虚拟人核心信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Service
public class PersonaCoreDaoImpl extends ServiceImpl<PersonaCoreMapper, PersonaCore> implements PersonaCoreDao {
  @Override
  public PersonaCoreMapper getBaseMapper() {
    return super.getBaseMapper();
  }
}
