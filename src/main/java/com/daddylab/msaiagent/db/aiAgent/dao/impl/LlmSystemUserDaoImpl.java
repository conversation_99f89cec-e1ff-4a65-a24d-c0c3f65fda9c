package com.daddylab.msaiagent.db.aiAgent.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.msaiagent.db.aiAgent.entity.LlmSystemUser;
import com.daddylab.msaiagent.db.aiAgent.mapper.LlmSystemUserMapper;
import com.daddylab.msaiagent.db.aiAgent.dao.LlmSystemUserDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 大模型系统用户定义 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Service
public class LlmSystemUserDaoImpl extends ServiceImpl<LlmSystemUserMapper, LlmSystemUser> implements LlmSystemUserDao {
    @Override
    public LlmSystemUserMapper getBaseMapper() {
        return super.getBaseMapper();
    }

    @Override
    public String systemMessage(String bizKey) {
        LlmSystemUser llmSystemUser = getOne(new QueryWrapper<LlmSystemUser>().eq("biz_key", bizKey));
        if (llmSystemUser == null) {
            return "";
        }
        return llmSystemUser.getSystemMsg();
    }
}
