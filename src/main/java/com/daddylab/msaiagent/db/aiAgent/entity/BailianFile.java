package com.daddylab.msaiagent.db.aiAgent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.daddylab.msaiagent.db.aiAgent.enums.BaiLianFileRemoteStatusEnum;
import com.daddylab.msaiagent.db.aiAgent.enums.BaiLianFileSubTypeEnum;
import com.daddylab.msaiagent.db.aiAgent.enums.BaiLianFileTypeEnum;
import com.daddylab.msaiagent.db.base.Entity;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;

/**
 * <p>
 * 百炼文件处理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
@TableName("bailian_file")
public class BailianFile extends Entity<BailianFile> {

    private static final long serialVersionUID = 1L;

    /**
     * 模块类型 1-稿定模版 2-图片素材 3-知识库素材 4-活动信息素材
     */
    @TableField("type")
    private BaiLianFileTypeEnum type;

    /**
     * 类型ID
     */
    @TableField("type_id")
    private Long typeId;

    /**
     * 1-基础信息 2-文件信息
     */
    @TableField("file_type")
    private BaiLianFileSubTypeEnum fileType;

    @TableField("url")
    private String url;

    /**
     * 关联百炼文件ID
     */
    @TableField("file_id")
    private String fileId;

    /**
     * 文件名
     */
    @TableField("filename")
    private String filename;

    /**
     * 百炼数据文件状态  1-待上传 2-已上传 3-已删除
     */
    @TableField("remote_status")
    private BaiLianFileRemoteStatusEnum remoteStatus;

    /**
     * 最近查询文件的时间
     */
    @TableField("last_search_time")
    private Long lastSearchTime;

    /**
     * 删除标记（标记此文件可以被彻底删除）
     */
    @TableField("delete_flag")
    private Integer deleteFlag;

    /**
     * 引用计数，用于记录当前文档是否正在被索引，为0时，可以删除
     */
    @TableField("ref_count")
    private Integer refCount;


    /**
     * 百炼类目ID
     */
    @TableField("category_id")
    private String categoryId;
}
