package com.daddylab.msaiagent.db.aiAgent.dao.impl;

import com.daddylab.msaiagent.db.aiAgent.entity.RedBookNoteRanking;
import com.daddylab.msaiagent.db.aiAgent.mapper.RedBookNoteRankingMapper;
import com.daddylab.msaiagent.db.aiAgent.dao.RedBookNoteRankingDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 小红书笔记排行 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Service
public class RedBookNoteRankingDaoImpl extends ServiceImpl<RedBookNoteRankingMapper, RedBookNoteRanking> implements RedBookNoteRankingDao {
  @Override
  public RedBookNoteRankingMapper getBaseMapper() {
    return super.getBaseMapper();
  }
}
