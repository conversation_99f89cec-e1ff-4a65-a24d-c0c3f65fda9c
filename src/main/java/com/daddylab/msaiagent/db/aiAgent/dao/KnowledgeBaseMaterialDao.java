package com.daddylab.msaiagent.db.aiAgent.dao;

import com.daddylab.msaiagent.db.aiAgent.entity.KnowledgeBaseMaterial;
import com.baomidou.mybatisplus.extension.service.IService;
import com.daddylab.msaiagent.db.aiAgent.mapper.KnowledgeBaseMaterialMapper;

import java.util.Optional;

/**
 * <p>
 * 知识库素材表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
public interface KnowledgeBaseMaterialDao extends IService<KnowledgeBaseMaterial> {
  @Override
  KnowledgeBaseMaterialMapper getBaseMapper();

  KnowledgeBaseMaterial getByUuid(String uuid);
}
