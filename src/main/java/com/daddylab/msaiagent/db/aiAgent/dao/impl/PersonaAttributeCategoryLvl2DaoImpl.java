package com.daddylab.msaiagent.db.aiAgent.dao.impl;

import com.daddylab.msaiagent.db.aiAgent.entity.PersonaAttributeCategoryLvl2;
import com.daddylab.msaiagent.db.aiAgent.mapper.PersonaAttributeCategoryLvl2Mapper;
import com.daddylab.msaiagent.db.aiAgent.dao.PersonaAttributeCategoryLvl2Dao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 虚拟人二级属性分类表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Service
public class PersonaAttributeCategoryLvl2DaoImpl extends ServiceImpl<PersonaAttributeCategoryLvl2Mapper, PersonaAttributeCategoryLvl2> implements PersonaAttributeCategoryLvl2Dao {
  @Override
  public PersonaAttributeCategoryLvl2Mapper getBaseMapper() {
    return super.getBaseMapper();
  }
}
