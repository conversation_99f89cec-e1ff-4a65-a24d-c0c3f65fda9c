package com.daddylab.msaiagent.db.aiAgent.dao.impl;

import com.daddylab.msaiagent.db.aiAgent.entity.PersonalizedPromotion;
import com.daddylab.msaiagent.db.aiAgent.mapper.PersonalizedPromotionMapper;
import com.daddylab.msaiagent.db.aiAgent.dao.PersonalizedPromotionDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 人物个性化提升 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Service
public class PersonalizedPromotionDaoImpl extends ServiceImpl<PersonalizedPromotionMapper, PersonalizedPromotion> implements PersonalizedPromotionDao {
  @Override
  public PersonalizedPromotionMapper getBaseMapper() {
    return super.getBaseMapper();
  }
}
