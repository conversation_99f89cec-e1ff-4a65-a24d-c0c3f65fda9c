package com.daddylab.msaiagent.db.aiAgent.dao;

import com.daddylab.msaiagent.db.aiAgent.entity.SceneFragment;
import com.baomidou.mybatisplus.extension.service.IService;
import com.daddylab.msaiagent.db.aiAgent.mapper.SceneFragmentMapper;

/**
 * <p>
 * 场景片段库表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
public interface SceneFragmentDao extends IService<SceneFragment> {
  @Override
  SceneFragmentMapper getBaseMapper();
}
