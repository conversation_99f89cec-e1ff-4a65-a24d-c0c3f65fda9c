package com.daddylab.msaiagent.db.aiAgent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.daddylab.msaiagent.db.base.Entity;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;

/**
 * <p>
 * 知识库素材表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
@TableName("knowledge_base_material")
public class KnowledgeBaseMaterial extends Entity<KnowledgeBaseMaterial> {

    private static final long serialVersionUID = 1L;

    /**
     * 素材全局唯一UUID
     */
    @TableField("material_uuid")
    private String materialUuid;

    /**
     * 素材名称(运营用，空为文件名)
     */
    @TableField("material_name")
    private String materialName;

    /**
     * 素材类型,200字内逗号相隔,比如:文章,白皮书
     */
    @TableField("material_type")
    private String materialType;

    /**
     * 描述（运营用）
     */
    @TableField("description")
    private String description;

    /**
     * 原始文件存储URL
     */
    @TableField("original_file_url")
    private String originalFileUrl;

    /**
     * 原始文件富文本
     */
    @TableField("original_content")
    private String originalContent;

    /**
     * 解析后的完整文本内容
     */
    @TableField("parsed_content")
    private String parsedContent;

    /**
     * 结构化切片数据 (格式示例见下方)
     */
    @TableField("content_chunks_json")
    private String contentChunksJson;

    /**
     * 解析状态 0-待处理 1-成功 2-失败
     */
    @TableField("parsing_status")
    private Integer parsingStatus;

    /**
     * 适用场景 (JSON数组: ["推广","知识分享"])
     */
    @TableField("target_application_scenarios_json")
    private String targetApplicationScenariosJson;

    /**
     * 关键词集合 (JSON数组: ["SEO优化","用户体验"])
     */
    @TableField("keywords_json")
    private String keywordsJson;

    /**
     * 核心信息要点 (JSON数组: ["5G技术优势","成本降低20%"])
     */
    @TableField("core_information_points_json")
    private String coreInformationPointsJson;

    /**
     * 参考文献/数据来源链接 (JSON数组)
     */
    @TableField("reference_urls_json")
    private String referenceUrlsJson;

    /**
     * 使用说明 (如适用于技术类内容创作)
     */
    @TableField("usage_guidelines")
    private String usageGuidelines;

    /**
     * 使用限制（如不能用于冬天）
     */
    @TableField("usage_restrictions")
    private String usageRestrictions;

    /**
     * 可能的错误
     */
    @TableField("contain_errors")
    private String containErrors;

    /**
     * 需要提问的问题(JSON数组)
     */
    @TableField("related_questions_json")
    private String relatedQuestionsJson;

    /**
     * 启用状态 1-启用 0-归档
     */
    @TableField("is_enabled")
    private Integer isEnabled;

    /**
     * 生成的会话ID
     */
    @TableField("chat_id")
    private Long chatId;
}
