package com.daddylab.msaiagent.db.aiAgent.dao.impl;

import com.daddylab.msaiagent.db.aiAgent.entity.Prompt;
import com.daddylab.msaiagent.db.aiAgent.mapper.PromptMapper;
import com.daddylab.msaiagent.db.aiAgent.dao.PromptDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 提示词 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Service
public class PromptDaoImpl extends ServiceImpl<PromptMapper, Prompt> implements PromptDao {
  @Override
  public PromptMapper getBaseMapper() {
    return super.getBaseMapper();
  }
}
