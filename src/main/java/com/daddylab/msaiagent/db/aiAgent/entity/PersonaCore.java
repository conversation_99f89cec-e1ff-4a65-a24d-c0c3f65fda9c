package com.daddylab.msaiagent.db.aiAgent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.daddylab.msaiagent.db.base.PureEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;

/**
 * <p>
 * 虚拟人核心信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
@TableName("persona_core")
public class PersonaCore extends PureEntity<PersonaCore> {

    /**
     * 虚拟人全局唯一UUID，用于跨系统关联
     */
    @TableField("persona_uuid")
    private String personaUuid;

    /**
     * 主体类型: 1-INDIVIDUAL(个体), 2-ORGANIZATION(组织/企业), 3-GROUP_ALIAS(团体别名), 4-PRODUCT_IP(产品IP)
     */
    @TableField("subject_type")
    private Integer subjectType;

    /**
     * 状态: 0-DRAFT(草稿) 1-ACTIVE(活跃), 2-INACTIVE(未激活/暂停), 3-PENDING_REVIEW(待审核), 4-ARCHIVED(已归档)
     */
    @TableField("status")
    private Integer status;

    /**
     * 定义来源: 1-AI_GENERATED, 2-OPERATOR_DESIGNED, 3-IMITATIVE_LEARNED
     */
    @TableField("creation_source")
    private Integer creationSource;

    /**
     * 核心定位标签 (JSON数组字符串),用于存储描述该虚拟人最核心、最关键的定位标签,这些标签应该是高度概括性的，能够一眼看出这个虚拟人的大致方向和目标人群如["新手妈妈", "90后职场", "生活美学分享"]等
     */
    @TableField("core_positioning_tags_json")
    private String corePositioningTagsJson;

    /**
     * 主要变现模式代码 (需维护代码表或硬编码约定)1: 商品带货 (直播、短视频、图文)2: 知识付费 (课程、社群、咨询)3: 广告植入/品牌合作4: 直播打赏/虚拟礼物 5: IP授权/衍生品
     */
    @TableField("primary_monetization_model_code")
    private Integer primaryMonetizationModelCode;
}
