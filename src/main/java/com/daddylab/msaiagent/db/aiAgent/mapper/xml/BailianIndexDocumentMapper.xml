<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.msaiagent.db.aiAgent.mapper.BailianIndexDocumentMapper">

    <select id="listByCategoryId" resultType="com.daddylab.msaiagent.db.aiAgent.entity.BailianIndexDocument">
        SELECT bid.*
        FROM `tech`.`bailian_index_document` `bid`
        JOIN tech.`bailian_file` `bf` on bid.`file_id` = bf.`file_id`
        WHERE `index_id` = #{indexId}
        AND bf.`category_id` = #{categoryId}
    </select>
</mapper>
