package com.daddylab.msaiagent.db.aiAgent.dao;

import com.daddylab.msaiagent.db.aiAgent.entity.BailianFile;
import com.baomidou.mybatisplus.extension.service.IService;
import com.daddylab.msaiagent.db.aiAgent.enums.BaiLianFileTypeEnum;
import com.daddylab.msaiagent.db.aiAgent.mapper.BailianFileMapper;

import java.util.List;

/**
 * <p>
 * 百炼文件处理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
public interface BailianFileDao extends IService<BailianFile> {
  @Override
  BailianFileMapper getBaseMapper();

  List<BailianFile> listByTypeId(BaiLianFileTypeEnum type, Long id);

  List<BailianFile> listByTypeIds(BaiLianFileTypeEnum type, List<Long> typeIds);

  List<BailianFile> listByFileId(String fileId);

  boolean updateRefCount(String fileId, long count);

  boolean updateRefCount(List<String> fileIds, long count);
}
