package com.daddylab.msaiagent.db.aiAgent.dao;

import com.daddylab.msaiagent.db.aiAgent.entity.PersonaAttributeCategoryLvl2;
import com.baomidou.mybatisplus.extension.service.IService;
import com.daddylab.msaiagent.db.aiAgent.mapper.PersonaAttributeCategoryLvl2Mapper;

/**
 * <p>
 * 虚拟人二级属性分类表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
public interface PersonaAttributeCategoryLvl2Dao extends IService<PersonaAttributeCategoryLvl2> {
  @Override
  PersonaAttributeCategoryLvl2Mapper getBaseMapper();
}
