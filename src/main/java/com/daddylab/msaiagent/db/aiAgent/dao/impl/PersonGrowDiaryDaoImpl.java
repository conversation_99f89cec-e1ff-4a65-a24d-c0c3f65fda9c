package com.daddylab.msaiagent.db.aiAgent.dao.impl;

import com.daddylab.msaiagent.db.aiAgent.entity.PersonGrowDiary;
import com.daddylab.msaiagent.db.aiAgent.mapper.PersonGrowDiaryMapper;
import com.daddylab.msaiagent.db.aiAgent.dao.PersonGrowDiaryDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <p>
 * 人物成长日记 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Service
public class PersonGrowDiaryDaoImpl extends ServiceImpl<PersonGrowDiaryMapper, PersonGrowDiary> implements PersonGrowDiaryDao {
    @Override
    public PersonGrowDiaryMapper getBaseMapper() {
        return super.getBaseMapper();
    }

    @Override
    public List<PersonGrowDiary> selectLatestDiary(String personUuId, Integer limit) {
        return this.lambdaQuery()
                .eq(PersonGrowDiary::getPersonUuId, personUuId)
                .last("order by id desc limit " + limit)
                .list();
    }
}
