package com.daddylab.msaiagent.db.aiAgent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.daddylab.msaiagent.db.base.Entity;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;

/**
 * <p>
 * 小红书笔记排行
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
@TableName("red_book_note_ranking")
public class RedBookNoteRanking extends Entity<RedBookNoteRanking> {

    private static final long serialVersionUID = 1L;

    /**
     * 排行类型。0：暴增笔记排行榜。1：商业笔记排行，2：低粉爆款排行
     */
    @TableField("ranking_type")
    private Integer rankingType;

    /**
     * 0:家居家装
     */
    @TableField("note_field")
    private Integer noteField;

    /**
     * 统计时间类型。0日榜。1周榜。2月榜。3近24小时。4近3天。5近7天。
     */
    @TableField("statics_time_type")
    private Integer staticsTimeType;

    /**
     * 统计时间
     */
    @TableField("statics_time_val")
    private String staticsTimeVal;

    /**
     * 笔记内容
     */
    @TableField("source_data")
    private String sourceData;

    /**
     * 0:新红数据
     */
    @TableField("source_type")
    private Integer sourceType;

    @TableField("source_id")
    private String sourceId;

    /**
     * 1：单日榜单。2：7日榜单。只对排行类型0类型有效
     */
    @TableField("date_type")
    private Integer dateType;
}
