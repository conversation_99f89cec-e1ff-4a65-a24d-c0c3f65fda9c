package com.daddylab.msaiagent.db.aiAgent.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @className BooleanEnum
 * @date 2024/9/10 14:43
 * @description: TODO
 */
@AllArgsConstructor
@Getter
public enum PromptTypeEnum implements IEnum<Integer> {
    OPTIMIZATION(1,"优化提示词"),
    VIRTUAL_PERSON(2,"虚拟人物"),
    SCENE(3,"场景"),
    TITLE(4,"标题"),
    CONTENT(5,"内容"),
    EXTRACT(6,"提炼记忆体"),
    NOTE_TAG(7,"笔记标签"),
    ;
    private final Integer value;
    private final String desc;
}
