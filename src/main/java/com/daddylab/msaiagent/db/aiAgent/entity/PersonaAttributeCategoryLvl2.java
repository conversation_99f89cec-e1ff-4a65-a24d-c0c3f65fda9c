package com.daddylab.msaiagent.db.aiAgent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.daddylab.msaiagent.db.base.PureEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;

/**
 * <p>
 * 虚拟人二级属性分类表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
@TableName("persona_attribute_category_lvl2")
public class PersonaAttributeCategoryLvl2 extends PureEntity<PersonaAttributeCategoryLvl2> {

    /**
     * 所属一级分类ID
     */
    @TableField("lvl1_category_id")
    private Long lvl1CategoryId;

    /**
     * 二级分类名称
     */
    @TableField("category_name")
    private String categoryName;

    /**
     * 分类描述
     */
    @TableField("description")
    private String description;

    /**
     * 显示顺序
     */
    @TableField("display_order")
    private Integer displayOrder;

    /**
     * 是否启用: 1-是, 0-否
     */
    @TableField("is_enabled")
    private Integer isEnabled;

    /**
     * 适用的主体类型 (JSON数组字符串, 如: "[1,2]"), 空表示通用 (继承一级或独立定义)
     */
    @TableField("applicable_subject_types_json")
    private String applicableSubjectTypesJson;
}
