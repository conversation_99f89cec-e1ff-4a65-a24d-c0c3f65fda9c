package com.daddylab.msaiagent.db.aiAgent.dao;

import com.daddylab.msaiagent.db.aiAgent.entity.RedBook;
import com.baomidou.mybatisplus.extension.service.IService;
import com.daddylab.msaiagent.db.aiAgent.mapper.RedBookMapper;

/**
 * <p>
 * 小红书笔记 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
public interface RedBookDao extends IService<RedBook> {
  @Override
  RedBookMapper getBaseMapper();
}
