package com.daddylab.msaiagent.db.aiAgent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.daddylab.msaiagent.db.base.PureEntity;
import com.daddylab.msaiagent.domain.enums.GrowFrequency;
import com.daddylab.msaiagent.domain.enums.GrowType;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;

/**
 * 虚拟人属性定义表
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
@TableName("persona_attribute_definition")
public class PersonaAttributeDefinition extends PureEntity<PersonaAttributeDefinition> {

  /** 所属二级分类ID */
  @TableField("lvl2_category_id")
  private Integer lvl2CategoryId;

  /** 属性唯一键 (程序用, 如: identity.core_tags) */
  @TableField("attribute_key")
  private String attributeKey;

  /** 属性显示名称 (UI用) */
  @TableField("attribute_name")
  private String attributeName;

  /** 属性描述和填写指南 */
  @TableField("description")
  private String description;

  /** 数据类型代码 (见备注) */
  @TableField("data_type_code")
  private Integer dataTypeCode;

  /** 是否允许多值: 1-是 (通常对应 STRING_ARRAY, JSON_ARRAY), 0-否 */
  @TableField("is_multi_value")
  private Integer isMultiValue;

  /** 默认值 (字符串形式存储) */
  @TableField("default_value_str")
  private String defaultValueStr;

  /** 针对哪些主体类型是必填 (JSON数组字符串, 如: "[1]表示对个体必填") */
  @TableField("is_required_for_subject_types_json")
  private String isRequiredForSubjectTypesJson;

  /** 校验规则 (JSON字符串, 如: {"regex": "^\d+$", "maxLength": 10, "options": ["A","B","C"]}) */
  @TableField("validation_rules_json")
  private String validationRulesJson;

  /** 显示顺序 */
  @TableField("display_order")
  private Integer displayOrder;

  /** 是否启用: 1-是, 0-否 */
  @TableField("is_enabled")
  private Integer isEnabled;

  /** 主要存储位置提示代码 (见备注) */
  @TableField("storage_location_hint_code")
  private Integer storageLocationHintCode;

  /** 适用的主体类型 (JSON数组字符串, 如: "[1,2]"), NULL或空表示通用 (继承二级或独立定义) */
  @TableField("applicable_subject_types_json")
  private String applicableSubjectTypesJson;

  /** 成长类型。1.成长型。2.状态型。3.静态型 */
  private GrowType growType;

  /** 属性更新条件 */
  private String growCondition;

  /** 更新触发条件和典型频率. 1.月检，2.季度检，3.半年检，4.年检，5.事件触发。6.手动调整。 */
  private GrowFrequency growFrequency;
}
