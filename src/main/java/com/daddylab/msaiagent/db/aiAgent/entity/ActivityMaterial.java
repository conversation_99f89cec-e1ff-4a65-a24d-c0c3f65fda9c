package com.daddylab.msaiagent.db.aiAgent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.daddylab.msaiagent.db.base.Entity;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;

/**
 * <p>
 * 活动信息素材表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
@TableName("activity_material")
public class ActivityMaterial extends Entity<ActivityMaterial> {

    private static final long serialVersionUID = 1L;

    /**
     * 素材全局唯一UUID
     */
    @TableField("material_uuid")
    private String materialUuid;

    /**
     * 素材名称
     */
    @TableField("material_name")
    private String materialName;

    /**
     * 素材类型,200字内逗号相隔,比如:电商促销,线下活动
     */
    @TableField("material_type")
    private String materialType;

    /**
     * 活动扶持政策（含补贴规则/激励措施）
     */
    @TableField("support_policy")
    private String supportPolicy;

    /**
     * 活动开始时间（时间戳）
     */
    @TableField("start_time")
    private Long startTime;

    /**
     * 活动结束时间（时间戳）
     */
    @TableField("end_time")
    private Long endTime;

    /**
     * 原始文件存储URL
     */
    @TableField("original_file_url")
    private String originalFileUrl;

    /**
     * 原始文件富文本
     */
    @TableField("original_content")
    private String originalContent;

    /**
     * 解析后的完整文本内容
     */
    @TableField("parsed_content")
    private String parsedContent;

    /**
     * 结构化切片数据 (格式示例见下方)
     */
    @TableField("content_chunks_json")
    private String contentChunksJson;

    /**
     * 解析状态 0-待处理 1-成功 2-失败
     */
    @TableField("parsing_status")
    private Integer parsingStatus;

    /**
     * 适用场景 ["直播带货","门店运营"]
     */
    @TableField("target_application_scenarios_json")
    private String targetApplicationScenariosJson;

    /**
     * 关键词 ["满减优惠","限时折扣"]
     */
    @TableField("keywords_json")
    private String keywordsJson;

    /**
     * 使用说明（如仅限新用户）
     */
    @TableField("usage_guidelines")
    private String usageGuidelines;

    /**
     * 使用限制（如不可与其他活动叠加）
     */
    @TableField("usage_restrictions")
    private String usageRestrictions;

    /**
     * 需要提问的问题(JSON数组["如何领取优惠券？"])
     */
    @TableField("related_questions_json")
    private String relatedQuestionsJson;

    /**
     * 启用状态 1-启用 0-归档
     */
    @TableField("is_enabled")
    private Integer isEnabled;
}
