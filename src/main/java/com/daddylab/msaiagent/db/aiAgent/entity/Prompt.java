package com.daddylab.msaiagent.db.aiAgent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.daddylab.msaiagent.db.aiAgent.enums.BooleanEnum;
import com.daddylab.msaiagent.db.aiAgent.enums.PromptTypeEnum;
import com.daddylab.msaiagent.db.base.Entity;

import java.io.Serial;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;

/**
 * <p>
 * 提示词
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
@TableName("prompt")
public class Prompt extends Entity<Prompt> {

    private static final long serialVersionUID = 1L;

    /**
     * 模版ID
     */
    @TableField("prompt_template_id")
    private Long promptTemplateId;

    /**
     * 提示词标题
     */
    @TableField("title")
    private String title;

    /**
     * 提示词类型 1-优化提示词 2-构建虚拟人物 3-构建场景 4-构建标题 5-构建内容 6-提炼记忆体
     */
    @TableField("type")
    private PromptTypeEnum type;

    /**
     * 是否使用
     */
    @TableField("active")
    private BooleanEnum active;

    /**
     * 提示词内容(markdown)
     */
    @TableField("content")
    private String content;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}
