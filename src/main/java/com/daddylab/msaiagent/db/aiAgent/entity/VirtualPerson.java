package com.daddylab.msaiagent.db.aiAgent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.daddylab.msaiagent.db.aiAgent.enums.BuildStatusEnum;
import com.daddylab.msaiagent.db.base.Entity;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;

/**
 * <p>
 * 虚拟人物
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-09
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
@TableName("virtual_person")
public class VirtualPerson extends Entity<VirtualPerson> {

    private static final long serialVersionUID = 1L;

    /**
     * 提示词ID
     */
    @TableField("prompt_id")
    private Long promptId;

    /**
     * 提示词内容
     */
    @TableField("prompt_content")
    private String promptContent;

    /**
     * 虚拟人物名称
     */
    @TableField("name")
    private String name;

    /**
     * 头像
     */
    @TableField("avatar")
    private String avatar;

    /**
     * 性别 0-未知 1-男 2-女
     */
    @TableField("sex")
    private Integer sex;

    /**
     * 年龄
     */
    @TableField("age")
    private Integer age;

    /**
     * 省
     */
    @TableField("province")
    private String province;

    /**
     * 市
     */
    @TableField("city")
    private String city;

    /**
     * 生日
     */
    @TableField("birthday")
    private String birthday;

    /**
     * 人物标签
     */
    @TableField("tags")
    private String tags;

    /**
     * 场景状态 0-待生成 1-生成中 2-生成完成
     */
    @TableField("status")
    private BuildStatusEnum status;

    /**
     * 使用的模型
     */
    @TableField("model")
    private String model;

    /**
     * 外貌特征
     */
    @TableField("appearance")
    private String appearance;

    /**
     * 性格特征
     */
    @TableField("`character`")
    private String character;

    /**
     * 背景故事
     */
    @TableField("background")
    private String background;

    /**
     * 成就和经历
     */
    @TableField("experience")
    private String experience;

    /**
     * 人际关系
     */
    @TableField("relation")
    private String relation;

    /**
     * 价值观
     */
    @TableField("`values`")
    private String values;

    /**
     * 兴趣爱好
     */
    @TableField("hobby")
    private String hobby;

    /**
     * 未来展望
     */
    @TableField("future")
    private String future;

    /**
     * 生成人物描述
     */
    @TableField("content")
    private String content;
}
