package com.daddylab.msaiagent.db.aiAgent.dao.impl;

import com.daddylab.msaiagent.db.aiAgent.entity.Scene;
import com.daddylab.msaiagent.db.aiAgent.mapper.SceneMapper;
import com.daddylab.msaiagent.db.aiAgent.dao.SceneDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 场景 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
@Service
public class SceneDaoImpl extends ServiceImpl<SceneMapper, Scene> implements SceneDao {
  @Override
  public SceneMapper getBaseMapper() {
    return super.getBaseMapper();
  }
}
