package com.daddylab.msaiagent.db.aiAgent.dao;

import com.daddylab.msaiagent.db.aiAgent.entity.ModelChat;
import com.baomidou.mybatisplus.extension.service.IService;
import com.daddylab.msaiagent.db.aiAgent.mapper.ModelChatMapper;

/**
 * <p>
 * 模型对话记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
public interface ModelChatDao extends IService<ModelChat> {
  @Override
  ModelChatMapper getBaseMapper();
}
