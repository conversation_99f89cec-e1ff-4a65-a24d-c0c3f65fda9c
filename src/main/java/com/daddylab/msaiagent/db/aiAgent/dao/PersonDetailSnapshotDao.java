package com.daddylab.msaiagent.db.aiAgent.dao;

import com.daddylab.msaiagent.db.aiAgent.entity.PersonDetailSnapshot;
import com.baomidou.mybatisplus.extension.service.IService;
import com.daddylab.msaiagent.db.aiAgent.mapper.PersonDetailSnapshotMapper;

/**
 * <p>
 * 虚拟人详情快照 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
public interface PersonDetailSnapshotDao extends IService<PersonDetailSnapshot> {
  @Override
  PersonDetailSnapshotMapper getBaseMapper();
}
