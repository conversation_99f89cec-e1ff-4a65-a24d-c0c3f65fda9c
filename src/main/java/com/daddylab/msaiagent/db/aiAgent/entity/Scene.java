package com.daddylab.msaiagent.db.aiAgent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.daddylab.msaiagent.db.base.Entity;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;

/**
 * <p>
 * 场景
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
@TableName("scene")
public class Scene extends Entity<Scene> {

    private static final long serialVersionUID = 1L;

    /**
     * 虚拟人物ID
     */
    @TableField("virtual_person_id")
    private Long virtualPersonId;

    /**
     * 提示词ID
     */
    @TableField("prompt_id")
    private Long promptId;

    /**
     * 提示词内容
     */
    @TableField("prompt_content")
    private String promptContent;

    /**
     * 场景标题
     */
    @TableField("title")
    private String title;

    /**
     * 场景标签
     */
    @TableField("tags")
    private String tags;

    /**
     * 场景状态 0-待生成 1-生成中 2-生成完成
     */
    @TableField("status")
    private Integer status;

    /**
     * 使用的模型
     */
    @TableField("model")
    private String model;

    /**
     * 生成场景内容
     */
    @TableField("content")
    private String content;
}
