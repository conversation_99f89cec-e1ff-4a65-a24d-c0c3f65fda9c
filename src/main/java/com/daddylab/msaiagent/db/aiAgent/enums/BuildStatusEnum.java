package com.daddylab.msaiagent.db.aiAgent.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @className BooleanEnum
 * @date 2024/9/10 14:43
 * @description: TODO
 */
@AllArgsConstructor
@Getter
public enum BuildStatusEnum implements IEnum<Integer> {
    WAIT(0,"待生成"),
    UNDERWAY(1,"生成中"),
    SUCCESS(2,"生成成功"),
    ;
    private final Integer value;
    private final String desc;
}
