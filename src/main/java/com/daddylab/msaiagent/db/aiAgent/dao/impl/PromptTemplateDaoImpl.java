package com.daddylab.msaiagent.db.aiAgent.dao.impl;

import com.daddylab.msaiagent.db.aiAgent.entity.PromptTemplate;
import com.daddylab.msaiagent.db.aiAgent.mapper.PromptTemplateMapper;
import com.daddylab.msaiagent.db.aiAgent.dao.PromptTemplateDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 提示词模版 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
@Service
public class PromptTemplateDaoImpl extends ServiceImpl<PromptTemplateMapper, PromptTemplate> implements PromptTemplateDao {
  @Override
  public PromptTemplateMapper getBaseMapper() {
    return super.getBaseMapper();
  }
}
