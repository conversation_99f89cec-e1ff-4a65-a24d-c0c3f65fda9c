package com.daddylab.msaiagent.db.aiAgent.mapper;

import com.daddylab.msaiagent.db.aiAgent.entity.BailianIndexDocument;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
* <p>
* 百炼文件索引下的文档列表 Mapper 接口
* </p>
*
* <AUTHOR>
* @since 2025-05-26
*/
public interface BailianIndexDocumentMapper extends BaseMapper<BailianIndexDocument> {
    List<BailianIndexDocument> listByCategoryId(
            @Param("indexId") String indexId, @Param("categoryId") String categoryId);
}
