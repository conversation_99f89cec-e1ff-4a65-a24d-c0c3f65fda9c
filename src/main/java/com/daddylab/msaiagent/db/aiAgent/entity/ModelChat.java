package com.daddylab.msaiagent.db.aiAgent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.daddylab.msaiagent.db.base.Entity;
import java.io.Serializable;

import com.daddylab.msaiagent.db.base.PureEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;

/**
 * <p>
 * 模型对话记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
@TableName("model_chat")
public class ModelChat extends PureEntity<ModelChat> {

    private static final long serialVersionUID = 1L;

    /**
     * 对话ID
     */
    @TableField("chat_id")
    private String chatId;

    /**
     * 模型名称
     */
    @TableField("model_name")
    private String modelName;

    /**
     * 对话类型
     */
    @TableField("chat_type")
    private String chatType;

    /**
     * 状态: 1对话中，2已结束
     */
    @TableField("status")
    private Integer status;

    /**
     * 对话内容
     */
    @TableField("content")
    private String content;

    /**
     * 异常信息
     */
    @TableField("error_content")
    private String errorContent;
}
