package com.daddylab.msaiagent.db.aiAgent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.daddylab.msaiagent.db.base.PureEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;

/**
 * <p>
 * 场景片段库表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
@TableName("scene_fragment")
public class SceneFragment extends PureEntity<SceneFragment> {

    private static final long serialVersionUID = 1L;

    /**
     * 场景片段全局唯一UUID
     */
    @TableField("fragment_uuid")
    private String fragmentUuid;

    /**
     * 场景片段名称 (运营用)
     */
    @TableField("fragment_name")
    private String fragmentName;

    /**
     * 场景片段简要描述
     */
    @TableField("description")
    private String description;

    /**
     * 场景片段分类代码 (见备注: GENERAL, PERSONA_SPECIFIC, KNOWLEDGE_BASED, PRODUCT_FOCUSED)
     */
    @TableField("category_type_code")
    private Integer categoryTypeCode;

    /**
     * 虚拟人标签适用性规则 (JSON对象, 用于PERSONA_SPECIFIC类型)
     */
    @TableField("persona_tag_applicability_json")
    private String personaTagApplicabilityJson;

    /**
     * 适用的主体类型代码数组 (JSON, 关联 persona_core.subject_type, NULL或空表示通用)
     */
    @TableField("subject_type_applicability_json")
    private String subjectTypeApplicabilityJson;

    /**
     * 文化背景标签数组 (JSON, 如: ["中国传统节日", "日韩潮流"])
     */
    @TableField("cultural_tags_json")
    private String culturalTagsJson;

    /**
     * 地域特定性标签数组 (JSON, 如: ["北京胡同", "上海弄堂"])
     */
    @TableField("region_specificity_tags_json")
    private String regionSpecificityTagsJson;

    /**
     * 结构化的场景要素 (JSON对象, 包含time, location, activity, mood, key_objects, sensory_details等建议)
     */
    @TableField("scene_elements_json")
    private String sceneElementsJson;

    /**
     * 核心的、可直接用于提示词的场景描述性文本或指令片段 (可包含参数化占位符)
     */
    @TableField("output_prompt_segment")
    private String outputPromptSegment;

    /**
     * 引导互动的文本模板 (可选, 可包含参数化占位符)
     */
    @TableField("interaction_prompt_template")
    private String interactionPromptTemplate;

    /**
     * 是否侧重互动: 1-是, 0-否
     */
    @TableField("is_interactive_focused")
    private Integer isInteractiveFocused;

    /**
     * 可扩展点定义 (JSON, 结构化描述output_prompt_segment中的可扩展变量及其引导)
     */
    @TableField("expansion_points_definition_json")
    private String expansionPointsDefinitionJson;

    /**
     * 情感基调标签数组 (JSON, 如: ["温馨", "愉悦", "轻微焦虑"])
     */
    @TableField("emotional_tone_tags_json")
    private String emotionalToneTagsJson;

    /**
     * 叙事功能标签数组 (JSON, 如: ["引入问题", "展示转变"])
     */
    @TableField("narrative_function_tags_json")
    private String narrativeFunctionTagsJson;

    /**
     * 时效性标签数组 (JSON, 如: ["春节", "#某热点"])
     */
    @TableField("timeliness_tags_json")
    private String timelinessTagsJson;

    /**
     * 时效性生效起始时间戳 (秒, NULL表示无限制)
     */
    @TableField("valid_from")
    private Long validFrom;

    /**
     * 时效性生效结束时间戳 (秒, NULL表示无限制)
     */
    @TableField("valid_to")
    private Long validTo;

    /**
     * 独特性评级 (1-5, 5为最独特)
     */
    @TableField("uniqueness_rating")
    private Integer uniquenessRating;

    /**
     * 使用频率计数器
     */
    @TableField("usage_frequency_counter")
    private Integer usageFrequencyCounter;

    /**
     * 触发此片段的关键词/标签数组 (JSON, 用于检索)
     */
    @TableField("trigger_keywords_json")
    private String triggerKeywordsJson;

    /**
     * 使用此片段的建议或限制
     */
    @TableField("usage_guidelines_or_constraints")
    private String usageGuidelinesOrConstraints;

    /**
     * 是否启用: 1-是, 0-否
     */
    @TableField("is_enabled")
    private Integer isEnabled;

    /**
     * 当前ai生成任务id
     */
    @TableField("job_id")
    private Long jobId;
}
