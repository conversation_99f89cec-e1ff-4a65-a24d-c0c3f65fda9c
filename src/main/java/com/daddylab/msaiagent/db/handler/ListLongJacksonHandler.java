package com.daddylab.msaiagent.db.handler;

import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.daddylab.msaiagent.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

/**
 *
 * @className ListLongJacksonHandler
 * <AUTHOR>
 * @date 2024/4/28 14:43
 * @description: mybatis plus 查询为List<Long>时，使用该handler处理
 */
@Slf4j
@MappedTypes({Object.class})
@MappedJdbcTypes(JdbcType.VARCHAR)
public class ListLongJacksonHandler extends AbstractJsonTypeHandler<Object> {

    public ListLongJacksonHandler(Class<?> type) {
        super(type);
    }

    @Override
    public Object parse(String json) {
        return JsonUtil.parseObjectList(json, Long.class);
    }

    @Override
    public String toJson(Object obj) {
        return JsonUtil.toJSONString(obj);
    }

}