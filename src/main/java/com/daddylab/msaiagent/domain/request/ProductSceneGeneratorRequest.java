package com.daddylab.msaiagent.domain.request;

import com.daddylab.msaiagent.db.aiAgent.entity.ProductMaterial;
import com.daddylab.msaiagent.domain.enums.SceneCategoryType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Schema(title = "产品场景生成请求", description = "请求生成产品使用场景")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductSceneGeneratorRequest {
    @Schema(title = "场景名称初步想法")
    private String sceneNameIdea;

    @Schema(title = "场景分类代码建议")
    private SceneCategoryType sceneCategorySuggestionCode;

    @Schema(title = "场景分类名称建议")
    private String sceneCategoryNameSuggestion;

    @Schema(title = "适用的主体类型代码数组")
    private List<Integer> subjectTypeApplicabilityTargetJson;

    @Schema(title = "场景核心描述")
    private String sceneCoreDescriptionNaturalLanguage;

    @Schema(title = "目标平台提示")
    private String targetPlatformHint;

    @Schema(title = "期望的情感基调标签数组")
    private List<String> desiredEmotionalTonesJson;

    @Schema(title = "期望的叙事功能标签数组")
    private List<String> desiredNarrativeFunctionsJson;

    @Schema(title = "用户期望包含的关键要素")
    private List<String> keyElementsToIncludeJson;

    @Schema(title = "给AI的额外说明")
    private String additionalNotesForAi;

    @Schema(title = "产品素材ID")
    @NotNull(message = "产品素材ID不能为空")
    private Long productMaterialId;

}
