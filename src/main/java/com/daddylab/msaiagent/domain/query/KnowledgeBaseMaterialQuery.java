package com.daddylab.msaiagent.domain.query;


import com.alibaba.cola.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value="KnowledgeBaseMaterialQuery", description="知识库素材分页参数")
public class KnowledgeBaseMaterialQuery extends PageQuery {

    @ApiModelProperty(value = "素材名称")
    private String materialName;
    @ApiModelProperty(value = "素材类型")
    private String materialType;
    @ApiModelProperty(value = "素材状态")
    private Integer isEnabled;
    @ApiModelProperty(value = "创建日期开始")
    private Long createdAtStart;
    @ApiModelProperty(value = "创建日期结束")
    private Long createdAtEnd;

}
