package com.daddylab.msaiagent.domain.query;

import com.alibaba.cola.dto.PageQuery;
import com.baomidou.mybatisplus.annotation.TableField;
import com.daddylab.msaiagent.common.base.dto.BasePageQuery;
import com.daddylab.msaiagent.domain.enums.SceneCategoryType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(title = "场景片段查询", description = "场景片段查询")
public class SceneFragmentQuery extends BasePageQuery {

    /** 场景片段全局唯一UUID */
    @Schema(title = "场景片段全局唯一UUID", description = "")
    @TableField("fragment_uuid")
    private String fragmentUuid;

    /** 场景片段名称 (运营用) */
    @Schema(title = "场景片段名称", description = "建议的片段名称")
    @TableField("fragment_name")
    private String fragmentName;

    /** 场景片段简要描述 */
    @Schema(title = "场景片段简要描述", description = "建议的片段描述", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("description")
    private String description;

    /** 场景片段分类代码 (见备注: GENERAL, PERSONA_SPECIFIC, KNOWLEDGE_BASED, PRODUCT_FOCUSED) */
    @Schema(title = "场景片段分类代码", description = "最终确认或建议的分类代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("category_type_code")
    private SceneCategoryType categoryTypeCode;
}
