package com.daddylab.msaiagent.domain.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "KnowledgeBaseMaterialEditForm", description = "知识库素材编辑事件表单")
public class KnowledgeBaseMaterialEditForm {
    @NotBlank(message = "素材UUID不能为空")
    @ApiModelProperty("素材UUID")
    private String materialUuid;

    @NotBlank(message = "修正字段不能为空")
    @ApiModelProperty("修正字段")
    private String fixField;

    @NotBlank(message = "修正内容不能为空")
    @ApiModelProperty("修正内容")
    private String fixContent;
}

