package com.daddylab.msaiagent.domain.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "KnowledgeBaseMaterialEnableForm", description = "知识库素材启用禁用事件表单")
public class KnowledgeBaseMaterialEnableForm {
    @ApiModelProperty(value = "素材UUID")
    private String materialUuid;
    @ApiModelProperty("启用状态 1-启用 0-停用")
    private Integer isEnabled;

}

