package com.daddylab.msaiagent.domain.form;

import com.daddylab.msaiagent.common.bailian.domain.enums.ModuleEnum;
import com.daddylab.msaiagent.db.aiAgent.enums.BaiLianFileTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 *
 * @className BailianFileUploadForm
 * <AUTHOR>
 * @date 2025/5/27 09:36
 * @description: TODO 
 */
@ApiModel(value = "BailianFileUploadForm", description = "百链文件上传表单")
@Data
public class BailianFileUploadForm {

    @NotEmpty(message = "文件类型不能为空")
    @ApiModelProperty("文件类型")
    private BaiLianFileTypeEnum fileType;

    @NotEmpty(message = "文件地址不能为空")
    @ApiModelProperty("oss的文件地址")
    private List<String> ossUrlList;
}
