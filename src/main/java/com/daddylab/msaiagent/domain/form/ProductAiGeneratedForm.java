package com.daddylab.msaiagent.domain.form;

import lombok.Data;

import java.io.Serializable;

@Data
public class ProductAiGeneratedForm implements Serializable {
    private String productName;

    //产品描述: 运营人员可以输入产品的详细描述，AI会根据这个描述生成相关内容
    private String productDetail;

    //核心卖点: 运营人员可以预先输入他们认为最重要的几个核心卖点，每个卖点一行或用分号隔开
    private String coreSellingPoints;

    //目标人群关键词: 运营可以输入一些目标人群的关键词，如“敏感肌”、“学生党”、“熬夜党”
    private String targetKeywords;

    //素材简要描述强调的重点、或者不希望AI关注的方面
    private String additionalNotesForAi;

    //用户输入的使用方法和技巧
    private String instructionsForUseBrief;
}
