package com.daddylab.msaiagent.domain.form;

import com.baomidou.mybatisplus.annotation.TableField;
import com.daddylab.msaiagent.domain.enums.PersonCreationSource;
import com.daddylab.msaiagent.domain.enums.PersonPrimaryMonetizationModelEnum;
import com.daddylab.msaiagent.domain.enums.PersonSubjectTypeEnum;
import lombok.Data;

@Data
public class PersonCreateForm {


    /**
     * 主体类型: 1-INDIVIDUAL(个体), 2-ORGANIZATION(组织/企业), 3-GROUP_ALIAS(团体别名), 4-PRODUCT_IP(产品IP)
     */
    private PersonSubjectTypeEnum subjectType;

    /**
     * 定义来源: 1-AI_GENERATED, 2-OPERATOR_DESIGNED, 3-IMITATIVE_LEARNED
     */
    private PersonCreationSource creationSource;

    /**
     * 主要变现模式代码 (需维护代码表或硬编码约定)1: 商品带货 (直播、短视频、图文)2: 知识付费 (课程、社群、咨询)3: 广告植入/品牌合作4: 直播打赏/虚拟礼物 5: IP授权/衍生品
     */
    @TableField("primary_monetization_model_code")
    private PersonPrimaryMonetizationModelEnum primaryMonetizationModel;

}
