package com.daddylab.msaiagent.domain.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "KnowledgeBaseMaterialFixForm", description = "知识库素材修正事件表单")
public class KnowledgeBaseMaterialFixForm {
    @ApiModelProperty("素材UUID")
    private String materialUuid;
    @ApiModelProperty("回答内容")
    private List<String> answer;
}

