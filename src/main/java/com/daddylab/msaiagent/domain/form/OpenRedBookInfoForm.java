package com.daddylab.msaiagent.domain.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 *
 * @className OpenRedBookInfoForm
 * <AUTHOR>
 * @date 2025/5/26 16:28
 * @description: TODO 
 */
@ApiModel(value = "OpenRedBookInfoForm", description = "小红书详情数据")
@Data
public class OpenRedBookInfoForm {

    @NotEmpty(message = "地址不能为空")
    @ApiModelProperty("笔记地址")
    private String url;
}
