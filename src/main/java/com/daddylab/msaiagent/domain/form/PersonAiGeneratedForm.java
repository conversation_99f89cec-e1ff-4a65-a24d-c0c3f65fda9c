package com.daddylab.msaiagent.domain.form;

import com.daddylab.msaiagent.domain.enums.PersonPrimaryMonetizationModelEnum;
import com.daddylab.msaiagent.domain.enums.PersonSubjectTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class PersonAiGeneratedForm implements Serializable {
    private PersonSubjectTypeEnum subjectType;
    private PersonPrimaryMonetizationModelEnum primaryMonetizationModel;
    //文本框 (如果选择了与商品相关的变现模式)：“主要带货品类/方向描述”。
    private String primaryMonetizationDescription;

    private Map<String,String> initQuestionAndAnswer;
    /*
    //问题1: 虚拟人的核心身份与角色是什么？ (必填)
    //引导文字/Placeholder： “请用一句话或几个关键词描述TA的主要身份标签、职业或核心特点。例如：‘一个热爱分享育儿经验的双胞胎妈妈’，‘对美食和旅行充满热情的自由撰稿人’，‘喜欢用代码解决生活难题的程序员’。”
    private String coreIdentityAndRole;

    *//*问题2: 这个虚拟人的核心理念/故事起点/独特之处是什么？ (必填)
    引导文字/Placeholder： “请描述TA与众不同的地方、核心价值观、创立账号的初衷，或一个能代表其特点的简短背景故事/经历。例如：‘因为在养育双胞胎过程中积累了大量实战经验，希望能帮助同样处境的父母’，‘TA坚信生活中的每个小确幸都值得记录和分享’，‘一次说走就走的旅行彻底改变了TA对生活的看法，从此爱上了探索世界’。”*//*
    private String coreConceptStory;

    *//*问题3: 这个虚拟人希望通过内容实现什么目标？为用户带来什么价值？ (必填)
    引导文字/Placeholder： “请说明TA希望通过发布的内容达到什么具体目标，能为关注者提供什么样的帮助、解决什么问题或带来什么体验。例如：‘提供实用的双胞胎养育技巧和资源，减轻新手父母的焦虑’，‘分享独特视角的旅行攻略和美食体验，激发大家对生活的热爱’，‘用通俗易懂的方式科普编程知识，帮助初学者入门’。”*//*
    private String contentGoalAndValue;*/

    /*运营自由描述 (推荐填写 - 此项保持，用于补充细节)：
    大型文本区域：“关于这个虚拟人，您还有哪些更具体的想法、灵感、故事细节、性格特点、或不希望出现的点？（请自由发挥，越详细越有助于AI理解和创作独特的画像）”*/
    private String additionalDescription;
}
