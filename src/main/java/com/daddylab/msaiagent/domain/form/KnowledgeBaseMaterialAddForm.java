package com.daddylab.msaiagent.domain.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "KnowledgeBaseMaterialAddForm", description = "知识库素材新增事件表单")
public class KnowledgeBaseMaterialAddForm {
    @ApiModelProperty("素材名称")
    private String materialName;
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("原始文件上传URL")
    private List<String> originalFileUrl;
    @ApiModelProperty("原始富文本")
    private String originalContent;

}

