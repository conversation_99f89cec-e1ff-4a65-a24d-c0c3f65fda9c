package com.daddylab.msaiagent.domain.form;

import com.daddylab.msaiagent.db.aiAgent.enums.BaiLianFileTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 *
 * @className BailianFileUploadForm
 * <AUTHOR>
 * @date 2025/5/27 09:36
 * @description: TODO 
 */
@ApiModel(value = "BailianRetrieveForm", description = "百链知识库检索")
@Data
public class BailianRetrieveForm {


    @NotEmpty(message = "文件类型不能为空")
    @ApiModelProperty("文件类型")
    private BaiLianFileTypeEnum baiLianFileTypeEnum;

    @NotEmpty(message = "检索关键字不能为空")
    @ApiModelProperty("检索关键字")
    private String keyword;

    @ApiModelProperty("召回数量(默认20)")
    private Integer size = 20;

}
