package com.daddylab.msaiagent.domain.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "KnowledgeBaseMaterialAIForm", description = "知识库素材AI生成事件表单")
public class KnowledgeBaseMaterialAIForm {
    @ApiModelProperty(value = "素材UUID")
    private String materialUuid;
    @ApiModelProperty("用户提示词")
    private String prompt;

}

