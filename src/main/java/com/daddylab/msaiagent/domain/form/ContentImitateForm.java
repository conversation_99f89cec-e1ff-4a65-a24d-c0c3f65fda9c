package com.daddylab.msaiagent.domain.form;

import com.google.j2objc.annotations.AutoreleasePool;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.checkerframework.checker.units.qual.A;

/**
 * <AUTHOR> up
 * @date 2025年05月19日 10:25 AM
 */
@Data
@ApiModel("内容仿写参数")
public class ContentImitateForm {

    /**
     * 核心仿写指令 (任务定义)
     */
    @ApiModelProperty("核心仿写指令 (任务定义)")
    private String coreInstruction;

    /**
     * 示范内容
     */
    @ApiModelProperty("范文")
    private String modelContent;


    // ----- 内容与主题深度定制
    // 新主题
    @ApiModelProperty("内容与主题深度定制-新主题阐述")
    private String newTitle;
    @ApiModelProperty("内容与主题深度定制-内容信息点")
    private String infoPoint;
    // 细节
    @ApiModelProperty("内容与主题深度定制-内容细节")
    private String detail;
    // 视角
    @ApiModelProperty("内容与主题深度定制-内容视角")
    private String angle;
    // 切入点
    @ApiModelProperty("内容与主题深度定制-内容切入点")
    private String breakthroughPoint;
    // 创新
    @ApiModelProperty("内容与主题深度定制-内容创新")
    private String innovate;
    // 和范围的差异
    @ApiModelProperty("内容与主题深度定制-内容差异化（相对于范文）")
    private String different;

    // 风格、语气与情感表达的精细调控
    // 语气
    @ApiModelProperty("风格、语气与情感表达的精细调控-语气词典")
    private String tone;
    // 语言特色
    @ApiModelProperty("风格、语气与情感表达的精细调控-语言特色")
    private String linguisticFeature;
    // emoji策略
    @ApiModelProperty("风格、语气与情感表达的精细调控-Emoji使用策略")
    private String emojiStrategy;
    // 真实感
    @ApiModelProperty("风格、语气与情感表达的精细调控-真实感")
    private String senseOfReality;
    // 信任度
    @ApiModelProperty("风格、语气与情感表达的精细调控-信任度")
    private String trustDegree;

    // 结构、排版与视觉引导
    @ApiModelProperty("结构、排版与视觉引导-标题创作进阶")
    private String titleDefinition;
    // 段落排布
    @ApiModelProperty("结构、排版与视觉引导-段落排布与逻辑")
    private String paragraphArrangement;
    // 突出的重点
    @ApiModelProperty("结构、排版与视觉引导-重点突出方式")
    private String prominentFocus;
    // 结尾设计
    @ApiModelProperty("结构、排版与视觉引导-结尾设计")
    private String tailDesign;
    @ApiModelProperty("结构、排版与视觉引导-Hashtag策略")
    private String hashtagStrategy;


    // 目标受众画像细化
    @ApiModelProperty("目标受众画像细化")
    private String targetPortrait;


    // 负面约束与排除项
    @ApiModelProperty("负面约束与排除项")
    private String negativeConstraint;


}

