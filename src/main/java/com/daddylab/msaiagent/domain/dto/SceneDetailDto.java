package com.daddylab.msaiagent.domain.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SceneDetailDto implements Serializable {

    /**
     * AI建议的片段名称，简洁明了
     */
    private String fragmentName;

    /**
     * 对这个场景片段草稿的简要描述
     */
    private String description;

    /**
     * AI建议的场景分类代码: 1-GENERAL, 2-PERSONA_SPECIFIC, 3-KNOWLEDGE_BASED, 4-PRODUCT_FOCUSED
     */
    private Integer categoryTypeCode;

    /**
     * 适用的主体类型代码，应与输入的subject_type_code一致或为其子集
     */
    private List<Integer> subjectTypeApplicabilityJson;

    /**
     * 如果category_type_code是PERSONA_SPECIFIC(2)，尝试根据输入信息给出虚拟人标签适用性规则的建议
     */
    private PersonaTagApplicability personaTagApplicabilityJson;

    /**
     * 文化标签，如无则为[]
     */
    private List<String> culturalTagsJson;

    /**
     * 地域标签，如无则为[]
     */
    private List<String> regionSpecificityTagsJson;

    /**
     * 包含time, location, activity, mood, key_objects, sensory_details等建议
     */
    private SceneElements sceneElementsJson;

    /**
     * 核心的、生动的、具体的场景描述性文本片段，可包含占位符
     */
    private String outputPromptSegment;

    /**
     * 引导互动的文本模板，如果适用，否则为null
     */
    private String interactionPromptTemplate;

    /**
     * 0或1, 根据是否包含互动模板或描述中的互动倾向判断
     */
    private Integer isInteractiveFocused;

    /**
     * 如果output_prompt_segment中有明确的、复杂的、需要进一步定义的扩展点
     */
    private Map<String, ExpansionPoint> expansionPointsDefinitionJson;

    /**
     * 情感标签，如无则为[]
     */
    private List<String> emotionalToneTagsJson;

    /**
     * 叙事功能标签，如无则为[]
     */
    private List<String> narrativeFunctionTagsJson;

    /**
     * 时效性标签，如无则为[]
     */
    private List<String> timelinessTagsJson;

    /**
     * 1-5, AI基于内容独特性给出的初步评级
     */
    private Integer uniquenessRating;

    /**
     * 触发关键词，AI从描述中提取或联想，如无则为[]
     */
    private List<String> triggerKeywordsJson;

    /**
     * 使用建议或限制，如果适用，否则为null
     */
    private String usageGuidelinesOrConstraints;

    /**
     * 虚拟人标签适用性规则
     */
    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class PersonaTagApplicability implements Serializable {
        /**
         * 匹配任意核心标签
         */
        private List<String> matchAnyOfCoreTags;

        /**
         * 匹配所有档案标签
         */
        private List<String> matchAllOfProfileTags;

        /**
         * 排除包含任意标签
         */
        private List<String> excludeIfHasAnyTags;
    }

    /**
     * 场景元素
     */
    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class SceneElements implements Serializable {
        /**
         * 时间描述建议
         */
        private List<String> timeSuggestions;

        /**
         * 地点描述建议
         */
        private List<String> locationSuggestions;

        /**
         * 活动描述建议
         */
        private List<String> activitySuggestions;

        /**
         * 氛围/情绪词建议
         */
        private List<String> moodAtmosphereSuggestions;

        /**
         * 关键物品建议
         */
        private List<String> keyObjectsPropsSuggestions;

        /**
         * 感官细节建议
         */
        private List<String> sensoryDetailsSuggestions;
    }

    /**
     * 扩展点定义
     */
    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class ExpansionPoint implements Serializable {
        /**
         * 描述这个扩展点是什么
         */
        private String description;

        /**
         * 期望的输入类型
         */
        private String expectedInputType;
    }
}
