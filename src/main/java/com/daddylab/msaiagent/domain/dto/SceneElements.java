package com.daddylab.msaiagent.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "场景的关键组成要素")
public class SceneElements {
    @Schema(description = "时间建议", defaultValue = "[]")
    private List<String> timeSuggestions;

    @Schema(description = "地点建议", defaultValue = "[]")
    private List<String> locationSuggestions;

    @Schema(description = "活动建议", defaultValue = "[]")
    private List<String> activitySuggestions;

    @Schema(description = "情绪氛围建议", defaultValue = "[]")
    private List<String> moodAtmosphereSuggestions;

    @Schema(description = "关键物品/道具建议", defaultValue = "[]")
    private List<String> keyObjectsPropsSuggestions;

    @Schema(description = "感官细节建议", defaultValue = "[]")
    private List<String> sensoryDetailsSuggestions;
}
