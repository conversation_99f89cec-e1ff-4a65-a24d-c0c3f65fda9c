package com.daddylab.msaiagent.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(title = "虚拟人标签适用性规则")
public class PersonaTagApplicability {
    @Schema(description = "满足其中一个核心标签即可")
    private List<String> matchAnyOfCoreTags;

    @Schema(description = "必须同时拥有这些画像标签")
    private List<String> matchAllOfProfileTags;

    @Schema(description = "如果有这些标签则不适用")
    private List<String> excludeIfHasAnyTags;
}
