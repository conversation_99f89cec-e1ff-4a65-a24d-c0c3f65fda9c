package com.daddylab.msaiagent.domain.dto;

import com.daddylab.msaiagent.common.bailian.domain.enums.BaiLianFileEventTypeEnum;
import com.daddylab.msaiagent.common.lock.DistributedLockKey;
import com.daddylab.msaiagent.db.aiAgent.enums.BaiLianFileTypeEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.*;
import lombok.Builder.Default;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @className BaiLianFileDealDTO
 * <AUTHOR>
 * @date 2025/3/4 13:33
 * @description: TODO
 */
@AllArgsConstructor(staticName = "of")
@NoArgsConstructor
@Data
@Builder
public class BaiLianFileDealDTO implements Serializable {
  private String filename;
  @DistributedLockKey
  @NotNull private BaiLianFileTypeEnum type;
  @DistributedLockKey @NotNull private Long typeId;
  // subType是基础信息时,这里存储基础信息
  private Map<String, String> baseInfo;
  // 文件的oss地址
  private List<String> ossUrl;
  @NotNull private BaiLianFileEventTypeEnum event;
  @Singular private List<String> tags = new ArrayList<>();
  private String baseInfoText;
  @Default private String baseInfoFormat = "txt";

  public static BaiLianFileDealDTOBuilder builder(
      BaiLianFileEventTypeEnum event, String filename, BaiLianFileTypeEnum type, Long typeId) {
    return new BaiLianFileDealDTOBuilder()
        .event(event)
        .filename(filename)
        .type(type)
        .typeId(typeId);
  }

  public static BaiLianFileDealDTOBuilder create(
      String filename, BaiLianFileTypeEnum type, Long typeId) {
    return builder(BaiLianFileEventTypeEnum.CREATE, filename, type, typeId);
  }

  public static BaiLianFileDealDTOBuilder updateBase(
      String filename, BaiLianFileTypeEnum type, Long typeId) {
    return builder(BaiLianFileEventTypeEnum.UPDATE_BASE, filename, type, typeId);
  }

  public static BaiLianFileDealDTOBuilder updateAll(
      String filename, BaiLianFileTypeEnum type, Long typeId) {
    return builder(BaiLianFileEventTypeEnum.UPDATE_ALL, filename, type, typeId);
  }

  public static BaiLianFileDealDTOBuilder delete(
       BaiLianFileTypeEnum type, Long typeId) {
    return builder(BaiLianFileEventTypeEnum.DELETE, null,type, typeId);
  }

  public static BaiLianFileDealDTO ofCreateText(
      BaiLianFileTypeEnum type,
      Long typeId,
      String filename,
      String baseInfoText,
      List<String> ossUrl) {
    BaiLianFileDealDTO baiLianFileDealDTO = new BaiLianFileDealDTO();
    baiLianFileDealDTO.setFilename(filename);
    baiLianFileDealDTO.setType(type);
    baiLianFileDealDTO.setTypeId(typeId);
    baiLianFileDealDTO.setBaseInfoText(baseInfoText);
    baiLianFileDealDTO.setOssUrl(ossUrl);
    baiLianFileDealDTO.setEvent(BaiLianFileEventTypeEnum.CREATE);
    return baiLianFileDealDTO;
  }

  public static BaiLianFileDealDTO ofUpdateText(
      BaiLianFileTypeEnum type,
      Long typeId,
      String filename,
      String baseInfoText,
      List<String> ossUrl) {
    BaiLianFileDealDTO baiLianFileDealDTO = new BaiLianFileDealDTO();
    baiLianFileDealDTO.setFilename(filename);
    baiLianFileDealDTO.setType(type);
    baiLianFileDealDTO.setTypeId(typeId);
    baiLianFileDealDTO.setBaseInfoText(baseInfoText);
    baiLianFileDealDTO.setOssUrl(ossUrl);
    baiLianFileDealDTO.setEvent(BaiLianFileEventTypeEnum.UPDATE_ALL);
    return baiLianFileDealDTO;
  }

  public static BaiLianFileDealDTO ofCreate(
      BaiLianFileTypeEnum type,
      Long typeId,
      String filename,
      Map<String, String> baseInfo,
      List<String> ossUrl) {
    BaiLianFileDealDTO baiLianFileDealDTO = new BaiLianFileDealDTO();
    baiLianFileDealDTO.setFilename(filename);
    baiLianFileDealDTO.setType(type);
    baiLianFileDealDTO.setTypeId(typeId);
    baiLianFileDealDTO.setBaseInfo(baseInfo);
    baiLianFileDealDTO.setOssUrl(ossUrl);
    baiLianFileDealDTO.setEvent(BaiLianFileEventTypeEnum.CREATE);
    return baiLianFileDealDTO;
  }

  public static BaiLianFileDealDTO ofAllUpdate(
      BaiLianFileTypeEnum type,
      Long typeId,
      String filename,
      Map<String, String> baseInfo,
      List<String> ossUrl) {
    BaiLianFileDealDTO baiLianFileDealDTO = new BaiLianFileDealDTO();
    baiLianFileDealDTO.setFilename(filename);
    baiLianFileDealDTO.setType(type);
    baiLianFileDealDTO.setTypeId(typeId);
    baiLianFileDealDTO.setBaseInfo(baseInfo);
    baiLianFileDealDTO.setOssUrl(ossUrl);
    baiLianFileDealDTO.setEvent(BaiLianFileEventTypeEnum.UPDATE_ALL);
    return baiLianFileDealDTO;
  }

  public static BaiLianFileDealDTO ofUpdateBase(
      BaiLianFileTypeEnum type, Long typeId, Map<String, String> baseInfo) {
    BaiLianFileDealDTO baiLianFileDealDTO = new BaiLianFileDealDTO();
    baiLianFileDealDTO.setFilename("");
    baiLianFileDealDTO.setType(type);
    baiLianFileDealDTO.setTypeId(typeId);
    baiLianFileDealDTO.setBaseInfo(baseInfo);
    baiLianFileDealDTO.setOssUrl(Collections.emptyList());
    baiLianFileDealDTO.setEvent(BaiLianFileEventTypeEnum.UPDATE_BASE);
    return baiLianFileDealDTO;
  }

  public static BaiLianFileDealDTO ofDelete(BaiLianFileTypeEnum type, Long typeId) {
    BaiLianFileDealDTO baiLianFileDealDTO = new BaiLianFileDealDTO();
    baiLianFileDealDTO.setFilename("");
    baiLianFileDealDTO.setType(type);
    baiLianFileDealDTO.setTypeId(typeId);
    baiLianFileDealDTO.setBaseInfo(Maps.newHashMap());
    baiLianFileDealDTO.setOssUrl(Lists.newArrayList());
    baiLianFileDealDTO.setEvent(BaiLianFileEventTypeEnum.DELETE);
    return baiLianFileDealDTO;
  }
}
