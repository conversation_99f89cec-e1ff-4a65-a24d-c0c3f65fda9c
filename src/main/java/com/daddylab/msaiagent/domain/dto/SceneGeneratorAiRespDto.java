package com.daddylab.msaiagent.domain.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SceneGeneratorAiRespDto implements Serializable {
    double aiConfidenceScore;
    List<String> clarificationQuestions;
    List<String> suggestionsForNextIteration;
    SceneDetailDto generatedSceneFragmentDraft;
    SceneDetailDto updatedSceneDescription;
}
