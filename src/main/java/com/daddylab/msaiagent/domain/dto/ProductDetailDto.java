package com.daddylab.msaiagent.domain.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ProductDetailDto implements Serializable {

    /**
     * 素材简要描述 (运营用)
     */
    private String description;

    /**
     * 商品官方名称/常用名
     */
    private String productName;

    /**
     * 商品分类标签 (JSON数组字符串)
     */
    private List<String> productCategoryTagsJson;

    /**
     * 核心卖点总列表 (JSON数组字符串, 作为所有卖点的集合)
     */
    private List<String> coreSellingPointsJson;

    /**
     * 目标人群细分-痛点-解决方案关联 (JSON数组对象, 结构详见下方注释)
     */
    private List<TargetedPainPointsSolution> targetedSegmentsPainPointsSolutionsJson;

    /**
     * 商品总体目标受众概括描述 (可选, 作为补充)
     */
    private String targetAudienceGeneralDescription;

    /**
     * 典型使用场景示例 (JSON数组字符串)
     */
    private List<String> usageScenarioExamplesJson;

    /**
     * 用户体验相关的关键词/标签 (JSON数组字符串)
     */
    private List<String> userExperienceKeywordsJson;

    /**
     * 简要使用方法或技巧
     */
    private String instructionsForUseBrief;

    /**
     * 产品规格参数 (JSON对象)
     */
    private Map<String,String> productSpecificationsJson;

    /**
     * 外观描述关键词 (JSON数组字符串)
     */
    private List<String> appearanceDescriptionKeywordsJson;

    /**
     * 行动召唤文案建议 (JSON数组字符串)
     */
    private List<String> callToActionSuggestionsJson;

    /**
     * 可用于内容创作的故事性切入点 (JSON数组字符串)
     */
    private List<String> storytellingAnglesJson;

    /**
     * 与竞品对比的差异化优势点 (JSON数组对象)
     */
    private List<Comparison> comparisonPointsVsCompetitorsJson;

    /**
     * 建议搭配的社交媒体话题标签 (JSON数组字符串)
     */
    private List<String> associatedHashtagsSuggestionJson;

    /**
     * 针对此产品推荐的内容调性/风格 (JSON数组字符串)
     */
    private List<String> contentToneStyleSuggestionsJson;

    /**
     * 给AI内容创作的特别注意事项或灵感提示
     */
    private String notesForAiContentCreation;

    /**
     * 通用自定义标签 (JSON数组字符串, 运营用)
     */
    private List<String> tagsJson;

    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class Comparison implements Serializable{
        String competitorName;
        String ourAdvantage;
    }

    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class TargetedPainPointsSolution implements Serializable{
        String segmentName;
        String segmentDescription;
        List<PainPoint> painPoints;
        List<SellPoint> relevantSellingPoints;
        List<String> contentAngleSuggestionsForSegment;
    }

    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class PainPoint implements Serializable{
        String painPointId;
        String description;
        List<String> keywords;
    }
    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class SellPoint implements Serializable{
        String sellingPointId;
        String description;
        List<String> solvesPainPointIds;
        List<String> keywords;
    }
}
