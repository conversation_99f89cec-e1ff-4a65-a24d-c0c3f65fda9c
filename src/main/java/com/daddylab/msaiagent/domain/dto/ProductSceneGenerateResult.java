package com.daddylab.msaiagent.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.daddylab.msaiagent.db.base.PureEntity;
import com.daddylab.msaiagent.db.handler.ListStringJacksonHandler;
import com.daddylab.msaiagent.domain.enums.PersonSubjectTypeEnum;
import com.daddylab.msaiagent.domain.enums.SceneCategoryType;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 场景片段库表
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
@Schema(title = "产品使用场景生成结果", description = "调用大模型返回的产品使用场景生成结果")
public class ProductSceneGenerateResult implements Serializable {

    @Serial private static final long serialVersionUID = 1L;

    /** 场景片段名称 (运营用) */
    @Schema(title = "场景片段名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String fragmentName;

    /** 场景片段简要描述 */
    @Schema(
            title = "场景片段简要描述",
            description = "建议的片段描述",
            requiredMode = Schema.RequiredMode.REQUIRED)
    private String description;

    /** 场景片段分类代码 (见备注: GENERAL, PERSONA_SPECIFIC, KNOWLEDGE_BASED, PRODUCT_FOCUSED) */
    @Schema(title = "场景片段分类", requiredMode = Schema.RequiredMode.REQUIRED)
    private SceneCategoryType categoryTypeCode;

    /** 虚拟人标签适用性规则 (JSON对象, 用于PERSONA_SPECIFIC类型) */
    @Schema(title = "虚拟人标签适用性规则", requiredMode = Schema.RequiredMode.REQUIRED)
    private PersonaTagApplicability personaTagApplicabilityJson;

    /** 适用的主体类型代码数组 (JSON, 关联 persona_core.subject_type, NULL或空表示通用) */
    @Schema(title = "适用的主体类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<PersonSubjectTypeEnum> subjectTypeApplicabilityJson;

    /** 文化背景标签数组 (JSON, 如: ["中国传统节日", "日韩潮流"]) */
    @Schema(
            title = "文化背景标签数组",
            example = "[\"中国传统节日\", \"日韩潮流\"]",
            requiredMode = Schema.RequiredMode.REQUIRED)
    private String culturalTagsJson;

    /** 地域特定性标签数组 (JSON, 如: ["北京胡同", "上海弄堂"]) */
    @Schema(
            title = "地域特定性标签数组",
            example = "[\"北京胡同\", \"上海弄堂\"]",
            requiredMode = Schema.RequiredMode.REQUIRED)
    private String regionSpecificityTagsJson;

    /** 结构化的场景要素 (JSON对象, 包含time, location, activity, mood, key_objects, sensory_details等建议) */
    @Schema(
            title = "结构化的场景要素",
            description = "包含时间、地点、活动、情绪氛围、关键物品/道具、感官细节等建议",
            requiredMode = Schema.RequiredMode.REQUIRED)
    private SceneElements sceneElementsJson;

    /** 核心的、可直接用于提示词的场景描述性文本或指令片段 (可包含参数化占位符) */
    @Schema(
            title = "场景描述性文本指令片段",
            description = "核心的、可直接用于提示词的场景描述性文本或指令片段 (可包含参数化占位符)",
            requiredMode = Schema.RequiredMode.REQUIRED)
    private String outputPromptSegment;

    /** 引导互动的文本模板 (可选, 可包含参数化占位符) */
    @Schema(
            title = "引导互动的文本模板",
            description = "可选, 可包含参数化占位符",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String interactionPromptTemplate;

    /** 是否侧重互动: 1-是, 0-否 */
    @Schema(
            title = "是否侧重互动",
            description = "根据是否包含互动模板或描述中的互动倾向判断",
            requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isInteractiveFocused;

    /** 可扩展点定义 (JSON, 结构化描述output_prompt_segment中的可扩展变量及其引导) */
    @Schema(
            title = "可扩展点定义",
            description =
                    "结构化描述output_prompt_segment中的可扩展变量及其引导（如果`output_prompt_segment`设计了复杂扩展点）",
            requiredMode = Schema.RequiredMode.REQUIRED)
    private String expansionPointsDefinitionJson;

    /** 情感基调标签数组 (JSON, 如: ["温馨", "愉悦", "轻微焦虑"]) */
    @Schema(
            title = "情感基调标签数组",
            example = "[\"温馨\", \"愉悦\", \"轻微焦虑\"]",
            requiredMode = Schema.RequiredMode.REQUIRED)
    private String emotionalToneTagsJson;

    /** 叙事功能标签数组 (JSON, 如: ["引入问题", "展示转变"]) */
    @Schema(
            title = "叙事功能标签数组",
            example = "[\"引入问题\", \"展示转变\"]",
            requiredMode = Schema.RequiredMode.REQUIRED)
    private String narrativeFunctionTagsJson;

    /** 时效性标签数组 (JSON, 如: ["春节", "#某热点"]) */
    @Schema(
            title = "时效性标签数组",
            example = "[\"春节\", \"#某热点\"]",
            requiredMode = Schema.RequiredMode.REQUIRED)
    private String timelinessTagsJson;

    /** 独特性评级 (1-5, 5为最独特) */
    @Schema(
            title = "独特性评级",
            minimum = "1",
            maximum = "5",
            description = "评估此场景的独特性，1-5, 5为最独特",
            requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer uniquenessRating;

    /** 使用频率计数器 */
    @Schema(title = "使用频率计数器", description = "用于评估此场景的使用频率")
    private Integer usageFrequencyCounter;

    /** 触发此片段的关键词/标签数组 (JSON, 用于检索) */
    @Schema(title = "基于描述和内容生成的触发词", requiredMode = Schema.RequiredMode.REQUIRED)
    private String triggerKeywordsJson;

    /** 使用此片段的建议或限制 */
    @Schema(title = "使用此片段的建议或限制", requiredMode = Schema.RequiredMode.REQUIRED)
    private String usageGuidelinesOrConstraints;
}
