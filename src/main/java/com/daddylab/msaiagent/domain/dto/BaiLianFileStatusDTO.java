package com.daddylab.msaiagent.domain.dto;

import com.daddylab.msaiagent.common.lock.DistributedLockKey;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * @className BaiLianFileDealDTO
 * <AUTHOR>
 * @date 2025/3/4 13:33
 * @description: TODO 
 */
@AllArgsConstructor(staticName = "of")
@NoArgsConstructor
@Data
public class BaiLianFileStatusDTO implements Serializable {
    @DistributedLockKey
    private Long id;
    private String fileId;
}
