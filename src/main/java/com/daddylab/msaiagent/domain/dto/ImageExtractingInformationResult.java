package com.daddylab.msaiagent.domain.dto;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Model class for Image Prompt Analysis
 * Represents the structure of the image analysis response as defined in ImagePrompt
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ImageExtractingInformationResult {
    
    /**
     * Overall scene description
     */
    private OverallSceneDescription overallSceneDescription;
    
    /**
     * Objects and entities identified in the image
     */
    private List<ObjectEntity> objectsAndEntities;
    
    /**
     * Setting and environment details
     */
    private SettingAndEnvironment settingAndEnvironment;
    
    /**
     * Text and symbols found in the image
     */
    private TextAndSymbols textAndSymbols;
    
    /**
     * Composition and perspective details
     */
    private CompositionAndPerspective compositionAndPerspective;
    
    /**
     * Actions and interactions in the image
     */
    private ActionsAndInteractions actionsAndInteractions;
    
    /**
     * Contextual understanding and inferences
     */
    private ContextualUnderstanding contextualUnderstandingAndInferences;
    
    /**
     * Details and subtleties in the image
     */
    private DetailsAndSubtleties detailsAndSubtleties;
    
    /**
     * Potential ambiguities in the image
     */
    private String potentialAmbiguities;
    
    /**
     * Key information summary
     */
    private List<String> keyInformationSummary;
    
    /**
     * Image category
     */
    private String imageCategory;
    
    /**
     * Overall scene description
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OverallSceneDescription {
        private String summary;
        private String mainSubjects;
        private String mood;
    }
    
    /**
     * Object or entity in the image
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ObjectEntity {
        private String type;
        private String description;
        private Attributes attributes;
        private String brandingOrLogo;
        
        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class Attributes {
            private String color;
            private String size;
            private String shape;
            private String texture;
            private String material;
        }
    }
    
    /**
     * Setting and environment details
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SettingAndEnvironment {
        private String location;
        private String backgroundElements;
        private String foregroundElements;
        private String lightingConditions;
        private String timeAndWeather;
    }
    
    /**
     * Text and symbols found in the image
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TextAndSymbols {
        private List<String> text;
        private List<Symbol> symbolsAndIcons;
        
        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class Symbol {
            private String symbol;
            private String meaning;
        }
    }
    
    /**
     * Composition and perspective details
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CompositionAndPerspective {
        private String cameraAngle;
        private String elementArrangement;
        private String focalPoint;
        private String compositionTechniques;
    }
    
    /**
     * Actions and interactions in the image
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ActionsAndInteractions {
        private String actions;
        private String interactions;
        private String motionOrStillness;
    }
    
    /**
     * Contextual understanding and inferences
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ContextualUnderstanding {
        private String purposeOrContext;
        private String inferences;
        private String culturalOrHistoricalElements;
        private String outsideFrameOrNext;
    }
    
    /**
     * Details and subtleties in the image
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DetailsAndSubtleties {
        private String notableDetails;
        private String anomaliesOrUnusualElements;
    }
}
