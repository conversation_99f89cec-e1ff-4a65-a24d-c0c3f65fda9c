package com.daddylab.msaiagent.domain.dto;


import com.daddylab.msaiagent.db.aiAgent.entity.PersonaAttributeCategoryLvl2;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class PersonCategoryDto implements Serializable {

    private Long lvl1CategoryId;

    private String categoryName;

    private String description;

    private List<Lvl2CategoryVO> lvl2Categories;

    @Data
    public static class Lvl2CategoryVO implements Serializable {
        private Long lvl2CategoryId;

        private String lvl2CategoryName;

        private String description;
    }

    public void addLvl2Category(PersonaAttributeCategoryLvl2 lvl2Category) {
        if (this.lvl2Categories == null) {
            this.lvl2Categories = new ArrayList<>();
        }
        Lvl2CategoryVO vo = new Lvl2CategoryVO();
        vo.setLvl2CategoryId(lvl2Category.getId());
        vo.setLvl2CategoryName(lvl2Category.getCategoryName());
        vo.setDescription(lvl2Category.getDescription());
        this.lvl2Categories.add(vo);
    }
}
