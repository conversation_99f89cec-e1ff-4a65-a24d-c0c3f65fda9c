package com.daddylab.msaiagent.domain.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PersonGeneratorAiRespDto implements Serializable {

    Map<String,Object> updatedPersonaAttributes;
    Map<String,Object> personaAttributes;
    List<String> clarificationQuestions;
    List<String> updateNotes;
    List<SuggestedNewAttribute> suggestedNewPersonaAttributesDefinition;
    List<SuggestedNewCategory> suggestedNewCategories;

    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class SuggestedNewCategory implements Serializable {
        private int level;
        private String suggestedName;
        private String description;
        private long parentLvl1CategoryIdIfLvl2;
    }

    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class SuggestedNewAttribute implements Serializable {
        private int belongsToLvl2CategoryId;
        private String suggestedAttributeKey;
        private String suggestedAttributeName;
        private String suggestedDescription;
        private int suggestedDataTypeCode;
        private int suggestedIsMultiValue;
        private int suggestedStorageLocationHintCode;
        private String valueToFill;
    }
}
