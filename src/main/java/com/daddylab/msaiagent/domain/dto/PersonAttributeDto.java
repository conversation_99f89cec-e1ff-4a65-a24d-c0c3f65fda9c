package com.daddylab.msaiagent.domain.dto;


import com.daddylab.msaiagent.db.aiAgent.entity.PersonaAttributeDefinition;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class PersonAttributeDto implements Serializable {

    public PersonAttributeDto(PersonaAttributeDefinition attributeDefinition) {
        this.lvl2CategoryId = attributeDefinition.getLvl2CategoryId();
        this.attributeKey = attributeDefinition.getAttributeKey();
        this.attributeName = attributeDefinition.getAttributeName();
        this.description = attributeDefinition.getDescription();
        this.dataTypeCode = attributeDefinition.getDataTypeCode();
        this.isMultiValue = attributeDefinition.getIsMultiValue();
        this.validationRulesJson = attributeDefinition.getValidationRulesJson();
        this.storageLocationHintCode = attributeDefinition.getStorageLocationHintCode();
    }

    /**
     * 所属二级分类ID
     */
    private Integer lvl2CategoryId;

    /**
     * 属性唯一键 (程序用, 如: identity.core_tags)
     */
    private String attributeKey;

    /**
     * 属性显示名称 (UI用)
     */
    private String attributeName;

    /**
     * 属性描述和填写指南
     */
    private String description;

    /**
     * 数据类型代码 (见备注)
     */
    private Integer dataTypeCode;

    /**
     * 是否允许多值: 1-是 (通常对应 STRING_ARRAY, JSON_ARRAY), 0-否
     */
    private Integer isMultiValue;

    /**
     * 校验规则 (JSON字符串, 如: {"regex": "^\d+$", "maxLength": 10, "options": ["A","B","C"]})
     */
    private String validationRulesJson;

    /**
     * 主要存储位置提示代码 (见备注)
     */
    private Integer storageLocationHintCode;

}
