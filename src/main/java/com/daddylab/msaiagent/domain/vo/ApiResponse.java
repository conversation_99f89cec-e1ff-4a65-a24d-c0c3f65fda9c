package com.daddylab.msaiagent.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Class  MiniResponse (接口统一数据返回)
 */
@ApiModel(description = "统一返回模型")
@Data
public class ApiResponse<T> implements Serializable {

    public static final int SUCCESSFUL_CODE = 0;

    public static final String SUCCESSFUL_MSG = "success";

    @ApiModelProperty(value = "状态码")
    private Integer code;

    @ApiModelProperty(value = "描述信息")
    private String msg;

    @ApiModelProperty(value = "返回数据")
    private T data;

    public ApiResponse(Integer code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    /**
     * 成功并返回结果数据
     *
     * @param data 返回数据
     * @return MiniResponse
     */
    public static <E> ApiResponse<E> success(E data) {
        return new ApiResponse<>(SUCCESSFUL_CODE, SUCCESSFUL_MSG, data);
    }

    /**
     * 成功
     *
     * @return MiniResponse
     */
    public static <E> ApiResponse<E> success() {
        return success(null);
    }

    /**
     * 系统异常类并返回自定义描述信息
     *
     * @param errorCode 错误码
     * @param msg      描述信息
     * @return MiniResponse
     */
    public static <E> ApiResponse<E> fail(int errorCode, String msg) {
        return new ApiResponse<>(errorCode, msg,null);
    }

    /**
     * 系统异常类并返回自定义描述信息
     *
     * @param msg      描述信息
     * @return MiniResponse
     */
    public static <E> ApiResponse<E> fail(String msg) {
        return new ApiResponse<>(500, msg,null);
    }
}
