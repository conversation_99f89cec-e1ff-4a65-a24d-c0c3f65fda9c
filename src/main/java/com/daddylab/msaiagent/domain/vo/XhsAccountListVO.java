package com.daddylab.msaiagent.domain.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 *
 * @className XhsAccountListForm
 * <AUTHOR>
 * @date 2025/4/29 15:59
 * @description: TODO 
 */
@NoArgsConstructor
@Data
public class XhsAccountListVO {

    @JsonProperty("accountId")
    private String accountId;
    @JsonProperty("accountName")
    private String accountName;
    @JsonProperty("contents")
    private List<Contents> contents;
    @JsonProperty("contents")
    private String cookie;

    @NoArgsConstructor
    @Data
    public static class Contents {
        @JsonProperty("url")
        private String url;
        @JsonProperty("title")
        private String title;
    }
}
