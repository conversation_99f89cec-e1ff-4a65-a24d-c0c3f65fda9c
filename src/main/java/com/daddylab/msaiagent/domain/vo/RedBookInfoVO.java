package com.daddylab.msaiagent.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.daddylab.msaiagent.db.aiAgent.enums.BooleanEnum;
import com.daddylab.msaiagent.db.handler.ListStringJacksonHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * @className RedBookInfoVO
 * <AUTHOR>
 * @date 2025/5/26 16:30
 * @description: TODO 
 */
@Data
@ApiModel(value = "RedBookInfoVO", description = "小红书笔记信息")
public class RedBookInfoVO {

    @ApiModelProperty("账号ID")
    private String accountId;

    @ApiModelProperty("账号加密ID")
    private String accountEncryptId;

    @ApiModelProperty("账号名称")
    private String accountName;

    @ApiModelProperty("笔记ID")
    private String noteId;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("笔记内容")
    private String content;

    @ApiModelProperty("笔记类型 1-图文 2-视频")
    private Integer type;

    @ApiModelProperty("笔记详情链接")
    private String url;

    @ApiModelProperty("标签")
    private List<String> tags;

    @ApiModelProperty("图片地址")
    private List<String> images;

    @ApiModelProperty("视频地址")
    private List<String> videos;

    @ApiModelProperty("点赞数量")
    private String upvoteNum;

    @ApiModelProperty("收藏数量")
    private String collectNum;

    @ApiModelProperty("评论数量")
    private String discussNum;

    @ApiModelProperty("笔记的原数据")
    private String sourceData;
}
