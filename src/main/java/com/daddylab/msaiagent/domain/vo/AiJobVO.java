package com.daddylab.msaiagent.domain.vo;

import com.daddylab.msaiagent.db.aiAgent.entity.AigcJob;
import lombok.Data;

import java.io.Serializable;

@Data
public class AiJobVO  implements Serializable {
    private long jobId;
    private String jobType;
    private long bizId;
    private int jobStatus;

    public static AiJobVO fromJob(AigcJob job) {
        AiJobVO vo = new AiJobVO();
        vo.setJobId(job.getId());
        vo.setJobType(job.getJobType());
        vo.setBizId(job.getBizId());
        vo.setJobStatus(job.getStatus());
        return vo;
    }
}
