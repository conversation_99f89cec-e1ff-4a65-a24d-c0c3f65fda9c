package com.daddylab.msaiagent.domain.vo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value="KnowledgeBaseMaterialPageVO", description="知识库素材列表分页VO")
public class KnowledgeBaseMaterialPageVO implements Serializable {
    @ApiModelProperty("主键ID")
    private Long id;
    @ApiModelProperty("素材UUID")
    private String materialUuid;
    @ApiModelProperty("素材名称")
    private String materialName;
    @ApiModelProperty("素材类型")
    private String materialType;
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("解析状态 0-待处理 1-成功 2-失败")
    private Integer parsingStatus;
    @ApiModelProperty("启用状态 1-启用 0-停用")
    private Integer isEnabled;


}
