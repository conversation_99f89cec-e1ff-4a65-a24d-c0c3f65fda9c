package com.daddylab.msaiagent.domain.vo;

import com.aliyun.oss.HttpMethod;
import com.daddylab.msaiagent.common.oss.models.OssBucket;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/9/20
 */
@Data
public class OssSignCommand {
  @ApiModelProperty(value = "请求方法", required = true, notes = "只支持 GET,PUT")
  @NotNull
  private HttpMethod method = HttpMethod.GET;

  @ApiModelProperty(value = "bucket", required = true)
  @NotNull
  private OssBucket bucket;

  @ApiModelProperty(value = "资源路径", required = true)
  @NotEmpty
  private String path;

  @ApiModelProperty(value = "资源类型", notes = "上传文件时必填")
  private String contentType;

  @ApiModelProperty(value = "附件名称", notes = "下载文件名称")
  private String attachmentFilename;
}
