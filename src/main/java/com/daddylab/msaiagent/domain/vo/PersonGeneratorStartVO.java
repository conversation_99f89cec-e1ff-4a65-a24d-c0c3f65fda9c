package com.daddylab.msaiagent.domain.vo;

import com.daddylab.msaiagent.domain.enums.PersonSubjectTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class PersonGeneratorStartVO implements Serializable {

    List<BaseQuestionVO> questions;


    private final static String coreIdentityAndRole = "虚拟人的核心身份与角色是什么？";
    private final static String coreIdentityAndRolePlaceholder = "“请用一句话或几个关键词描述TA的主要身份标签、职业或核心特点。例如：‘一个热爱分享育儿经验的双胞胎妈妈’，‘对美食和旅行充满热情的自由撰稿人’，‘喜欢用代码解决生活难题的程序员’。”";
    private final static String coreConceptStory = "这个虚拟人的核心理念/故事起点/独特之处是什么？";
    private final static String coreConceptStoryPlaceholder = "“请描述TA与众不同的地方、核心价值观、创立账号的初衷，或一个能代表其特点的简短背景故事/经历。例如：‘因为在养育双胞胎过程中积累了大量实战经验，希望能帮助同样处境的父母’，‘TA坚信生活中的每个小确幸都值得记录和分享’，‘一次说走就走的旅行彻底改变了TA对生活的看法，从此爱上了探索世界’。”";
    private final static String contentGoalAndValue = "这个虚拟人希望通过内容实现什么目标？为用户带来什么价值？";
    private final static String contentGoalAndValuePlaceholder = "“请说明TA希望通过发布的内容达到什么具体目标，能为关注者提供什么样的帮助、解决什么问题或带来什么体验。例如：‘提供实用的双胞胎养育技巧和资源，减轻新手父母的焦虑’，‘分享独特视角的旅行攻略和美食体验，激发大家对生活的热爱’，‘用通俗易懂的方式科普编程知识，帮助初学者入门’。”";

    private final static String groupIdentityAndRole = "核心身份与角色是什么？";
    private final static String groupIdentityAndRolePlaceholder = "请描述这个账号所代表的组织/团体的核心名称、业务领域或主要职能。例如：‘XX科技有限公司官方账号，专注于AI解决方案’，‘“光影骑士”独立游戏开发团队’，‘“城市生活美学”内容共创社群’。";
    private final static String groupConceptStory = "这个组织/团体的核心理念/创立故事/独特优势是什么？";
    private final static String groupConceptStoryPlaceholder = "请描述该组织/团体的核心价值观、创立初衷、品牌故事、或者区别于同行的独特技术/服务/文化。例如：‘我们致力于用科技赋能传统行业，让技术更有温度’，‘团队由一群热爱复古游戏的资深玩家组成，希望能重现经典游戏的魅力’，‘我们相信每个普通人的生活都值得被记录和赞美’。";
    private final static String groupGoalAndValue = "这个账号希望通过内容实现什么目标？为用户/客户/成员带来什么价值？";
    private final static String groupGoalAndValuePlaceholder = "请说明这个账号希望通过其发布的内容达到什么具体目标（如品牌推广、用户教育、社群互动、销售转化等），以及能为关注者/用户/客户/成员提供什么样的产品、服务、信息或体验。例如：‘帮助企业了解并应用最新的AI技术，提升运营效率’，‘分享游戏开发过程中的趣事和干货，与玩家建立深度连接’，‘打造一个温暖互助的内容社区，让成员可以自由分享和交流生活感悟’。";


    public PersonGeneratorStartVO(PersonSubjectTypeEnum subjectTypeEnum) {
        if(PersonSubjectTypeEnum.INDIVIDUAL == subjectTypeEnum){
            questions = new ArrayList<>();
            questions.add(new BaseQuestionVO(coreIdentityAndRole, coreIdentityAndRolePlaceholder));
            questions.add(new BaseQuestionVO(coreConceptStory, coreConceptStoryPlaceholder));
            questions.add(new BaseQuestionVO(contentGoalAndValue, contentGoalAndValuePlaceholder));
        }
        if(PersonSubjectTypeEnum.GROUP_ALIAS == subjectTypeEnum || PersonSubjectTypeEnum.ORGANIZATION == subjectTypeEnum){
            questions = new ArrayList<>();
            questions.add(new BaseQuestionVO(groupIdentityAndRole, groupIdentityAndRolePlaceholder));
            questions.add(new BaseQuestionVO(groupConceptStory, groupConceptStoryPlaceholder));
            questions.add(new BaseQuestionVO(groupGoalAndValue, groupGoalAndValuePlaceholder));
        }
    }
}
