package com.daddylab.msaiagent.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class SceneGeneratorStartVO implements Serializable {

    Map<String,List<BaseQuestionVO>> questions;

    public SceneGeneratorStartVO() {
        this.questions = new HashMap<>();
        initializeQuestions();
    }
    
    private void initializeQuestions() {
        // 组一：基础要素型 (快速搭建场景框架)
        List<BaseQuestionVO> group1 = new ArrayList<>();
        group1.add(new BaseQuestionVO("这个场景的核心\"一句话梗概\"是什么？", "例如：周末宅家看电影的放松时刻、工作日早晨匆忙准备出门、与朋友在咖啡馆畅聊人生"));
        group1.add(new BaseQuestionVO("场景发生的具体时间段或特殊节点是？", "例如：一个普通的周三下午、情人节的傍晚、deadline前夜"));
        group1.add(new BaseQuestionVO("场景的主要地点/环境特征是什么？", "例如：舒适的卧室床上、人声鼎沸的网红餐厅、安静的公园长椅上"));
        group1.add(new BaseQuestionVO("场景中主要人物（如果已定）的核心状态或正在进行的活动是？", "例如：感到有些疲惫但内心平静、正在专注地完成一项手工作品、兴奋地开箱一个新快递"));
        group1.add(new BaseQuestionVO("你希望这个场景给人的整体感觉/氛围是怎样的？", "例如：温馨治愈、轻松搞笑、专业严谨、悬念迭起"));
        questions.put("基础要素型 (适合快速搭建场景框架)", group1);
        
        // 组二：感官细节与情感体验型 (侧重营造氛围和代入感)
        List<BaseQuestionVO> group2 = new ArrayList<>();
        group2.add(new BaseQuestionVO("如果用几个词来形容这个场景的\"视觉\"感受，会是什么？", "例如：阳光明媚，色彩鲜亮、灯光昏暗，略带神秘、简约干净，一尘不染"));
        group2.add(new BaseQuestionVO("场景中可能会有哪些标志性的\"声音\"或\"气味\"？", "例如：窗外的雨声和键盘敲击声、咖啡的浓郁香气和轻柔的爵士乐、新鲜出炉面包的甜味"));
        group2.add(new BaseQuestionVO("场景中的主要人物（如果已定）此刻的核心\"情绪\"或\"心境\"是怎样的？", "例如：充满期待和喜悦、略感焦虑但努力克服、平静而满足、好奇并带着一丝紧张"));
        group2.add(new BaseQuestionVO("场景中是否存在某个特别的\"关键物品\"或\"环境细节\"能突出场景特点或人物状态？", "例如：桌角一杯快要冷掉的咖啡、墙上一张褪色的旧照片、手中紧握的一张电影票"));
        group2.add(new BaseQuestionVO("如果用户\"进入\"这个场景，你希望他们最先注意到什么？或者感受到什么？", "例如：温暖的阳光洒在脸上的感觉、空气中淡淡的花香、远处传来的轻柔音乐声"));
        questions.put("感官细节与情感体验型 (侧重营造氛围和代入感)", group2);
        
        // 组三：故事性与互动性引导型 (侧重场景的动态和内容潜力)
        List<BaseQuestionVO> group3 = new ArrayList<>();
        group3.add(new BaseQuestionVO("这个场景的\"开端\"是怎样的？是什么引发了这个场景的发生？", "例如：收到一个意外的通知、突然想尝试一件新事物、一个持续已久的习惯"));
        group3.add(new BaseQuestionVO("场景中是否会发生某个\"小小的转折\"或\"意想不到的事件\"？", "例如：咖啡不小心洒了、接到一个重要的电话、发现了一个被遗忘的旧物件"));
        group3.add(new BaseQuestionVO("这个场景为后续的内容（例如，引出某个商品、知识点或观点）埋下了哪些\"伏笔\"或\"连接点\"？", "例如：主人公遇到的小困难正好需要某个产品来解决、场景中的某个细节引出一个生活技巧、人物的心境变化体现某种生活态度"));
        group3.add(new BaseQuestionVO("如果在这个场景中要与用户互动，你会设计什么样的\"提问\"或\"引导参与\"的方式？", "例如：大家有过类似的经历吗？、你们猜我接下来会怎么做？、推荐一下你们在这个场景下会听的歌吧！"));
        group3.add(new BaseQuestionVO("这个场景结束后，主要人物（如果已定）的状态或想法可能会有什么样的\"细微变化\"？", "例如：从焦虑变得放松、对某件事有了新的认识、决定尝试一种新的生活方式"));
        questions.put("故事性与互动性引导型 (侧重场景的动态和内容潜力)", group3);
        
        // 组四：目标导向与实用性型 (侧重场景服务于特定目的)
        List<BaseQuestionVO> group4 = new ArrayList<>();
        group4.add(new BaseQuestionVO("这个场景主要是为了展现/引出什么核心\"信息\"或\"主题\"？", "例如：某个产品的使用体验、一个生活小技巧、一种积极的生活态度"));
        group4.add(new BaseQuestionVO("你希望通过这个场景，让用户对核心信息/主题产生什么样的\"认知\"或\"感受\"？", "例如：觉得产品很实用、认同这个生活技巧、被这种生活态度所感染"));
        group4.add(new BaseQuestionVO("场景中的哪些元素或情节最能有效地传递这个核心信息/主题？", "例如：人物使用产品时的自然表现、解决问题的巧妙方法、情绪转变的关键时刻"));
        group4.add(new BaseQuestionVO("如果这个场景与某个\"素材\"（如商品、知识点）相关，场景是如何自然地将素材\"融入\"其中的？", "例如：通过人物的日常需求自然引出产品、在解决问题的过程中展示知识点、让素材成为场景不可缺少的一部分"));
        group4.add(new BaseQuestionVO("这个场景最适合在什么类型的内容（如图文笔记、短视频、直播）中呈现？为什么？", "例如：适合短视频因为有明显的情节转折、适合图文笔记因为需要详细的环境描述、适合直播因为有很强的互动性"));
        questions.put("目标导向与实用性型 (侧重场景服务于特定目的)", group4);
    }
}
