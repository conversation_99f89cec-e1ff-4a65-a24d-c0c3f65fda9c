package com.daddylab.msaiagent.domain.enums;

import lombok.Getter;

@Getter
public enum PersonCreationSource {
   /* 1: AI_GENERATED (纯AI生成)
2: OPERATOR_DESIGNED (运营设计+ai补充)
3: IMITATIVE_LEARNED (仿写学习)*/
    AI_GENERATED(1, "纯AI生成"),
    OPERATOR_DESIGNED(2, "运营设计+ai补充"),
    IMITATIVE_LEARNED(3, "仿写学习");

    private final int code;
    private final String description;

    PersonCreationSource(int code, String description) {
        this.code = code;
        this.description = description;
    }
}
