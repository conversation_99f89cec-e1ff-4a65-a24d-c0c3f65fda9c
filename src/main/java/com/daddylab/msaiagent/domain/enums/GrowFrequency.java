package com.daddylab.msaiagent.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/** 更新触发条件和典型频率. 1.月检，2.季度检，3.半年检，4.年检，5.事件触发。6.手动调整。 */
@Getter
public enum GrowFrequency {
  UNKNOWN(0, "未知"),
  MONTHLY(1, "月检"),
  QUARTERLY(2, "季度检"),
  HALF_YEARLY(3, "半年检"),
  ANNUAL(4, "年检"),
  EVENT_TRIGGERED(5, "事件触发"),
  MANUAL_ADJUSTMENT(6, "手动调整");

  @EnumValue private final int value;
  private final String desc;

  GrowFrequency(int value, String desc) {
    this.value = value;
    this.desc = desc;
  }
}
