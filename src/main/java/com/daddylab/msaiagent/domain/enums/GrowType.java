package com.daddylab.msaiagent.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;
import org.checkerframework.common.value.qual.EnumVal;

@Getter
public enum GrowType {

//    1.成长型。2.状态型。3.静态型

    UNKNOWN(0, "未知"),
    GROW_TYPE(1, "成长型"),
    STATUS_TYPE(2, "状态型"),
    STATIC_TYPE(3, "静态型");

    @EnumValue
    private final int vale;
    private final String desc;

    GrowType(int vale, String desc) {
        this.vale = vale;
        this.desc = desc;
    }
}
