package com.daddylab.msaiagent.domain.enums;

import lombok.Getter;

@Getter
public enum AttributeDataTypeEnum {
 /*   1: STRING (单行文本)
2: TEXT (多行文本)
3: INTEGER (整数)
4: DECIMAL (小数)
5: BOOLEAN (布尔型，实际存储可以是0或1的TINYINT)
6: DATE (日期，存储时间戳)
7: DATETIME (日期时间，存储时间戳)
8: JSON_OBJECT (JSON对象字符串)
9: JSON_ARRAY_STRING (JSON数组字符串，每个元素是字符串)
10: JSON_ARRAY_OBJECT (JSON数组字符串，每个元素是对象)
11: ENUM_LIKE (类枚举，配合validation_rules_json中的options)
*/
    STRING(1, "单行文本"),
    TEXT(2, "多行文本"),
    INTEGER(3, "整数"),
    DECIMAL(4, "小数"),
    BOOLEAN(5, "布尔型"),
    DATE(6, "日期"),
    DATETIME(7, "日期时间"),
    JSON_OBJECT(8, "JSON对象字符串"),
    JSON_ARRAY_STRING(9, "JSON数组字符串，每个元素是字符串"),
    JSON_ARRAY_OBJECT(10, "JSON数组字符串，每个元素是对象"),
    ENUM_LIKE(11, "类枚举");

    private final int code;
    private final String description;

    AttributeDataTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }
}
