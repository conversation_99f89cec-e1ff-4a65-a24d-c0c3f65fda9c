package com.daddylab.msaiagent.domain.enums;

import lombok.Getter;

@Getter
public enum StorageLocationHintEnum {
   /* 1: MONGODB_DYNAMIC (主要存储在MongoDB的动态属性中)
2: MYSQL_CORE (可能直接存储在persona_core的某些字段，或MySQL的其他关联表)
3: NEO4J_NODE_PROPERTY (倾向于作为Neo4j节点的属性)
4: NEO4J_RELATIONSHIP_PROPERTY (倾向于作为Neo4j关系的属性)
5: VECTOR_DB_METADATA (作为向量数据库中向量的元数据)*/
    MONGODB_DYNAMIC(1, "主要存储在MongoDB的动态属性中"),
    MYSQL_CORE(2, "可能直接存储在persona_core的某些字段，或MySQL的其他关联表"),
    NEO4J_NODE_PROPERTY(3, "倾向于作为Neo4j节点的属性"),
    NEO4J_RELATIONSHIP_PROPERTY(4, "倾向于作为Neo4j关系的属性"),
    VECTOR_DB_METADATA(5, "作为向量数据库中向量的元数据");

    private final int code;
    private final String description;

    StorageLocationHintEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }
}
