package com.daddylab.msaiagent.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * <AUTHOR>
 * @className AIModelEnum
 * @date 2025/4/30 17:32
 * @description: TODO
 */
@Getter
@AllArgsConstructor
public enum AIModelEnum {
    OPENAI("openAiChatModel"),
    GEMINI("vertexAiGeminiChat"),
    CLAUDE("anthropicChatModel"),
    DASHSCOPE("dashscopeChatModel"),
    AZURE_OPENAI("azureOpenAiChatModel")
    ;

    final private String value;

}
