package com.daddylab.msaiagent.domain.enums;

import lombok.Getter;

@Getter
public enum PersonStatusEnum {

    /*0: DRAFT (草稿)
    1: ACTIVE (活跃)
2: INACTIVE (未激活/暂停)
3: PENDING_REVIEW (待审核)
4: ARCHIVED (已归档)*/
    DRAFT(0, "草稿"),
    ACTIVE(1, "活跃"),
    INACTIVE(2, "未激活/暂停"),
    PENDING_REVIEW(3, "待审核"),
    ARCHIVED(4, "已归档");

    private final int code;
    private final String description;

    PersonStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

}
