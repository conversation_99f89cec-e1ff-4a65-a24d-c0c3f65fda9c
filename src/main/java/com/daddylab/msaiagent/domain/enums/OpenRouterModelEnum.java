package com.daddylab.msaiagent.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OpenRouterModelEnum {
    GEMINI25("google-gemini-2_5-pro-preview","google/gemini-2.5-pro-preview")
    ;
    final private String key;
    final private String name;

    public static OpenRouterModelEnum getEnumByName(String key) {
        for (OpenRouterModelEnum e : OpenRouterModelEnum.values()) {
            if (e.name.equals(key)) {
                return e;
            }
        }
        return null;
    }
}
