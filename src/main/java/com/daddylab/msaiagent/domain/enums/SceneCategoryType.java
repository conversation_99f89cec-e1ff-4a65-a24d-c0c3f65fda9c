package com.daddylab.msaiagent.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(
        title = "场景片段分类代码",
        description =
                """
                 1: GENERAL (通用型)
                 2: PERSONA_SPECIFIC (专用型 - 与特定虚拟人或类型关联)
                 3: KNOWLEDGE_BASED (知识库型)
                 4: PRODUCT_FOCUSED (商品型)
                 """)
@Getter
public enum SceneCategoryType {
    GENERAL(1, "通用型"),
    PERSONA_SPECIFIC(2, "专用型 - 与特定虚拟人或类型关联"),
    KNOWLEDGE_BASED(3, "知识库型"),
    PRODUCT_FOCUSED(4, "商品型");

    @EnumValue
    private final int code;

    private final String description;

    SceneCategoryType(int code, String description) {
        this.code = code;
        this.description = description;
    }
}
