package com.daddylab.msaiagent.domain.enums;

import lombok.Getter;

@Getter
public enum PersonPrimaryMonetizationModelEnum {
    /*
    1: 商品带货 (直播、短视频、图文)2: 知识付费 (课程、社群、咨询)3: 广告植入/品牌合作4: 直播打赏/虚拟礼物 5: IP授权/衍生品
     */
    PRODUCT_SALES(1, "商品带货 (直播、短视频、图文)"),
    KNOWLEDGE_PAYMENT(2, "知识付费 (课程、社群、咨询)"),
    ADVERTISING(3, "广告植入/品牌合作"),
    LIVE_REWARD(4, "直播打赏/虚拟礼物"),
    IP_AUTHORIZATION(5, "IP授权/衍生品");
    private final int code;
    private final String description;
    PersonPrimaryMonetizationModelEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static PersonPrimaryMonetizationModelEnum fromCode(int code) {
        for (PersonPrimaryMonetizationModelEnum type : values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown code: " + code);
    }
}
