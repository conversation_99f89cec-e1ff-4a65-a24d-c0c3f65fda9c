package com.daddylab.msaiagent.domain.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(
        title = "主体类型",
        description =
                """
    INDIVIDUAL(1, "个体虚拟人"), ORGANIZATION(2, "组织/企业虚拟人"), GROUP_ALIAS(3, "团体别名，如虚拟乐队"), PRODUCT_IP(4, "产品IP拟人化")
    """)
@Getter
public enum PersonSubjectTypeEnum {
    INDIVIDUAL(1, "个体虚拟人"),
    ORGANIZATION(2, "组织/企业虚拟人"),
    GROUP_ALIAS(3, "团体别名，如虚拟乐队"),
    PRODUCT_IP(4, "产品IP拟人化");

    private final int code;
    private final String description;

    PersonSubjectTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static PersonSubjectTypeEnum fromCode(int code) {
        for (PersonSubjectTypeEnum type : values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }
}
