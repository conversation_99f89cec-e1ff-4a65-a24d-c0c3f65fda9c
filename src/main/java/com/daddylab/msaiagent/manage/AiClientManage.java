package com.daddylab.msaiagent.manage;

import cn.hutool.extra.spring.SpringUtil;
import com.daddylab.msaiagent.domain.enums.AIModelEnum;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.stereotype.Component;

/**
 *
 * @className AiClientManage
 * <AUTHOR>
 * @date 2025/4/30 17:26
 * @description: TODO 
 */
public class AiClientManage {

    /**
     * 获取聊天的客户端
     *
     * @param aiModelEnum com.daddylab.msaiagent.domain.enums.AIModelEnum
     * @return org.springframework.ai.chat.client.ChatClient
     * <AUTHOR>
     * @date 2025/4/30 17:38
     */
    public static ChatClient buildDefaultClient(AIModelEnum aiModelEnum) {
        return ChatClient.create(chatModel(aiModelEnum));
    }

    /**
     * 获取ChatModel
     *
     * @param aiModelEnum com.daddylab.msaiagent.domain.enums.AIModelEnum
     * @return org.springframework.ai.chat.model.ChatModel
     * <AUTHOR>
     * @date 2025/5/8 13:54
     */
    public static ChatModel chatModel(AIModelEnum aiModelEnum) {
        return SpringUtil.getBean(aiModelEnum.getValue());
    }
}
