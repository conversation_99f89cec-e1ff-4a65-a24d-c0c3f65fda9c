package com.daddylab.msaiagent.convert;

import com.daddylab.msaiagent.common.utils.JsonUtil;
import com.daddylab.msaiagent.db.aiAgent.entity.KnowledgeBaseMaterial;
import com.daddylab.msaiagent.db.aiAgent.entity.ProductMaterial;
import com.daddylab.msaiagent.domain.dto.ProductDetailDto;
import com.daddylab.msaiagent.domain.vo.KnowledgeBaseMaterialPageVO;
import com.daddylab.msaiagent.domain.vo.KnowledgeBaseMaterialVO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

/**
 * 知识库映射器
 */
@Mapper
public interface KnowledgeBaseConvert {

    KnowledgeBaseConvert INSTANCE = Mappers.getMapper(KnowledgeBaseConvert.class);

    KnowledgeBaseMaterialPageVO toPageVO(KnowledgeBaseMaterial knowledgeBaseMaterial);

    KnowledgeBaseMaterialVO toVO(KnowledgeBaseMaterial knowledgeBaseMaterial);
}