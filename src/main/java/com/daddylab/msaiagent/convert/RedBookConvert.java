package com.daddylab.msaiagent.convert;

import com.daddylab.msaiagent.db.aiAgent.entity.RedBook;
import com.daddylab.msaiagent.domain.vo.RedBookInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @className RedBookConvert
 * @date 2025/5/26 16:37
 * @description: TODO
 */
@Mapper
public interface RedBookConvert {
    RedBookConvert INSTANCE = Mappers.getMapper(RedBookConvert.class);

    RedBookInfoVO redBookToInfoVO(RedBook redBook);

}
