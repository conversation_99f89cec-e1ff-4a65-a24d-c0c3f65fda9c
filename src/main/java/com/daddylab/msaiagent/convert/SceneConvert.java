package com.daddylab.msaiagent.convert;

import com.daddylab.msaiagent.common.utils.JsonUtil;
import com.daddylab.msaiagent.db.aiAgent.entity.SceneFragment;
import com.daddylab.msaiagent.domain.dto.SceneDetailDto;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

/**
 * 场景片段映射器
 * 用于将AI生成的场景信息更新到场景片段实体中
 */
@Mapper
public interface SceneConvert {

    SceneConvert INSTANCE = Mappers.getMapper(SceneConvert.class);

    /**
     * 将SceneDetailDto中的非空字段更新到SceneFragment中
     * 
     * @param dto AI生成的场景信息DTO
     * @param fragment 要更新的场景片段实体
     */
    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
    )
    @Mapping(target = "fragmentName", source = "fragmentName")
    @Mapping(target = "description", source = "description")
    @Mapping(target = "categoryTypeCode", source = "categoryTypeCode", qualifiedByName = "integerToByte")
    @Mapping(target = "subjectTypeApplicabilityJson", source = "subjectTypeApplicabilityJson", qualifiedByName = "integerListToJson")
    @Mapping(target = "personaTagApplicabilityJson", source = "personaTagApplicabilityJson", qualifiedByName = "personaTagApplicabilityToJson")
    @Mapping(target = "culturalTagsJson", source = "culturalTagsJson", qualifiedByName = "listToJson")
    @Mapping(target = "regionSpecificityTagsJson", source = "regionSpecificityTagsJson", qualifiedByName = "listToJson")
    @Mapping(target = "sceneElementsJson", source = "sceneElementsJson", qualifiedByName = "sceneElementsToJson")
    @Mapping(target = "outputPromptSegment", source = "outputPromptSegment")
    @Mapping(target = "interactionPromptTemplate", source = "interactionPromptTemplate")
    @Mapping(target = "isInteractiveFocused", source = "isInteractiveFocused", qualifiedByName = "integerToByte")
    @Mapping(target = "expansionPointsDefinitionJson", source = "expansionPointsDefinitionJson", qualifiedByName = "expansionPointsToJson")
    @Mapping(target = "emotionalToneTagsJson", source = "emotionalToneTagsJson", qualifiedByName = "listToJson")
    @Mapping(target = "narrativeFunctionTagsJson", source = "narrativeFunctionTagsJson", qualifiedByName = "listToJson")
    @Mapping(target = "timelinessTagsJson", source = "timelinessTagsJson", qualifiedByName = "listToJson")
    @Mapping(target = "uniquenessRating", source = "uniquenessRating", qualifiedByName = "integerToByte")
    @Mapping(target = "triggerKeywordsJson", source = "triggerKeywordsJson", qualifiedByName = "listToJson")
    @Mapping(target = "usageGuidelinesOrConstraints", source = "usageGuidelinesOrConstraints")
    void updateFragmentFromDto(SceneDetailDto dto, @MappingTarget SceneFragment fragment);

    /**
     * 将SceneFragment转换为SceneDetailDto
     * 
     * @param fragment 场景片段实体
     * @return 场景详情DTO
     */
    @Mapping(target = "fragmentName", source = "fragmentName")
    @Mapping(target = "description", source = "description")
    @Mapping(target = "categoryTypeCode", source = "categoryTypeCode", qualifiedByName = "byteToInteger")
    @Mapping(target = "subjectTypeApplicabilityJson", source = "subjectTypeApplicabilityJson", qualifiedByName = "jsonToIntegerList")
    @Mapping(target = "personaTagApplicabilityJson", source = "personaTagApplicabilityJson", qualifiedByName = "jsonToPersonaTagApplicability")
    @Mapping(target = "culturalTagsJson", source = "culturalTagsJson", qualifiedByName = "jsonToStringList")
    @Mapping(target = "regionSpecificityTagsJson", source = "regionSpecificityTagsJson", qualifiedByName = "jsonToStringList")
    @Mapping(target = "sceneElementsJson", source = "sceneElementsJson", qualifiedByName = "jsonToSceneElements")
    @Mapping(target = "outputPromptSegment", source = "outputPromptSegment")
    @Mapping(target = "interactionPromptTemplate", source = "interactionPromptTemplate")
    @Mapping(target = "isInteractiveFocused", source = "isInteractiveFocused", qualifiedByName = "byteToInteger")
    @Mapping(target = "expansionPointsDefinitionJson", source = "expansionPointsDefinitionJson", qualifiedByName = "jsonToExpansionPoints")
    @Mapping(target = "emotionalToneTagsJson", source = "emotionalToneTagsJson", qualifiedByName = "jsonToStringList")
    @Mapping(target = "narrativeFunctionTagsJson", source = "narrativeFunctionTagsJson", qualifiedByName = "jsonToStringList")
    @Mapping(target = "timelinessTagsJson", source = "timelinessTagsJson", qualifiedByName = "jsonToStringList")
    @Mapping(target = "uniquenessRating", source = "uniquenessRating", qualifiedByName = "byteToInteger")
    @Mapping(target = "triggerKeywordsJson", source = "triggerKeywordsJson", qualifiedByName = "jsonToStringList")
    @Mapping(target = "usageGuidelinesOrConstraints", source = "usageGuidelinesOrConstraints")
    SceneDetailDto fragmentToDto(SceneFragment fragment);

    // ========== 类型转换方法 ==========

    /**
     * 将Integer转换为Byte
     */
    @Named("integerToByte")
    default Byte integerToByte(Integer value) {
        if (value == null) {
            return null;
        }
        return value.byteValue();
    }

    /**
     * 将Byte转换为Integer
     */
    @Named("byteToInteger")
    default Integer byteToInteger(Byte value) {
        if (value == null) {
            return null;
        }
        return value.intValue();
    }

    // ========== 对象转JSON方法 ==========

    /**
     * 将List<String>转换为JSON字符串
     */
    @Named("listToJson")
    default String listToJson(List<String> list) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        return JsonUtil.toJSONString(list);
    }

    /**
     * 将List<Integer>转换为JSON字符串
     */
    @Named("integerListToJson")
    default String integerListToJson(List<Integer> list) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        return JsonUtil.toJSONString(list);
    }

    /**
     * 将PersonaTagApplicability转换为JSON字符串
     */
    @Named("personaTagApplicabilityToJson")
    default String personaTagApplicabilityToJson(SceneDetailDto.PersonaTagApplicability applicability) {
        if (applicability == null) {
            return null;
        }
        return JsonUtil.toJSONString(applicability);
    }

    /**
     * 将SceneElements转换为JSON字符串
     */
    @Named("sceneElementsToJson")
    default String sceneElementsToJson(SceneDetailDto.SceneElements elements) {
        if (elements == null) {
            return null;
        }
        return JsonUtil.toJSONString(elements);
    }

    /**
     * 将Map<String, ExpansionPoint>转换为JSON字符串
     */
    @Named("expansionPointsToJson")
    default String expansionPointsToJson(Map<String, SceneDetailDto.ExpansionPoint> expansionPoints) {
        if (expansionPoints == null || expansionPoints.isEmpty()) {
            return null;
        }
        return JsonUtil.toJSONString(expansionPoints);
    }

    // ========== JSON转对象方法 ==========

    /**
     * 将JSON字符串转换为List<String>
     */
    @Named("jsonToStringList")
    default List<String> jsonToStringList(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return JsonUtil.parseObjectList(json, String.class);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 将JSON字符串转换为List<Integer>
     */
    @Named("jsonToIntegerList")
    default List<Integer> jsonToIntegerList(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return JsonUtil.parseObjectList(json, Integer.class);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 将JSON字符串转换为PersonaTagApplicability
     */
    @Named("jsonToPersonaTagApplicability")
    default SceneDetailDto.PersonaTagApplicability jsonToPersonaTagApplicability(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return JsonUtil.parseObject(json, SceneDetailDto.PersonaTagApplicability.class);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 将JSON字符串转换为SceneElements
     */
    @Named("jsonToSceneElements")
    default SceneDetailDto.SceneElements jsonToSceneElements(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return JsonUtil.parseObject(json, SceneDetailDto.SceneElements.class);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 将JSON字符串转换为Map<String, ExpansionPoint>
     */
    @Named("jsonToExpansionPoints")
    default Map<String, SceneDetailDto.ExpansionPoint> jsonToExpansionPoints(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return JsonUtil.parseObject(json, Map.class);
        } catch (Exception e) {
            return null;
        }
    }
} 