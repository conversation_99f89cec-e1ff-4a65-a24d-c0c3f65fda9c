package com.daddylab.msaiagent.convert;

import cn.hutool.core.util.StrUtil;
import com.daddylab.msaiagent.common.base.enums.IEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

@Mapper
public interface EnumConvert {

  EnumConvert INSTANCE = Mappers.getMapper(EnumConvert.class);

  default int iEnumToInt(IEnum<Integer> enumInteger) {
    if (enumInteger == null) {
       return 0;
    }
    return enumInteger.getValue();
  }

  default String iEnumToStr(IEnum<String> enumString) {
    if (enumString == null) {
      return StrUtil.EMPTY;
    }
    return enumString.getValue();
  }

  @Named("iEnumToDesc")
  default String iEnumToDesc(IEnum<String> iEnum) {
    if (iEnum == null) {
      return StrUtil.EMPTY;
    }
    return iEnum.getDesc();
  }
}
