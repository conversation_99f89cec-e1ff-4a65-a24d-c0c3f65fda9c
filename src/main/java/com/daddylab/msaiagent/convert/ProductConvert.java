package com.daddylab.msaiagent.convert;

import com.daddylab.msaiagent.common.utils.JsonUtil;
import com.daddylab.msaiagent.db.aiAgent.entity.ProductMaterial;
import com.daddylab.msaiagent.domain.dto.ProductDetailDto;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

/**
 * 商品映射器
 * 用于将AI生成的商品信息更新到商品素材实体中
 */
@Mapper
public interface ProductConvert {

    ProductConvert INSTANCE = Mappers.getMapper(ProductConvert.class);

    /**
     * 将ProductDetailDto中的非空字段更新到ProductMaterial中
     * 
     * @param dto AI生成的商品信息DTO
     * @param material 要更新的商品素材实体
     */
    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
    )
    @Mapping(target = "productCategoryTagsJson", source = "productCategoryTagsJson", qualifiedByName = "listToJson")
    @Mapping(target = "coreSellingPointsJson", source = "coreSellingPointsJson", qualifiedByName = "listToJson")
    @Mapping(target = "targetedSegmentsPainPointsSolutionsJson", source = "targetedSegmentsPainPointsSolutionsJson", qualifiedByName = "targetedSolutionsToJson")
    @Mapping(target = "usageScenarioExamplesJson", source = "usageScenarioExamplesJson", qualifiedByName = "listToJson")
    @Mapping(target = "userExperienceKeywordsJson", source = "userExperienceKeywordsJson", qualifiedByName = "listToJson")
    @Mapping(target = "productSpecificationsJson", source = "productSpecificationsJson", qualifiedByName = "mapToJson")
    @Mapping(target = "appearanceDescriptionKeywordsJson", source = "appearanceDescriptionKeywordsJson", qualifiedByName = "listToJson")
    @Mapping(target = "callToActionSuggestionsJson", source = "callToActionSuggestionsJson", qualifiedByName = "listToJson")
    @Mapping(target = "storytellingAnglesJson", source = "storytellingAnglesJson", qualifiedByName = "listToJson")
    @Mapping(target = "comparisonPointsVsCompetitorsJson", source = "comparisonPointsVsCompetitorsJson", qualifiedByName = "comparisonsToJson")
    @Mapping(target = "associatedHashtagsSuggestionJson", source = "associatedHashtagsSuggestionJson", qualifiedByName = "listToJson")
    @Mapping(target = "contentToneStyleSuggestionsJson", source = "contentToneStyleSuggestionsJson", qualifiedByName = "listToJson")
    @Mapping(target = "tagsJson", source = "tagsJson", qualifiedByName = "listToJson")
    void updateMaterialFromDto(ProductDetailDto dto, @MappingTarget ProductMaterial material);

    /**
     * 将ProductMaterial转换为ProductDetailDto
     * 
     * @param material 商品素材实体
     * @return 商品详情DTO
     */
    @Mapping(target = "productCategoryTagsJson", source = "productCategoryTagsJson", qualifiedByName = "jsonToStringList")
    @Mapping(target = "coreSellingPointsJson", source = "coreSellingPointsJson", qualifiedByName = "jsonToStringList")
    @Mapping(target = "targetedSegmentsPainPointsSolutionsJson", source = "targetedSegmentsPainPointsSolutionsJson", qualifiedByName = "jsonToTargetedSolutions")
    @Mapping(target = "usageScenarioExamplesJson", source = "usageScenarioExamplesJson", qualifiedByName = "jsonToStringList")
    @Mapping(target = "userExperienceKeywordsJson", source = "userExperienceKeywordsJson", qualifiedByName = "jsonToStringList")
    @Mapping(target = "productSpecificationsJson", source = "productSpecificationsJson", qualifiedByName = "jsonToMap")
    @Mapping(target = "appearanceDescriptionKeywordsJson", source = "appearanceDescriptionKeywordsJson", qualifiedByName = "jsonToStringList")
    @Mapping(target = "callToActionSuggestionsJson", source = "callToActionSuggestionsJson", qualifiedByName = "jsonToStringList")
    @Mapping(target = "storytellingAnglesJson", source = "storytellingAnglesJson", qualifiedByName = "jsonToStringList")
    @Mapping(target = "comparisonPointsVsCompetitorsJson", source = "comparisonPointsVsCompetitorsJson", qualifiedByName = "jsonToComparisons")
    @Mapping(target = "associatedHashtagsSuggestionJson", source = "associatedHashtagsSuggestionJson", qualifiedByName = "jsonToStringList")
    @Mapping(target = "contentToneStyleSuggestionsJson", source = "contentToneStyleSuggestionsJson", qualifiedByName = "jsonToStringList")
    @Mapping(target = "tagsJson", source = "tagsJson", qualifiedByName = "jsonToStringList")
    ProductDetailDto materialToDto(ProductMaterial material);

    /**
     * 将List<String>转换为JSON字符串
     */
    @Named("listToJson")
    default String listToJson(List<String> list) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        return JsonUtil.toJSONString(list);
    }

    /**
     * 将Map<String,String>转换为JSON字符串
     */
    @Named("mapToJson")
    default String mapToJson(Map<String, String> map) {
        if (map == null || map.isEmpty()) {
            return null;
        }
        return JsonUtil.toJSONString(map);
    }

    /**
     * 将List<TargetedPainPointsSolution>转换为JSON字符串
     */
    @Named("targetedSolutionsToJson")
    default String targetedSolutionsToJson(List<ProductDetailDto.TargetedPainPointsSolution> list) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        return JsonUtil.toJSONString(list);
    }

    /**
     * 将List<Comparison>转换为JSON字符串
     */
    @Named("comparisonsToJson")
    default String comparisonsToJson(List<ProductDetailDto.Comparison> list) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        return JsonUtil.toJSONString(list);
    }

    // ========== 反向转换方法：JSON字符串 -> 对象 ==========

    /**
     * 将JSON字符串转换为List<String>
     */
    @Named("jsonToStringList")
    default List<String> jsonToStringList(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return JsonUtil.parseObjectList(json, String.class);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 将JSON字符串转换为Map<String,String>
     */
    @Named("jsonToMap")
    default Map<String, String> jsonToMap(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return JsonUtil.parseObject(json, Map.class);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 将JSON字符串转换为List<TargetedPainPointsSolution>
     */
    @Named("jsonToTargetedSolutions")
    default List<ProductDetailDto.TargetedPainPointsSolution> jsonToTargetedSolutions(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return JsonUtil.parseObjectList(json, ProductDetailDto.TargetedPainPointsSolution.class);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 将JSON字符串转换为List<Comparison>
     */
    @Named("jsonToComparisons")
    default List<ProductDetailDto.Comparison> jsonToComparisons(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return JsonUtil.parseObjectList(json, ProductDetailDto.Comparison.class);
        } catch (Exception e) {
            return null;
        }
    }
} 