package com.daddylab.msaiagent.agent;

import com.daddylab.msaiagent.db.aiAgent.entity.Note;
import com.daddylab.msaiagent.db.aiAgent.entity.VirtualPerson;
import com.daddylab.msaiagent.domain.enums.AIModelEnum;
import com.daddylab.msaiagent.manage.AiClientManage;
import com.daddylab.msaiagent.tool.HotspotTool;
import com.daddylab.msaiagent.tool.RedBookTool;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.model.tool.ToolCallingChatOptions;
import org.springframework.ai.tool.ToolCallbacks;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *
 * @className NoteTagAgent
 * <AUTHOR>
 * @date 2025/5/14 10:52
 * @description: 笔记标签智能体
 */
@Component
public class NoteTagAgent {
    @Autowired
    private HotspotTool hotspotTool;


    /**
     * 根据文章和提示构建生成笔记标签
     */
    public String buildTag(AIModelEnum model, String sysTemplate, String prompt) {
        ChatClient chatClient = AiClientManage.buildDefaultClient(model);
        return chatClient.prompt()
                .system(sysTemplate)
                .user(prompt)
                .options(ToolCallingChatOptions.builder()
                        .temperature(0.8)
                        .toolCallbacks(ToolCallbacks.from(hotspotTool))
                        .build())
                .call().content();
    }
}
