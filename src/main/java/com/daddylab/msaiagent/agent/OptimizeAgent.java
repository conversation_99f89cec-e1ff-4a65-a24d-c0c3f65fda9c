package com.daddylab.msaiagent.agent;

import com.daddylab.msaiagent.db.aiAgent.entity.Prompt;
import com.daddylab.msaiagent.db.aiAgent.enums.PromptTypeEnum;
import com.daddylab.msaiagent.domain.enums.AIModelEnum;
import com.daddylab.msaiagent.manage.AiClientManage;
import com.daddylab.msaiagent.service.PromptService;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import reactor.core.publisher.Flux;


/**
 *
 * @className OpttimizeAgent
 * <AUTHOR>
 * @date 2025/5/8 15:37
 * @description: TODO 
 */
@Component
public class OptimizeAgent {
    @Autowired
    private PromptService promptService;
    /**
     * 优化提示词
     *
     * @param aiModelEnum AIModelEnum
     * @param prompt java.lang.String
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025/5/8 16:00
     */
    public String dispose(AIModelEnum aiModelEnum, String prompt) {
        Prompt promptActive = promptService.getPromptActive(PromptTypeEnum.OPTIMIZATION);
        Assert.notNull(promptActive, "promptActive is null");
        Flux<String> flux = AiClientManage.buildDefaultClient(aiModelEnum)
                .prompt()
                .options(ChatOptions.builder()
                        .temperature(0.8)
                        .build())
                .system(promptActive.getContent())
                .user(prompt)
                .stream()
                .content();
        StringBuilder sb = new StringBuilder();
        flux.subscribe(sb::append);
        flux.blockLast();
        return sb.toString();
    }
}
