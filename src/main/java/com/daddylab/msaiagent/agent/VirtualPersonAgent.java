package com.daddylab.msaiagent.agent;

import com.daddylab.msaiagent.controller.TestController;
import com.daddylab.msaiagent.db.aiAgent.entity.VirtualPerson;
import com.daddylab.msaiagent.domain.enums.AIModelEnum;
import com.daddylab.msaiagent.manage.AiClientManage;
import com.daddylab.msaiagent.tool.RedBookTool;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.model.tool.ToolCallingChatOptions;
import org.springframework.ai.tool.ToolCallbacks;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *
 * @className VirtualAgent
 * <AUTHOR>
 * @date 2025/5/9 10:52
 * @description: TODO 
 */
@Component
public class VirtualPersonAgent {
    @Autowired
    private RedBookTool redBookTool;


    /**
     * 根据模版和提示构建一个虚拟人物
     *
     * @param sysTemplate java.lang.String
     * @param prompt java.lang.String
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025/5/9 11:04
     */
    public VirtualPerson buildPerson(AIModelEnum model, String sysTemplate, String prompt) {
        ChatClient chatClient = AiClientManage.buildDefaultClient(model);
        return chatClient.prompt()
                .system(sysTemplate)
                .user(prompt)
                .options(ToolCallingChatOptions.builder()
                        .temperature(0.8)
                        .toolCallbacks(ToolCallbacks.from(redBookTool))
                        .build())
                .call()
                .entity(VirtualPerson.class);
    }
}
