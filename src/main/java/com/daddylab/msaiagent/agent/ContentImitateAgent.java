package com.daddylab.msaiagent.agent;

import com.daddylab.msaiagent.db.aiAgent.dao.LlmSystemUserDao;
import com.daddylab.msaiagent.domain.enums.AIModelEnum;
import com.daddylab.msaiagent.domain.form.ContentImitateForm;
import com.daddylab.msaiagent.manage.AiClientManage;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.lang.reflect.Field;

/**
 * 内容仿写
 *
 * <AUTHOR> up
 * @date 2025年05月19日 10:16 AM
 */
@Service
@Slf4j
public class ContentImitateAgent {

    @Autowired
    LlmSystemUserDao llmSystemUserDao;

    private static final String BIZ_CODE = "CONTENT_IMITATE";

    private static final String demo = """
            你是一位专业的小红书笔记创作助手。
                                你的任务是根据用户提供的范文和具体要求，仿写出高质量、高吸引力、符合小红书平台风格的新笔记。
                                你的语言风格应该自然、生动、富有亲和力，擅长使用emoji和流行的网络表达，能够准确把握不同主题笔记的侧重点和排版美学。
                                请始终以帮助用户创作出爆款笔记为目标。
            """;


    public String imitate(AIModelEnum model, ContentImitateForm request) {
        final String userPrompt = convertToString(request);
        Assert.hasText(userPrompt, "user prompt不能为空");
        final ChatClient chatClient = AiClientManage.buildDefaultClient(model);
        final String systemMessage = llmSystemUserDao.systemMessage(BIZ_CODE);
        final ChatClient.CallResponseSpec call = chatClient.prompt().system(systemMessage).call();
        return call.content();
    }


    public static String convertToString(ContentImitateForm form) {
        if (form == null) {
            return "";
        }

        StringBuilder result = new StringBuilder();

        try {
            Field[] fields = ContentImitateForm.class.getDeclaredFields();

            for (Field field : fields) {
                field.setAccessible(true);
                ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);

                if (annotation != null) {
                    String annotationValue = annotation.value();

                    Object fieldValue = field.get(form);

                    if (fieldValue != null) {
                        result.append(annotationValue)
                                .append("：")
                                .append(fieldValue)
                                .append("\n");
                    }
                }
            }
        } catch (IllegalAccessException e) {
            return "";
        }

        return result.toString();
    }


    public void convert2(ContentImitateForm form) {
//        """
//
//
//
//                """
    }


    public static void main(String[] args) {

        ContentImitateForm contentImitateForm = new ContentImitateForm();

// 设置基本参数
        contentImitateForm.setModelContent("""
                人工智能：从概念到现实的跨越\\n\\n" +
                        "人工智能技术正以惊人的速度改变着我们的世界。从最初图灵测试的构想，到如今的深度学习网络，AI已从科幻小说中的想象成为日常生活的一部分。\uD83D\uDE80\\n\\n" +
                        "现代AI应用已经渗透到各个领域：智能助手简化我们的日常任务，计算机视觉助力医疗诊断，自然语言处理让机器理解人类语言成为可能。这些突破背后，是算法创新和计算能力的双重提升。\\n\\n" +
                        "尽管AI带来无限可能，我们也需要思考数据隐私和算法偏见等伦理问题。未来的AI发展，不仅需要技术突破，更需要负责任的治理框架。\\n\\n" +\s
                        "你准备好迎接AI驱动的未来了吗？#人工智能 #技术革新 #未来展望""");
        contentImitateForm.setCoreInstruction("基于范文风格，撰写一篇关于元宇宙技术发展的文章");

// 设置内容与主题深度定制参数
        contentImitateForm.setNewTitle("元宇宙：数字与现实的融合新纪元");
        contentImitateForm.setInfoPoint("元宇宙概念起源、技术基础、应用场景、未来发展");
        contentImitateForm.setDetail("强调元宇宙中VR/AR技术的应用和区块链作为底层支撑的重要性");
        contentImitateForm.setAngle("从普通用户视角出发，探讨元宇宙如何改变日常生活");
        contentImitateForm.setBreakthroughPoint("从教育领域切入，讨论元宇宙如何革新传统教育模式");
        contentImitateForm.setInnovate("引入'数字孪生'概念，探讨现实世界与虚拟世界的映射关系");
        contentImitateForm.setDifferent("相比范文更注重技术落地应用而非理论发展");

// 设置风格、语气与情感表达参数
        contentImitateForm.setTone("专业中带有探索性，充满好奇和期待");
        contentImitateForm.setLinguisticFeature("使用生动的比喻和类比，将复杂技术概念通俗化");
        contentImitateForm.setEmojiStrategy("适量使用科技相关emoji，如🌐💻🚀，增强科技感");
        contentImitateForm.setSenseOfReality("通过引用真实案例和数据增强可信度");
        contentImitateForm.setTrustDegree("引用权威专家观点和研究数据，保持科学严谨");

// 设置结构、排版与视觉引导参数
        contentImitateForm.setTitleDefinition("主标题简洁有力，副标题解释性强，使用问号引发思考");
        contentImitateForm.setParagraphArrangement("采用金字塔结构，从概念介绍到具体应用再到未来展望");
        contentImitateForm.setProminentFocus("关键技术点使用加粗处理，重要数据使用列表展示");
        contentImitateForm.setTailDesign("以开放性问题结尾，鼓励读者思考与讨论");
        contentImitateForm.setHashtagStrategy("使用#元宇宙 #数字经济 #虚拟现实 等主流话题标签");

// 设置目标受众和负面约束
        contentImitateForm.setTargetPortrait("25-40岁，对新兴科技感兴趣，有基本技术背景的都市年轻人");
        contentImitateForm.setNegativeConstraint("避免过度技术术语堆砌，不使用过于夸张的未来预测，避免营销式表达");

//        System.out.println("ContentImitateForm对象创建成功");

        final String userPrompt = convertToString(contentImitateForm);
        System.out.println(userPrompt);


    }
}
