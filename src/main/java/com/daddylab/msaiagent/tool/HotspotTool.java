package com.daddylab.msaiagent.tool;

import com.daddylab.msaiagent.db.aiAgent.dao.RedbookHotspotDao;
import com.daddylab.msaiagent.service.RedbookHotspotService;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 热点工具
 */
@Service
public class HotspotTool {
    @Autowired
    private RedbookHotspotService redbookHotspotService;

    @Tool(description = "获取今天日期")
    String getCurrentDateTime() {
        return LocalDateTime.now().atZone(LocaleContextHolder.getTimeZone().toZoneId()).toString();
    }

    @Tool(description = "获取小红书热点")
    String getRedbookHospot(@ToolParam(description = "天数", required = false) Integer days) {
        if (days == null || days <= 0) {
            days = 5; // 默认5天
        }
        System.out.println("使用了小红书热点工具，天数为：" + days);
        String rank1 = redbookHotspotService.getRank1(days);
        String rank2 = redbookHotspotService.getRank2(days);
        String rank3 = redbookHotspotService.getRank3(days);
        String rank4 = redbookHotspotService.getRank4(days);
        String res = "行业热度榜: " + rank1 + "\n" +
                "热词飙升榜: " + rank2 + "\n" +
                "热词总量榜: " + rank3 + "\n" +
                "新榜热点指数: " + rank4;
        System.out.println(res);
        return res;
    }

}
