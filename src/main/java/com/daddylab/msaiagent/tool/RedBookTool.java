package com.daddylab.msaiagent.tool;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.msaiagent.db.aiAgent.entity.RedBook;
import com.daddylab.msaiagent.domain.vo.RedBookInfoVO;
import com.daddylab.msaiagent.service.RedBookService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.filter.Filter;
import org.springframework.ai.vectorstore.filter.FilterExpressionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 获取用户的小红书笔记的工具
 *
 * @className RedBookVirtualTool
 * <AUTHOR>
 * @date 2025/5/8 10:23
 * @description: TODO
 */
@Slf4j
@Component
public class RedBookTool {

    @Autowired private RedBookService redBookService;
    @Autowired private VectorStore vectorStore;

    @Tool(name = "crawlerAccountNote", description = "获取小红书的用户主页的全部笔记")
    public String crawlerAccountNote(@ToolParam(description = "用户主页链接") String url) {
        redBookService.runCrawlAccountList(url);
        return "我已经正在获取了,请稍后。。。";
    }

    @Tool(name = "analyzeAccountStyle", description = "分析某个用户小红书账号的N条笔记内容")
    public String analyzeAccountStyle(
            @ToolParam(description = "小红书账号") String accountId,
            @ToolParam(description = "笔记条数") Long size) {
        List<RedBook> redBookList = redBookService.getAccount(accountId, size);
        if (CollUtil.isEmpty(redBookList)) {
            return "暂时找不到该账号的数据";
        }
        log.info("[分析小红书笔记] accountId={}, size={}", accountId, size);
        return redBookList.stream()
                .map(
                        redBook -> {
                            return StrUtil.format(
                                    "标题：{} || 内容：{} \n\n",
                                    redBook.getTitle(),
                                    redBook.getContent());
                        })
                .collect(Collectors.joining(StrUtil.CRLF));
    }

    @Tool(name = "searchRedBookNotes", description = "根据关键字搜索小红书指定账号的若干条笔记")
    public String search(
            @ToolParam(description = "小红书账号") String accountId,
            @ToolParam(description = "笔记条数") Long size,
            @ToolParam(description = "搜索关键词") String query) {
        List<Document> documents =
                vectorStore.similaritySearch(
                        SearchRequest.builder()
                                .query(query)
                                .similarityThreshold(0.7)
                                .topK(size.intValue())
                                .filterExpression(
                                        new FilterExpressionBuilder()
                                                .eq("accountId", accountId)
                                                .build())
                                .build());
        if (documents == null || documents.isEmpty()) {
            return "没有找到相关数据";
        }
        return documents.stream()
                .map(
                        document -> {
                            return StrUtil.format(
                                    "标题：{} || 内容：{} \n\n",
                                    document.getMetadata().get("title"),
                                    document.getText());
                        })
                .collect(Collectors.joining(StrUtil.CRLF));
    }

    @Tool(name = "noteDetail", description = "获取小红书笔记详情页的内容")
    public RedBookInfoVO noteDetail(@ToolParam(description = "笔记详情页") String url) {
        return redBookService.getUrlInfo(url);
    }
}
