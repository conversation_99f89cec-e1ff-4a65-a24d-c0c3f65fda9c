package com.daddylab.msaiagent.tool;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.daddylab.msaiagent.common.utils.JsonUtil;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;

public class TimeTool {
    @Tool(description = "获取当前时间戳（秒级别）")
    public Long getCurrentTimestamp() {
        return DateUtil.currentSeconds();
    }

    @Tool(description = "获取当前时间戳（毫秒级别）")
    public Long getCurrentTimestampMs() {
        return DateUtil.current();
    }

    @Tool(description = "获取时间格式化可用格式列表")
    public String getAvailableFormatList() {
        return JsonUtil.toJSONPrettyString(
                Arrays.asList(DatePattern.NORM_DATE_PATTERN, DatePattern.NORM_DATETIME_PATTERN));
    }

    @Tool(description = "格式化时间戳")
    public String format(
            @ToolParam(description = "时间戳（秒级）") Long epochSecond,
            @ToolParam(description = "时间格式") String format) {
        return DateUtil.format(
                LocalDateTime.ofInstant(Instant.ofEpochSecond(epochSecond), ZoneId.systemDefault()),
                format);
    }
}
