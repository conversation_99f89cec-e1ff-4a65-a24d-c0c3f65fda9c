package com.daddylab.msaiagent.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class JsonExtractor {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 检测字符串是否为JSON格式，如果是返回整个JSON，如果不是但包含JSON则提取JSON部分
     * @param aiResponse AI返回的响应文本
     * @return 提取的JSON字符串或原始文本
     */
    public static String extractJson(String aiResponse) {
        if (aiResponse == null || aiResponse.trim().isEmpty()) {
            return "";
        }

        // 首先检查整个字符串是否为有效的JSON
        if (isValidJson(aiResponse)) {
            return aiResponse; // 返回原始的完整JSON
        }
        
        // 检查是否包含Markdown代码块形式的JSON (```json ... ```)
        String markdownJson = extractMarkdownJson(aiResponse);
        if (markdownJson != null && !markdownJson.isEmpty()) {
            return markdownJson;
        }

        // 尝试在文本中查找JSON对象 ({...})
        String jsonObject = findJsonPattern(aiResponse, "\\{[^\\{\\}]*(\\{[^\\{\\}]*\\})*[^\\{\\}]*\\}");
        if (jsonObject != null) {
            return jsonObject;
        }

        // 尝试在文本中查找JSON数组 ([...])
        String jsonArray = findJsonPattern(aiResponse, "\\[[^\\[\\]]*\\]");
        if (jsonArray != null) {
            return jsonArray;
        }

        return "";
    }
    
    /**
     * 从Markdown代码块格式中提取JSON内容
     */
    private static String extractMarkdownJson(String text) {
        // 匹配```json开头和```结尾之间的内容
        Pattern pattern = Pattern.compile("```json\\s*\\n(.*?)\\n\\s*```", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(text);
        
        if (matcher.find()) {
            String potentialJson = matcher.group(1);
            if (isValidJson(potentialJson)) {
                return potentialJson;
            }
        }
        
        return null;
    }

    /**
     * 检查字符串是否为有效的JSON
     */
    private static boolean isValidJson(String json) {
        try {
            objectMapper.readTree(json);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 使用正则表达式在文本中查找匹配的JSON模式
     */
    private static String findJsonPattern(String text, String regex) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(text);

        while (matcher.find()) {
            String potentialJson = matcher.group();
            if (isValidJson(potentialJson)) {
                return potentialJson;
            }
        }

        return null;
    }
}
