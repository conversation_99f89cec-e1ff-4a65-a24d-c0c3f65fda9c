package com.daddylab.msaiagent.util;

import com.daddylab.msaiagent.common.utils.JsonUtil;
import org.springframework.ai.util.json.schema.JsonSchemaGenerator;

import java.util.Map;

public class JsonSchemaUtils {
    public static String toJSONStringWithSchema(Object obj) {
        String schema = JsonSchemaGenerator.generateForType(obj.getClass());
        String data = JsonUtil.toJSONString(obj);
        return JsonUtil.toJSONString(Map.of("schema", schema, "data", data));
    }

    public static String generateSchema(Class<?> clazz) {
        return JsonSchemaGenerator.generateForType(clazz);
    }
}
