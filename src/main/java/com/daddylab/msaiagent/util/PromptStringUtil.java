package com.daddylab.msaiagent.util;

import java.util.regex.Pattern;

public class PromptStringUtil {
    public static String removeMarkedContent(String input) {
        // 使用正则表达式匹配<-remove-by-history>与</-remove-by-history-end>之间的所有内容（包括这两个标记）
        // Pattern.DOTALL 确保 . 可以匹配包括换行符在内的任意字符
        return Pattern.compile("<-remove-by-history>.*?</-remove-by-history-end>",
                        Pattern.DOTALL)
                .matcher(input)
                .replaceAll("");
    }
}
