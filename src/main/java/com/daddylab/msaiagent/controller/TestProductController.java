package com.daddylab.msaiagent.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.daddylab.msaiagent.domain.form.ProductAiGeneratedForm;
import com.daddylab.msaiagent.domain.vo.AiJobVO;
import com.daddylab.msaiagent.domain.vo.ApiResponse;
import com.daddylab.msaiagent.domain.vo.ProductInfoVO;
import com.daddylab.msaiagent.service.material.ProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/test/product")
public class TestProductController {

    @Autowired
    private ProductService productService;

    @GetMapping("/get")
    public ApiResponse<ProductInfoVO> getProduct(@RequestParam("productId") Long productId){
        return ApiResponse.success(productService.getProductDetail(productId));
    }

    @GetMapping("/list")
    public ApiResponse<IPage<ProductInfoVO>> getProductList(@RequestParam("page") int page, @RequestParam("size") int size){
        return ApiResponse.success(productService.getPersonList(page,size));
    }

    @PostMapping("/generator/create")
    public ApiResponse<AiJobVO> generator(@RequestBody ProductAiGeneratedForm form) {
        return ApiResponse.success(productService.generateByAi(form));
    }

    @GetMapping("/generator/parse")
    public ApiResponse<Void> parse(@RequestParam("productId") Long productId) {
        productService.parseDetailByChatRecord(productId);
        return ApiResponse.success();
    }
}
