package com.daddylab.msaiagent.controller.config;

import com.daddylab.msaiagent.common.interceptor.UserInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.concurrent.ThreadPoolExecutor;

/** spring mvc config */
@Configuration
public class WebMvcConfigurerConfig implements WebMvcConfigurer {

  @Override
  public void addInterceptors(InterceptorRegistry registry) {
    String[] excludePathPatterns = {
      "/favicon.ico",
      "/error",
      "/swagger-ui.html",
      "/swagger-resources/**",
      "/open/**",
      "/inner/**"
    };

    // 登录拦截器
    registry
        .addInterceptor(userInterceptor())
        .addPathPatterns("/**")
        .excludePathPatterns(excludePathPatterns);


    WebMvcConfigurer.super.addInterceptors(registry);
  }

  @Bean
  public UserInterceptor userInterceptor() {
    return new UserInterceptor();
  }
  @Override
  public void addCorsMappings(CorsRegistry registry) {
    registry
        .addMapping("/**")
        .allowedOriginPatterns("*")
        .allowedMethods("GET", "HEAD", "POST", "PUT", "DELETE", "OPTIONS")
        .allowCredentials(true)
        .maxAge(3600)
        .allowedHeaders("*");
  }

  @Override
  public void configureAsyncSupport(AsyncSupportConfigurer configurer) {
    configurer.setTaskExecutor(webMvcTaskExecutor());
  }

  /** 异步线程池 */
  public ThreadPoolTaskExecutor webMvcTaskExecutor() {
    ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
    threadPoolTaskExecutor.setCorePoolSize(16);
    threadPoolTaskExecutor.setMaxPoolSize(32);
    threadPoolTaskExecutor.setKeepAliveSeconds(60);
    threadPoolTaskExecutor.setQueueCapacity(128);
    threadPoolTaskExecutor.setThreadNamePrefix("mvc-Task-Thread-Pool");
    threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true);
    threadPoolTaskExecutor.setAwaitTerminationSeconds(60);
    threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
    threadPoolTaskExecutor.initialize();
    return threadPoolTaskExecutor;
  }
}
