package com.daddylab.msaiagent.controller;

import com.alibaba.cola.dto.PageResponse;
import com.daddylab.msaiagent.db.aiAgent.entity.KnowledgeBaseMaterial;
import com.daddylab.msaiagent.domain.form.*;
import com.daddylab.msaiagent.domain.query.KnowledgeBaseMaterialQuery;
import com.daddylab.msaiagent.domain.vo.KnowledgeBaseMaterialPageVO;
import com.daddylab.msaiagent.domain.vo.KnowledgeBaseMaterialVO;
import com.daddylab.msaiagent.service.material.KnowledgeBaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/knowledgeBaseMaterial")
@Api("知识库素材管理")
public class KnowledgeBaseMaterialController {
    @Autowired
    private KnowledgeBaseService knowledgeBaseService;

    @ApiOperation("列表查询")
    @PostMapping("/page")
    public PageResponse<KnowledgeBaseMaterialPageVO> pageQuery(@RequestBody KnowledgeBaseMaterialQuery query) {
        return knowledgeBaseService.pageQuery(query);
    }

    @ApiOperation(value = "创建")
    @PostMapping("/add")
    public String add(@Valid @RequestBody KnowledgeBaseMaterialAddForm form) {
        return knowledgeBaseService.add(form);
    }

    @ApiOperation(value = "编辑")
    @PostMapping("/edit")
    public Boolean edit(@Valid @RequestBody KnowledgeBaseMaterialEditForm form) {
        return knowledgeBaseService.edit(form);
    }

    @ApiOperation(value = "修正")
    @PostMapping("/fix")
    public Boolean fix(@Valid @RequestBody KnowledgeBaseMaterialFixForm form) {
        return knowledgeBaseService.fix(form);
    }

    @ApiOperation("详情")
    @GetMapping("/detail")
    public KnowledgeBaseMaterialVO detail(@RequestParam("uuid") String uuid) {
        return knowledgeBaseService.detail(uuid);
    }

    @ApiOperation("启用/禁用")
    @PostMapping("/enable")
    public Boolean enable(@Valid @RequestBody KnowledgeBaseMaterialEnableForm form) {
        return knowledgeBaseService.enable(form.getMaterialUuid(), form.getIsEnabled());
    }
}
