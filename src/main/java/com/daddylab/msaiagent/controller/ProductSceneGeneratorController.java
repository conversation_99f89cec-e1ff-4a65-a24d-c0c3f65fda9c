package com.daddylab.msaiagent.controller;

import com.daddylab.msaiagent.common.base.response.Result;
import com.daddylab.msaiagent.db.aiAgent.entity.SceneFragment;
import com.daddylab.msaiagent.domain.request.ProductSceneGeneratorRequest;
import com.daddylab.msaiagent.service.ProductSceneGeneratorService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/scene/agent/productSceneGenerator/")
@Api(tags = "商品场景生成器")
public class ProductSceneGeneratorController {
    @Autowired private ProductSceneGeneratorService productSceneGeneratorService;

    @RequestMapping("/generate")
    public Result<SceneFragment> generate(
            @Validated @RequestBody ProductSceneGeneratorRequest request) {
        SceneFragment sceneFragment = productSceneGeneratorService.generate(request);
        return Result.success(sceneFragment);
    }

    @RequestMapping("/applyChat")
    public Result<SceneFragment> applyChat(@RequestParam Long sceneId, @RequestParam Long chatId) {
        SceneFragment sceneFragment = productSceneGeneratorService.applyChat(sceneId, chatId);
        return Result.success(sceneFragment);
    }
}
