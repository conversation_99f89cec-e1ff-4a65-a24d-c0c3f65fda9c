package com.daddylab.msaiagent.controller;

import com.daddylab.msaiagent.db.aiAgent.dao.PersonDetailSnapshotDao;
import com.daddylab.msaiagent.domain.vo.ApiResponse;
import com.daddylab.msaiagent.service.person.PersonGeneratorService;
import com.daddylab.msaiagent.service.person.PersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> up
 * @date 2025年05月29日 10:09 AM
 */
@RestController
@RequestMapping("/personGrowUp")
public class VirtualPersonGrowthController {

    @Autowired
    private PersonDetailSnapshotDao personDetailSnapshotDao;



}
