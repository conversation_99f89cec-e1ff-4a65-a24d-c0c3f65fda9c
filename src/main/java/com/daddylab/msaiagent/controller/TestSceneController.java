package com.daddylab.msaiagent.controller;

import com.daddylab.msaiagent.domain.form.SceneFragmentGeneratorForm;
import com.daddylab.msaiagent.domain.vo.AiJobVO;
import com.daddylab.msaiagent.domain.vo.ApiResponse;
import com.daddylab.msaiagent.domain.vo.SceneGeneratorStartVO;
import com.daddylab.msaiagent.service.scene.SceneFragmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/test/scene")
public class TestSceneController {

    @Autowired
    private SceneFragmentService sceneFragmentService;

    @PostMapping("/generator")
    public ApiResponse<AiJobVO> generator(@RequestBody SceneFragmentGeneratorForm form) {
        return ApiResponse.success(sceneFragmentService.generateByAi(form));
    }

    @GetMapping("/generator/start")
    public ApiResponse<SceneGeneratorStartVO> getGenerator(){
        return ApiResponse.success(new SceneGeneratorStartVO());
    }

}
