package com.daddylab.msaiagent.controller;

import com.daddylab.msaiagent.db.aiAgent.entity.ModelChat;
import com.daddylab.msaiagent.domain.vo.ApiResponse;
import com.daddylab.msaiagent.service.ModelChatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/test/ai")
@Slf4j
public class AiTestController {
    @Autowired
    private ModelChatService modelChatService;

    @GetMapping("/run")
    public ApiResponse<Void> run(@RequestParam("prompt") String promptStr){
        ModelChat chat = modelChatService.createChat("google/gemini-2.5-pro-preview", "test");
        modelChatService.startChatAsync(chat,promptStr, log::info);
        return ApiResponse.success();
    }
}
