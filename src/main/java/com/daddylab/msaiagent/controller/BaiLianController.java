package com.daddylab.msaiagent.controller;

import com.daddylab.msaiagent.common.bailian.domain.vo.RetrieveVO;
import com.daddylab.msaiagent.common.base.response.Result;
import com.daddylab.msaiagent.domain.form.BailianFileUploadForm;
import com.daddylab.msaiagent.domain.form.BailianRetrieveForm;
import com.daddylab.msaiagent.service.bailian.BaiLianFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 *
 * @className BailianController
 * <AUTHOR>
 * @date 2025/5/26 16:09
 * @description: TODO 
 */
@Api("百炼接口管理")
@RestController
@RequestMapping("/bailian")
public class BaiLianController {

    @Autowired
    private BaiLianFileService baiLianFileService;

    @ApiOperation("文件上传")
    @PostMapping("/upload")
    public Result<Boolean> upload(@RequestBody BailianFileUploadForm bailianFileUploadForm) {
        baiLianFileService.batchUploadFiles(bailianFileUploadForm.getFileType(), bailianFileUploadForm.getOssUrlList());
        return Result.success(true);
    }

    @ApiOperation("知识库检索")
    @PostMapping("/retrieve")
    public Result<List<RetrieveVO>> retrieve(@RequestBody BailianRetrieveForm bailianRetrieveForm) {
        return Result.success(baiLianFileService.retrieve(bailianRetrieveForm));
    }
}
