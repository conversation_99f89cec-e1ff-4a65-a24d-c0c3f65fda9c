package com.daddylab.msaiagent.controller.open;

import com.daddylab.msaiagent.common.base.response.Result;
import com.daddylab.msaiagent.domain.form.OpenRedBookInfoForm;
import com.daddylab.msaiagent.domain.vo.RedBookInfoVO;
import com.daddylab.msaiagent.service.RedBookService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 *
 * @className RedBookController
 * <AUTHOR>
 * @date 2025/5/26 16:25
 * @description: TODO 
 */
@Api(tags = "小红书接口管理")
@RequestMapping("/open/redbook")
@RestController
public class RedBookController {

    @Autowired
    private RedBookService redBookService;

    @PostMapping("/info")
    public Result<RedBookInfoVO> info(@RequestBody OpenRedBookInfoForm openRedBookInfoForm) {
        return Result.success(redBookService.getUrlInfo(openRedBookInfoForm.getUrl()));
    }
}
