package com.daddylab.msaiagent.controller;

import com.daddylab.msaiagent.agent.OptimizeAgent;
import com.daddylab.msaiagent.db.aiAgent.entity.Note;
import com.daddylab.msaiagent.db.aiAgent.entity.VirtualPerson;
import com.daddylab.msaiagent.manage.AiClientManage;
import com.daddylab.msaiagent.domain.enums.AIModelEnum;
import com.daddylab.msaiagent.service.NoteService;
import com.daddylab.msaiagent.service.VirtualPersonService;
import com.daddylab.msaiagent.tool.HotspotTool;
import com.daddylab.msaiagent.tool.RedBookTool;
import lombok.Data;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.model.tool.DefaultToolCallingManager;
import org.springframework.ai.model.tool.ToolCallingChatOptions;
import org.springframework.ai.model.tool.ToolCallingManager;
import org.springframework.ai.model.tool.ToolExecutionResult;
import org.springframework.ai.tool.ToolCallbacks;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 *
 * @className TestController
 * <AUTHOR>
 * @date 2025/4/28 10:45
 * @description: TODO 
 */
@RestController
@RequestMapping("/test")
public class TestController {

    @Autowired
    private RedBookTool redBookTool;
    @Autowired
    private OptimizeAgent optimizeAgent;
    @Autowired
    private VirtualPersonService virtualPersonService;
    @Autowired
    private HotspotTool hotspotTool;
    @Autowired
    private NoteService noteService;


    @PostMapping("/test1")
    public String test1(@RequestBody RequestBodyTest1 requestBodyTest1) {
        ChatClient chatClient = AiClientManage.buildDefaultClient(requestBodyTest1.getAiModelEnum());
        chatClient.prompt().user(requestBodyTest1.getPrompt()).call().content();
        ChatClient.ChatClientRequestSpec clientRequestSpec = chatClient.prompt(requestBodyTest1.getPrompt());
        return clientRequestSpec.call().content();
    }

    @Data
    public static class RequestBodyTest1 {
        private String prompt;
        private AIModelEnum aiModelEnum;
    }

    @Data
    public static class RequestBodyTest2 {
        private String prompt;
        private AIModelEnum aiModelEnum;
    }

    @Data
    public static class ActorsFilms {
        private String name;
        private String actor;
    }

    @PostMapping("/test2")
    public List<ActorsFilms> test2(@RequestBody RequestBodyTest2 requestBodyTest2) {
        // ai输出结构化数据
        List<ActorsFilms> actorsFilms = ChatClient.create(AiClientManage.chatModel(requestBodyTest2.getAiModelEnum()))
                .prompt()
                .user(requestBodyTest2.getPrompt())
                .call()
                .entity(new ParameterizedTypeReference<List<ActorsFilms>>() {});
        return actorsFilms;
    }


    @Data
    public static class RequestBodyTest3 {
        private String prompt;
        private AIModelEnum aiModelEnum;
    }

    public static  class WeatherService {

    }

    public static class DateTimeTools {

        @Tool(name = "getCurrentDateTime", description = "获取用户所在时区中的当前日期和时间")
        String getCurrentDateTime() {
            return LocalDateTime.now().atZone(LocaleContextHolder.getTimeZone().toZoneId()).toString();
        }

    }

    @PostMapping("/test3")
    public String test3(@RequestBody RequestBodyTest3 requestBodyTest3) {
        // ai tool call

        // Auto call
//        String content = ChatClient.create(AiClientManage.chatModel(requestBodyTest3.getAiModelEnum()))
//                .prompt()
//                .tools(new DateTimeTools())
//                .user(requestBodyTest3.getPrompt())
//                .call().content();
        // Manual call
        ChatOptions chatOptions = ToolCallingChatOptions.builder()
                .toolCallbacks(ToolCallbacks.from(new DateTimeTools(), redBookTool))
                .internalToolExecutionEnabled(false)
                .build();
        ToolCallingManager toolCallingManager = DefaultToolCallingManager.builder().build();
        Prompt prompt = new Prompt(requestBodyTest3.getPrompt(), chatOptions);
        ChatModel chatModel = AiClientManage.chatModel(requestBodyTest3.getAiModelEnum());
        ChatResponse chatResponse = chatModel.call(prompt);
        while (chatResponse.hasToolCalls()) {
            ToolExecutionResult toolExecutionResult = toolCallingManager.executeToolCalls(prompt, chatResponse);
            prompt = new Prompt(toolExecutionResult.conversationHistory(), chatOptions);
            chatResponse = chatModel.call(prompt);
        }
        return chatResponse.getResult().getOutput().getText();
    }

    @PostMapping("/test4")
    public String test4(@RequestBody RequestBodyTest3 requestBodyTest3) {
        return optimizeAgent.dispose(requestBodyTest3.getAiModelEnum(), requestBodyTest3.getPrompt());
    }

    @Data
    public static class RequestBodyTest5 {
        private String prompt;
    }

    @PostMapping("/test5")
    public VirtualPerson test5(@RequestBody RequestBodyTest5 requestBodyTest5) {
        return virtualPersonService.imitationBuildVirtualPerson(requestBodyTest5.getPrompt());
    }

    @PostMapping("/test6")
    public String test6(@RequestBody RequestBodyTest3 requestBodyTest3) {
        String content = ChatClient.create(AiClientManage.chatModel(requestBodyTest3.getAiModelEnum()))
                .prompt()
                .options(ToolCallingChatOptions.builder()
                        .toolCallbacks(ToolCallbacks.from(hotspotTool))
                        .build())
                .user(requestBodyTest3.getPrompt())
                .call().content();
        return content;
    }
    @Data
    public static class RequestBodyTest7 {
        private String title;
        private String content;
    }
    @PostMapping("/test7")
    public String test7(@RequestBody RequestBodyTest7 test7) {
        Note note = new Note();
        note.setTitle(test7.title);
        note.setContent(test7.content);
        noteService.save(note);
        String tag = noteService.buildNoteTag(note);
        return tag;
    }
}
