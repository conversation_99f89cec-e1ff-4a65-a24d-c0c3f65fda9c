package com.daddylab.msaiagent.controller;

import com.daddylab.msaiagent.common.base.response.Result;
import com.daddylab.msaiagent.domain.vo.OssSignCommand;
import com.daddylab.msaiagent.domain.vo.OssSignURLCommand;
import com.daddylab.msaiagent.domain.vo.OssSignURLResult;
import com.daddylab.msaiagent.domain.vo.StsTokenResult;
import com.daddylab.msaiagent.service.OssService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/9/6
 */
@Api(tags = "阿里云OSS")
@RequestMapping("/oss")
@RestController
@RequiredArgsConstructor
public class OssController {
  private final OssService ossService;

  @PostMapping(value = "/sign")
  @ApiOperation("签名")
  public Result<List<String>> sign(@ApiParam("签名参数") @RequestBody List<OssSignCommand> commands) {
    return Result.success(ossService.sign(commands));
  }

  @PostMapping(value = "/signURL")
  @ApiOperation("对OSS对象访问链接签名")
  public Result<List<OssSignURLResult>> signUrl(
      @ApiParam("签名参数") @RequestBody List<OssSignURLCommand> commands) {
    return Result.success(ossService.signURL(commands));
  }

  @PostMapping(value = "/stsToken")
  @ApiOperation("获取STS Token")
  public Result<StsTokenResult> stsToken() {
    return Result.success(ossService.getStsToken());
  }
}
