package com.daddylab.msaiagent.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.daddylab.msaiagent.db.aiAgent.entity.PersonaAttributeDefinition;
import com.daddylab.msaiagent.domain.enums.PersonSubjectTypeEnum;
import com.daddylab.msaiagent.domain.form.PersonAiGeneratedForm;
import com.daddylab.msaiagent.domain.form.PersonUpdateByAiForm;
import com.daddylab.msaiagent.domain.form.PersonDetailUpdateForm;
import com.daddylab.msaiagent.domain.vo.*;
import com.daddylab.msaiagent.service.person.PersonConstructService;
import com.daddylab.msaiagent.service.person.PersonGeneratorService;
import com.daddylab.msaiagent.service.person.PersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/test/person")
public class TestPersonController {

    @Autowired
    private PersonGeneratorService personGeneratorService;

    @Autowired
    private PersonService personService;

    @Autowired
    private PersonConstructService personConstructService;

    @GetMapping("/get")
    public ApiResponse<PersonInfoVO> getPerson(@RequestParam("uid") Long uid){
        return ApiResponse.success(personService.getPersonInfo(uid));
    }

    @GetMapping("/list")
    public ApiResponse<IPage<PersonBriefInfoVO>> getPersonList(@RequestParam("page") int page, @RequestParam("size") int size){
        return ApiResponse.success(personService.getPersonList(page,size));
    }

    @GetMapping("/generator/construct")
    public ApiResponse<Map<String, PersonaAttributeDefinition>> getConstruct(){
        return ApiResponse.success(personConstructService.constructPerson());
    }

    @GetMapping("/generator/start")
    public ApiResponse<PersonGeneratorStartVO> getGenerator(@RequestParam("subjectType") PersonSubjectTypeEnum subjectType){
        return ApiResponse.success(new PersonGeneratorStartVO(subjectType));
    }

    @GetMapping("/generator/parse")
    public ApiResponse<Void> parse(@RequestParam("uid") Long uid) {
        personGeneratorService.parseDetailByChatRecord(uid);
        return ApiResponse.success();
    }

    @PostMapping("/generator/create")
    public ApiResponse<AiJobVO> generator(@RequestBody PersonAiGeneratedForm form) {
        return ApiResponse.success(personGeneratorService.generatePersonByAi(form));
    }

    @PostMapping("/generator/guide")
    public ApiResponse<AiJobVO> generatorQa(@RequestBody PersonUpdateByAiForm form) {
        return ApiResponse.success(personGeneratorService.guidingQuestionAndUpdatePerson(form));
    }

    @PostMapping("/generator/update")
    public ApiResponse<Void> update(@RequestBody PersonDetailUpdateForm form) {
        personService.updatePerson(form.getUid(),form.getPersonaAttributes());
        return ApiResponse.success();
    }
}
