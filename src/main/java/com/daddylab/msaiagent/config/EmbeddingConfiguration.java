package com.daddylab.msaiagent.config;

import com.alibaba.cloud.ai.dashscope.embedding.DashScopeEmbeddingModel;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class EmbeddingConfiguration {

    @Primary
    @Bean(name = "defaultEmbeddingModel")
    public EmbeddingModel embeddingModel(DashScopeEmbeddingModel dashScopeEmbeddingModel) {
        return dashScopeEmbeddingModel;
    }
}
