package com.daddylab.msaiagent.config;

import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@ConfigurationProperties(prefix = "llm")
@Data
@Slf4j
public class LlmConfig {

    private Map<String, LLMConfigDto> configs = new HashMap<>();

    @Data
    public static class LLMConfigDto{
        private String modelName;
        private String apiKey;
        private String baseUrl;
        private String completionsPath = "/v1/chat/completions";
    }




}
