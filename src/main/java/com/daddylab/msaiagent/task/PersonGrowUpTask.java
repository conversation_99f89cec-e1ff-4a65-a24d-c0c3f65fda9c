package com.daddylab.msaiagent.task;

import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.job.extend.autoRegister.XxlJobAutoRegister;
import com.daddylab.msaiagent.agent.VirtualPersonGrowthAgent;
import com.daddylab.msaiagent.db.aiAgent.dao.PersonaDetailDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> up
 * @date 2025年05月29日 10:15 AM
 */
@Component
@Slf4j
public class PersonGrowUpTask {

  @Autowired VirtualPersonGrowthAgent virtualPersonGrowthAgent;
  @Autowired PersonaDetailDao personaDetailDao;

  @XxlJobAutoRegister(cron = "0 0 2 1 * ?", author = "seven up", jobDesc = "人物成长")
  @XxlJob("PersonGrowUpTask-growUp")
  public void growUp() {
    personaDetailDao
        .lambdaQuery()
        .list()
        .forEach(
            val -> {
              try {
                virtualPersonGrowthAgent.personGrowUp(val.getPersonaId());
                TimeUnit.SECONDS.sleep(5);
              } catch (Exception e) {
                log.error("VirtualPersonGrowthAgent fail.personId:{}", val.getId());
              }
            });
  }
}
