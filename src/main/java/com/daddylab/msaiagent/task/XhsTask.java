package com.daddylab.msaiagent.task;

import com.daddylab.job.core.context.XxlJobHelper;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.job.extend.autoRegister.XxlJobAutoRegister;
import com.daddylab.msaiagent.service.RedBookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *
 * @className XhsTask
 * <AUTHOR>
 * @date 2025/4/29 15:34
 * @description: TODO 
 */
@Component
public class XhsTask {

    @Autowired
    private RedBookService redBookService;

    @XxlJob("XhsTask-freshCookie")
    @XxlJobAutoRegister(cron = "0 0 * * * ?", author = "jinbiao.shen", jobDesc = "刷新小红书cookie")
    public void freshCookie() {
        redBookService.freshCookie();
    }


    @XxlJob("XhsTask-crawlerList")
    @XxlJobAutoRegister(cron = "0 */5 * * * ?", author = "jinbiao.shen", jobDesc = "爬取小红书账号列表")
    public void crawlerList() {
        redBookService.runCrawlAccountList(XxlJobHelper.getJobParam());
    }

    @XxlJob("XhsTask-crawlerDetail")
    @XxlJobAutoRegister(cron = "0 */5 * * * ?", author = "jinbiao.shen", jobDesc = "爬取小红书账号详情")
    public void crawlerDetail() {
        redBookService.runCrawlDetail();
    }





    @XxlJob("XhsTask-freshDetail")
    @XxlJobAutoRegister(cron = "0 0 7 * * ?", author = "jinbiao.shen", jobDesc = "刷新详情数据")
    public void freshDetail() {
        redBookService.freshCrawlDetail(2);
    }
}
