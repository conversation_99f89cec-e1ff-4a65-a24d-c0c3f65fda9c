spring:
  ai:
    azure:
      openai:
        chat:
          options:
            model: gpt-4.1
        endpoint: https://daddygpt.openai.azure.com/
        api-key: ********************************
    dashscope:
      api-key: sk-********************************
      workspace-id: llm-nhvgf0rt7lcbxu4c
      embedding:
        options:
          model: text-embedding-v3
          dimensions: 1024
      image:
        options:
          model: qwen-vl-max
      chat:
        options:
          model: qwen-max
      read-timeout: 120
    vectorstore:
      qdrant:
        host: ************
        port: 30334
        collection-name: ai-agent-documents
        initialize-schema: false
    vertex:
      ai:
        gemini:
          projectId: 781932015831
          apiEndpoint: https://generativelanguage.googleapis.com
          location: cn
          transport: GRPC
          chat:
            options:
              model: gemini-2.0-pro




  main:
    allow-circular-references: true
    lazy-initialization: true
  profiles:
    active: @profileActive@
  application:
    name: ms-ai-agent
  config:
    import:
      - optional:nacos:ms-ai-agent.yaml
  cloud:
    nacos:
      config:
        enabled: true
        server-addr: '*************:8848,*************:8848,*************:8848'
        username: 'nacos'
        password: '82CVXbpDsTabVS'
        namespace: ${spring.profiles.active}
        group: MS-AI-AGENT
        max-retry: 3
        config-retry-time: 3000
        refresh-enabled: true
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
server:
  servlet:
    context-path: /ai-agent
    encoding:
      charset: UTF-8
      force-response: true
      force-request: false
  port: 8080
management:
  server:
    port: 9902
  health:
    elasticsearch:
      enabled: false
    mail:
      enabled: false
  endpoint:
    health:
      show-details: always
  endpoints:
    web:
      base-path: /
      path-mapping:
        health: k8s_readiness
        info: k8s_liveiness
        prometheus: metrics
      exposure:
        include: '*'
        exclude: metrics
  metrics:
    tags:
      application: @project.artifactId@

logging:
  config: classpath:logback-spring.xml
  level:
    root: info
    org:
      springframework:
        ai: trace
