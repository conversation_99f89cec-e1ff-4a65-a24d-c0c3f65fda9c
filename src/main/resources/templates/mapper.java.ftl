package ${package.Mapper};

import ${package.Entity}.${entity};
import ${superMapperClassPackage};
<#if dsIndex?exists && dsIndex != 0>
import com.baomidou.dynamic.datasource.annotation.DS;
</#if>

<#if mapperAnnotationClass??>
    import ${mapperAnnotationClass.name};
</#if>


/**
* <p>
* ${table.comment!} Mapper 接口
* </p>
*
* <AUTHOR>
* @since ${date}
*/
<#if dsIndex?exists && dsIndex != 0>
@DS("${dsValue}")
</#if>
<#if mapperAnnotationClass??>
@${mapperAnnotationClass.simpleName}
</#if>
<#if kotlin>
interface ${table.mapperName} : ${superMapperClass}<${entity}>
<#else>
public interface ${table.mapperName} extends ${superMapperClass}<${entity}> {

}
</#if>
