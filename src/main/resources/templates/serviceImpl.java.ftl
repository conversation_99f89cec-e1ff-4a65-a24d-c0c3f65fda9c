package ${package.ServiceImpl};

import ${package.Entity}.${entity};
import ${package.Mapper}.${table.mapperName};
<#if generateService>
import ${package.Service}.${table.serviceName};
</#if>
import ${superServiceImplClassPackage};
import org.springframework.stereotype.Service;

<#if dsIndex?exists && dsIndex != 0>
import com.baomidou.dynamic.datasource.annotation.DS;
</#if>

/**
 * <p>
 * ${table.comment!} 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
<#if dsIndex?exists && dsIndex != 0>
@DS("${dsValue}")
</#if>
@Service
<#if kotlin>
open class ${table.serviceImplName} : ${superServiceImplClass}<${table.mapperName}, ${entity}>()<#if generateService>, ${table.serviceName}</#if> {

}
<#else>
public class ${table.serviceImplName} extends ${superServiceImplClass}<${table.mapperName}, ${entity}><#if generateService> implements ${table.serviceName}</#if> {
  @Override
  public ${table.mapperName} getBaseMapper() {
    return super.getBaseMapper();
  }
}
</#if>
