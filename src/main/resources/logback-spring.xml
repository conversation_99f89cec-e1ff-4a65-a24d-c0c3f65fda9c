<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- https://github.com/spring-projects/spring-boot/blob/v1.4.2.RELEASE/spring-boot/src/main/resources/org/springframework/boot/logging/logback/defaults.xml -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

<!--    <conversionRule conversionWord="oEex"-->
<!--                    converterClass="com.daddylab.tech.common.logback.OneLineExtendedThrowableProxyConverter"/>-->
<!--    <conversionRule conversionWord="jEm"-->
<!--                    converterClass="com.daddylab.tech.common.logback.JsonEscapeMessageConverter"/>-->

    <springProperty scope="context" name="ACTIVE_PROFILE" source="spring.profiles.active" defaultValue="local"/>
    <property name="CONSOLE_LOG_PATTERN2" value="%clr(%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} [%X{tid}] %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>
    <property name="JSON_LOG_PATTERN"
              value='{"logTime":"%d{yyyy-MM-dd HH:mm:ss.SSS}","profile":"${ACTIVE_PROFILE}","thread":"%thread","codeLine":"%line","pid":"${PID:- }","level": "%-5level","className": "%logger{80}","tid":"%X{tid}","msg":"%jEm","e":"%oEex"}%n'/>
    <property name="FILE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{80} [%X{tid}] - %msg%n"/>

    <appender name="CONSOLE_APPENDER" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN2}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <appender name="CONSOLE_APPENDER_JSON" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${JSON_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <property name="LOGFILE.PATH" value="./data/ms-ai-agent/logs"/>
    <property name="LOGFILE.MAX_HISTORY" value="15"/>
    <appender name="FILE_APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOGFILE.PATH}/app.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOGFILE.PATH}/app-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>${LOGFILE.MAX_HISTORY}</maxHistory>
        </rollingPolicy>
        <append>true</append>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>
    <appender name="ASYNC_FILE_APPENDER" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="FILE_APPENDER"/>
    </appender>

    <springProfile name="!k8s">
        <root level="INFO">
            <appender-ref ref="CONSOLE_APPENDER"/>
            <appender-ref ref="ASYNC_FILE_APPENDER"/>
        </root>
    </springProfile>

    <springProfile name="k8s">
        <root level="INFO">
            <appender-ref ref="CONSOLE_APPENDER_JSON"/>
            <appender-ref ref="ASYNC_FILE_APPENDER"/>
        </root>
    </springProfile>

    <springProfile name="local">
<!--        <logger name="org.springframework.boot.autoconfigure" level="DEBUG"/>-->
        <logger name="org.springframework.boot.context.config" level="TRACE"/>
    </springProfile>

    <logger name="root" level="DEBUG"/>
    <logger name="com.daddylab" level="DEBUG"/>

</configuration>